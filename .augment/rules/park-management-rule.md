---
type: 'agent_requested'
description: 'applyTo all *.vue'
---

- **`src/views/`**: 存放视图组件，按功能模块组织。
  - 每个功能模块一个目录 (e.g., `src/views/system/dict/`)。
  - 模块内可包含:
    - `apis.js`
    - `components/` 子目录
    - 视图文件如 `list.vue`, `detail.vue`。

## 3. 文件和目录命名规范

- **目录**: 统一使用驼峰命名法 (e.g., `userManagement`, `systemConfig`)。
- **非 Vue 文件**: 根据情况使用驼峰命名法 (e.g., `usePageTable.js`) 或横杠连接命名法 (kebab-case, e.g., `api-client.js`)。
- **Vue 文件 (路由视图)**: 使用小驼峰命名法 (e.g., `list.vue`, `detail.vue` as in `src/views/system/dict/index.vue`)。
- **Vue 文件 (组件)**: **务必**使用大驼峰命名法 (PascalCase) (e.g., `EditDict.vue` in `src/views/system/dict/components/EditDict.vue`, `CommonImport.vue` in `src/components/CommonImport.vue`)。这与 ESLint 规则 `vue/multi-word-component-names` (当前配置为 `'off'`) 的最佳实践一致，即使规则关闭，也应遵循此命名。

## 4. `src/views/` 模块内部结构规范

功能模块应清晰分离，避免混合。例如，一个 "培训 (training)" 功能包含 "培训班 (class)" 和 "培训需求 (demand)"：

```
src/views/
└── training/
    ├── class/
    │   ├── apis.js
    │   ├── components/
    │   │   └── EditClassModal.vue
    │   ├── list.vue
    │   └── detail.vue
    └── demand/
        ├── apis.js
        ├── components/
        │   └── ViewDemand.vue
        ├── list.vue
        └── detail.vue
```

不应将多个子模块的 `apis.js` 或 `components/` 合并到父模块层面，以保持结构清晰和路由直观。

## 5. CSS 和样式规范

### 5.1.优先使用 Tailwind CSS

项目已集成 Tailwind CSS ，应优先使用其原子类进行样式开发，减少自定义 CSS。

### 5.2. 自定义 CSS 的场景

仅在以下情况编写自定义 CSS：

1.  修改 Ant Design Vue 组件的默认样式。
2.  若 Tailwind CSS 类名组合过长 (超过两行 `class` 属性)。
    ```html
    <!-- 示例：过长的 Tailwind 类 -->
    <div
      class="border border-primary border-solid px-[10px] transition-colors cursor-pointer bg-white text-primary hover:bg-primary hover:text-white ..."
    >
      ...
    </div>
    ```
    此时应封装为自定义样式。

### 5.3. Scoped Styles

在 Vue 组件中，**必须**使用 `<style scoped lang="less">` 限制样式作用域，防止全局污染。

### 5.4. 修改 Ant Design Vue 组件样式

若需修改 Ant Design Vue 组件样式且 `scoped` 无法满足需求：

- 可以不使用 `scoped`，但**必须**在外层包裹一个自定义类名进行限定，禁止直接修改全局 Ant Design Vue 样式。

  ```vue
  <style lang="less">
  /* 错误示范 */
  /* .ant-button { width: 100px; } */

  /* 正确示范 */
  .my-custom-scope .ant-button {
    width: 100px;
  }
  </style>
  ```

## 6. Vue 组件开发规范

### 6.1. `<script setup>` 内部代码组织顺序

- 以下是 `<script setup>` 内部代码的组织顺序，**请严格遵循**：

  - import 语句 (自定义组件, API, 工具函数, 第三方库)

    1. 第三方库
    2. 工具函数
    3. 公共 API
    4. 自定义 API
    5. 自定义组件
    6. 其他

  - defineProps()
  - defineEmits()
  - 引入 Hooks (useRouter(), useStore(), 自定义 Hooks)
  - 组件状态 (ref, reactive, computed etc.)
    1. 引用数据
    2. 响应式数据
    3. 计算属性
    4. searchParams
    5. searchList
    6. defaultColumns
    7. columns
    8. 其他
  - 各种业务逻辑函数
    1. open
    2. handleCancel
    3. handlePrevStep
    4. handleNextStep
    5. handleSwitchDetail
    6. handleAdd
    7. handleEdit
    8. handleDetail
    9. handleImport
    10. handleExport
    11. loadDetail
    12. saveData
    13. handleSubmit
    14. handleTemporaryStorage
    15. handleDelete
    16. handleBatchDelete
    17. handleSearch
    18. onTableChange
    19. 其他
  - 生命周期管理 (watch, watchEffect, onMounted, etc.)
  - defineExpose()

- 移除所有文档说明性质的注释（例如："1. import 语句"、"1.1 第三方库"、"2. defineProps()"等标题式注释）
- 由于使用了 prettier 插件，保存会自动格式化文件代码，代码生成时忽视缩进和代码格式问题

### 6.2. 组件大小

理论上，一个 Vue 组件的代码不应超过 500 行。若超出，应考虑拆分为更小的、可复用的子组件，除非是包含大量表单字段等特殊情况。

### 6.3. 自动导入核心 API

由于项目使用了 `unplugin-auto-import` , Vue, Vue Router, Pinia 的核心 API (如 `ref`, `computed`, `onMounted`, `useRouter`, `defineStore` 等) 会自动导入。**不应再手动 `import` 这些 API**。

### 6.4. `src/components/` 目录下的组件使用

此目录中的全局组件已通过 `unplugin-vue-components` 配置自动导入。在模板中直接使用`src/components/` 目录下的组件标签即可，无需在 `<script setup>` 中 `import`。

### 6.5. Props 传递和组件标签命名

- 在模板中给组件传递 props 时，属性名应使用 `kebab-case` (短横线连接)。
- 在模板中使用组件时，组件标签应使用 `kebab-case`。
  ```vue
  <template>
    <common-table :table-data="data" @row-click="handleRowClick"></common-table>
  </template>
  ```

### 6.6. 事件命名

- 组件自定义事件应使用 `camelCase` (小驼峰命名法)。
- 事件名称应简洁明了，避免使用 `on` 前缀。
  ```vue
  <template>
    <my-component @customEvent="handleCustomEvent"></my-component>
  </template>
  ```
- 事件处理函数应使用 `handle` 前缀，后接事件名称。
  ```vue
  <script setup>
  const handleCustomEvent = () => {
    // 处理自定义事件
  }
  </script>
  ```
- 所有函数都需要加上简洁的 JSDoc 风格注释，说明方法的功能

## 7. API 调用与异常捕获

项目已配置全局 API 接口异常捕获机制。因此，在组件的方法中调用 API 时，**无需手动添加 `try...catch` 块**进行异常处理，除非需要针对特定错误执行特殊的业务逻辑。

## 8. 图标引用规范

- 统一使用**阿里巴巴矢量图标库**。通过类名 `a-icon-xxx` 使用图标。
- **禁止**下载和引入 `@ant-design/icons-vue` 或其他第三方图标库依赖。

## 9. 其他注意事项

- 最终不要运行测试来验证功能（如npm run dev等）

---

请所有开发人员熟悉并严格遵守以上规范。
