<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}水电表`"
    class="edit-water-electricity-drawer common-drawer"
    width="1072px"
    placement="right"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-align="left"
        :label-col="{ style: { width: '100px' } }"
      >
        <a-form-item label="编码(表号)" name="number">
          <a-input v-model:value="formData.number" placeholder="请输入编码(表号)" />
        </a-form-item>
        <a-form-item label="名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入名称" show-count :maxlength="50" />
        </a-form-item>
        <a-form-item label="倍率" name="doubleRate">
          <a-input-number
            v-model:value="formData.doubleRate"
            placeholder="请输入倍率"
            style="width: 100%"
            :min="0"
            :precision="2"
          />
        </a-form-item>
        <a-form-item label="类型" name="type">
          <dict-select
            v-model="formData.type"
            placeholder="请选择类型"
            code="CT_BASE_ENUM_WaterElectriCityTableNum_Type"
          ></dict-select>
        </a-form-item>
        <a-form-item label="属性" name="property">
          <dict-select
            v-model="formData.property"
            placeholder="请选择属性"
            code="CT_BASE_ENUM_WaterElectriCityTableNum_Property"
          ></dict-select>
        </a-form-item>
        <a-form-item label="分组" name="treePath">
          <a-form-item-rest>
            <a-cascader
              v-model:value="formData.treePath"
              style="width: 100%"
              :options="treeOptions"
              placeholder="请选择分组"
              :field-names="{ label: 'name', value: 'id', children: 'children' }"
              @change="handleTreeChange"
            />
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="地址" name="pcaCodeArray">
          <a-form-item-rest>
            <div class="flex">
              <a-cascader
                class="!mr-[4px]"
                v-model:value="formData.pcaCodeArray"
                :options="areaList"
                placeholder="请选择省市区"
              />
              <a-input v-model:value="formData.address" placeholder="请输入详细地址" style="width: 100%" />
            </div>
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="租金归集公司" name="collectionCompany">
          <company-select v-model="formData.collectionCompany" placeholder="请选择租金归集公司"></company-select>
        </a-form-item>
        <a-form-item label="资产权属公司" name="ownerCompany">
          <company-select v-model="formData.ownerCompany" placeholder="请选择资产权属公司"></company-select>
        </a-form-item>
        <a-form-item label="物业管理公司" name="manageCompany">
          <company-select v-model="formData.manageCompany" placeholder="请选择物业管理公司" disabled></company-select>
        </a-form-item>
        <a-form-item label="单价" name="price">
          <a-input-number
            v-model:value="formData.price"
            placeholder="请输入单价"
            style="width: 100%"
            :min="0"
            :precision="6"
            addon-after="元"
          />
        </a-form-item>
        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" show-count :maxlength="255" />
        </a-form-item>
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { useUserStore } from '@/store/modules/user'
import areaList from '@/json/region.json'
import { addWaterElectricity, editWaterElectricity } from '../apis/waterElectricity'
import { getWaterElectricityTableNumTree } from '../apis/waterElectricityTableNumTree'

const store = useUserStore()

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const treeOptions = ref([])

const formDataDefault = {
  id: '',
  number: '',
  name: '',
  status: 'ENABLE',
  doubleRate: 1,
  type: '',
  property: '',
  treeId: '',
  treePath: [],
  address: '',
  price: '',
  collectionCompany: '',
  ownerCompany: '',
  manageCompany: '',
  remark: '',
  pcaCode: '',
  pcaCodeArray: []
}

const formData = reactive({ ...formDataDefault })

/**
 * 联合验证省市区代码数组和详细地址
 * @param {any} _rule - 验证规则
 * @param {Array} value - 省市区代码数组
 * @returns {Promise} 验证结果
 */
const validateAddressInfo = () => {
  const pcaCodeArray = formData.pcaCodeArray
  const address = formData.address

  // 省市区验证
  if (!pcaCodeArray || !Array.isArray(pcaCodeArray) || pcaCodeArray.length === 0) {
    return Promise.reject('请选择完整的省市区信息')
  }
  if (pcaCodeArray.length < 3) {
    return Promise.reject('请选择到区级行政区')
  }

  // 详细地址验证
  if (!address || address.trim() === '') {
    return Promise.reject('请输入详细地址')
  }

  return Promise.resolve()
}

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  number: [{ required: true, message: '请输入编码(表号)', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  property: [{ required: true, message: '请选择属性', trigger: 'change' }],
  doubleRate: [{ type: 'number', min: 0.01, message: '倍率必须大于0', trigger: 'blur' }],
  pcaCodeArray: [{ required: true, validator: validateAddressInfo, trigger: 'change' }],
  address: [{ required: true, validator: validateAddressInfo, trigger: ['blur', 'change'] }],
  collectionCompany: [{ required: true, message: '请选择租金归集公司', trigger: 'change' }],
  ownerCompany: [{ required: true, message: '请选择资产权属公司', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  price: [{ type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'blur' }]
}

/**
 * 初始化加载树形数据
 */
const loadTreeData = async () => {
  try {
    const response = await getWaterElectricityTableNumTree()
    if (response && response.success && Array.isArray(response.result)) {
      treeOptions.value = response.result
    }
  } catch {
    // 加载失败时的处理
    treeOptions.value = []
  }
}

const handleTreeChange = (value) => {
  formData.treeId = value && value.length ? value[1] : ''
}

/**
 * 打开抽屉弹窗
 * @param {Object} record 水电表记录，编辑时传入
 */
const open = (record) => {
  visible.value = true
  confirmLoading.value = true

  // 加载树形数据
  loadTreeData()

  formData.collectionCompany = store.userInfo.currentCompany
  formData.ownerCompany = store.userInfo.currentCompany
  formData.manageCompany = store.userInfo.currentCompany

  if (record?.id) {
    Object.assign(formData, record)
    // 处理省市区数据
    if (formData.pcaCode && typeof formData.pcaCode === 'string') {
      formData.pcaCodeArray = formData.pcaCode.split(',')
    }
    if (formData.treePath && formData.treePath.length) {
      formData.treePath = formData.treePath.split(',')
    }
  }
  confirmLoading.value = false
}

/**
 * 取消编辑并关闭抽屉
 */
const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  visible.value = false
}

/**
 * 保存水电表信息
 */
const handleSave = async () => {
  await formRef.value.validate()
  confirmLoading.value = true
  try {
    const params = { ...formData }
    params.pcaCode = formData.pcaCodeArray.join(',')
    params.treePath = formData.treePath.length ? formData.treePath.join(',') : ''
    if (params.id) {
      await editWaterElectricity(params)
    } else {
      await addWaterElectricity(params)
    }
    message.success('保存成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}

defineExpose({ open })
</script>

<style lang="less">
.edit-water-electricity-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }
}
</style>
