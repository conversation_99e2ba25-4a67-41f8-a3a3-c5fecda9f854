<template>
  <a-drawer
    v-model:open="visible"
    title="水电表详情"
    class="common-detail-drawer"
    width="1072px"
    :mask-closable="false"
  >
    <template #extra v-if="!readonly">
      <div class="flex">
        <span
          class="primary-btn"
          @click="handleEdit"
          v-auth="'bas:ct_bas_water_electricity_table_num:edit'"
          v-if="detailData.status !== 'ENABLE'"
        >
          编辑
        </span>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="delete">
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">{{ detailData.name }}</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_BaseStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <div class="mb-[40px]">
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">编码(表号): {{ detailData.number || '-' }}</span>
          <span class="w-[50%]">倍率: {{ detailData.doubleRate || '-' }}</span>
          <span class="w-[50%]">类型: {{ detailData.type_dictText || '-' }}</span>
          <span class="w-[50%]">属性: {{ detailData.property_dictText || '-' }}</span>
          <span class="w-[50%]">分组: {{ detailData.treeId_dictText || '-' }}</span>
          <span class="w-[50%]">地址: {{ detailData.address || '-' }}</span>
          <span class="w-[50%]">资产权属公司: {{ detailData.ownerCompany_dictText || '-' }}</span>
          <span class="w-[50%]">租金归集公司: {{ detailData.collectionCompany_dictText || '-' }}</span>
          <span class="w-[50%]">物业管理公司: {{ detailData.manageCompany_dictText || '-' }}</span>
          <span class="w-[100%]">单价: {{ renderMoney(detailData.price, 6, '元') || '-' }}</span>
          <span class="w-[100%] break-words whitespace-pre-wrap">备注: {{ detailData.remark || '-' }}</span>
        </div>
      </div>
    </a-spin>
  </a-drawer>
  <edit-water-electricity ref="editDrawerRef" @refresh="refreshData" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import { renderMoney } from '@/utils/render'
import { queryWaterElectricityById, deleteWaterElectricity } from '../apis/waterElectricity'
import EditWaterElectricity from './EditWaterElectricity.vue'

defineProps({
  readonly: { type: Boolean, default: false }
})

const emits = defineEmits(['refresh', 'edit'])

const visible = ref(false)
const loading = ref(false)
const editDrawerRef = ref()
const detailData = ref({})

/**
 * 打开详情抽屉
 */
const open = async (record) => {
  visible.value = true
  loading.value = true

  try {
    await loadDetail(record.id)
  } finally {
    loading.value = false
  }
}

/**
 * 编辑水电表
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 加载详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const { result } = await queryWaterElectricityById({ id })
    if (result) {
      detailData.value = result
    }
  } finally {
    loading.value = false
  }
}

/**
 * 删除水电表
 */
const handleDelete = () => {
  if (!hasPermission('bas:ct_bas_water_electricity_table_num:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除"${detailData.value.name}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteWaterElectricity({ id: detailData.value.id })
      message.success('删除成功')
      visible.value = false
      emits('refresh')
    }
  })
}

/**
 * 刷新当前详情数据
 */
const refreshData = async () => {
  await loadDetail(detailData.value.id)
  emits('refresh')
}

defineExpose({ open })
</script>
