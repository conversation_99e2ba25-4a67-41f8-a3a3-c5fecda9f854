import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

// 获取抄表数列表
export const getMeterReadList = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterElectricityMeterRead/list',
    params
  })
}

// 通过id查询抄表数详情
export const queryById = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterElectricityMeterRead/queryById',
    params
  })
}

// 新增抄表数
export const addMeterRead = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricityMeterRead/add',
    data
  })
}

// 编辑抄表数
export const editMeterRead = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricityMeterRead/edit',
    data
  })
}

// 删除抄表数
export const deleteMeterRead = (params) => {
  return request({
    method: 'delete',
    url: '/bas/waterElectricityMeterRead/delete',
    params
  })
}

// 批量删除抄表数
export const batchDeleteMeterRead = (params) => {
  return request({
    method: 'delete',
    url: '/bas/waterElectricityMeterRead/deleteBatch',
    params
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricityMeterRead/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/bas/waterElectricityMeterRead/importExcel', data, controller)
}
