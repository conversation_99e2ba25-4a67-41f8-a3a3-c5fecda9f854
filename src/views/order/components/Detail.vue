<template>
  <a-drawer
    v-model:open="visible"
    title="订单详情"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)" class="primary-btn" @click="handleEdit">
          编辑
        </span>
        <a-dropdown>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down text-[12px]"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item v-if="['AUDITOK'].includes(detailData.status)">
                <div class="primary-btn" @click="reverse">反审核</div>
              </a-menu-item>
              <a-menu-item v-if="['TEMP', 'BACK'].includes(detailData.status)">
                <div class="primary-btn" @click="handleDel">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">订单详情</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detailData.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>订单编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">下单时间：{{ detailData.bizDate || '-' }}</span>
        <span class="w-[50%]">下单客户：{{ detailData.customer_dictText || '-' }}</span>
        <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
        <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
        <span class="w-[50%]">状态：{{ renderDict(detailData.status, 'CT_BASE_ENUM_AuditStatus') || '-' }}</span>
        <span class="w-[50%]">结算时间：{{ detailData.createTime || '-' }}</span>
        <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
      </div>
      <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">计费明细</h2>
      <a-table
        :data-source="detailData.orderBillChargeDetailList"
        :columns="columns1"
        :pagination="false"
        :scroll="{ y: 300, x: 1300 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <span class="primary-btn" @click="rowViewAssetsDetail(record)">资产详情</span>
          </template>
        </template>
      </a-table>

      <div class="flex justify-between items-center mt-[24px] mb-[12px] text-[16px] font-bold text-[#1D335C]">
        <h2>结算记录</h2>
        <div>
          <span>已结算：{{ renderMoney(billingRecordData.consumedAmt, 2) }}</span>
          <span class="ml-[10px] mr-[10px]">|</span>
          <span>待结算：{{ renderMoney(billingRecordData.notConsumedAmt, 2) }}</span>
        </div>
      </div>

      <a-table
        :data-source="billingRecordData.list"
        :columns="columns2"
        :pagination="false"
        :scroll="{ y: 300, x: 1300 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <span class="primary-btn" @click="rowViewAssetsDetail(record)">资产详情</span>
          </template>
        </template>
      </a-table>

      <div class="bg-[#f7f8fa] border border-[#E6E9F0] p-[16px] rounded-[8px] mt-[40px]">
        <h2 class="text-[16px] font-bold mb-[8px] text-[#1D335C]">
          含税总金额：{{ renderMoney(computePreview.allAmount, 2) }}
        </h2>
        <div class="text-secondary text-[14px]">
          <span>税额：{{ renderMoney(computePreview.allTaxAmount, 2) }}</span>
          <span class="ml-[10px] mr-[10px]">|</span>
          <span>不含税金额：{{ renderMoney(computePreview.allAmount - computePreview.allTaxAmount, 2) }}</span>
          <span class="ml-[10px] mr-[10px]">|</span>
          <span>定金：{{ renderMoney(computePreview.allDepositAmount, 2) }}</span>
        </div>
      </div>
    </a-spin>
  </a-drawer>
  <!-- 编辑 -->
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
</template>
<script setup>
import AddEdit from './AddEdit.vue'
import { Modal, message } from 'ant-design-vue'
import { detailById, queryOrderBillChargeDetailByMainId, delById, unAudit, listByOrderBillId } from '../apis'
import { renderDict, renderMoney } from '@/utils/render'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    getDetailById(data.id)
  }
}
defineExpose({ open })

const computePreview = computed(() => {
  const obj = {
    allAmount: 0,
    allTaxAmount: 0,
    allDepositAmount: 0
  }
  detailData.value.orderBillChargeDetailList.forEach((item) => {
    obj.allAmount += item.amount
    const count = (item.taxRate / 100) * item.amount
    obj.allTaxAmount += count
    obj.allDepositAmount += item.depositAmount
  })
  return obj
})
// 通过id获取详情
const billingRecordData = ref({
  consumedAmt: 0,
  list: [],
  notConsumedAmt: 0
})
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  const data = await queryOrderBillChargeDetailByMainId(id)
  const data2 = await listByOrderBillId({ orderBillId: id })
  billingRecordData.value = data2.result
  result.orderBillChargeDetailList = data.result
  detailData.value = result
}

const columns1 = [
  {
    title: '类型',
    dataIndex: 'type',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_OrderBillChargeDetail_Type'),
    width: 200,
    ellipsis: true,
    fixed: true
  },
  { title: '计费项', dataIndex: 'orderChargeProject_dictText', ellipsis: true, width: 200 },
  { title: '计费说明', dataIndex: 'orderChargeProjectRule' },
  { title: '消费数量', dataIndex: 'count' },
  { title: '单位', dataIndex: 'measureUnit', customRender: ({ text }) => renderDict(text, 'CT_BAS_MeasureUnit') },
  { title: '平均单价', dataIndex: 'price', customRender: ({ text }) => renderMoney(text, 6) },
  { title: '税率', dataIndex: 'taxRate', customRender: ({ text }) => renderMoney(text, 4) },
  { title: '定金', dataIndex: 'depositAmount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '总金额', dataIndex: 'amount', customRender: ({ text }) => renderMoney(text, 2) }
]
const columns2 = [
  { title: '结算日期', dataIndex: 'receiveDate', width: 200, ellipsis: true, fixed: true },
  {
    title: '结算金额',
    dataIndex: 'consumedAmt',
    ellipsis: true,
    customRender: ({ text }) => renderMoney(text, 2)
  }
  // { title: '结算方式', dataIndex: 'orderChargeProjectRule' }
]
const detailData = ref({
  orderBillChargeDetailList: []
})
const loading = ref(false)

// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
  handleClose()
}

// 反审核
const reverse = () => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      await unAudit({ id: detailData.value.id })
      message.success('反审核成功')
      emits('loadData')
      handleClose()
    }
  })
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '确认删除当前资产处置？',
    content: '',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      handleClose()
    }
  })
}

// 查看资产详情
const detailRef = ref()
const rowViewAssetsDetail = (row) => {
  detailRef?.value.open(row.houseOwner)
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>
