<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="ruleForm.id ? '编辑订单' : '新增订单'"
    placement="right"
    width="1072px"
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="ruleForm" ref="formRef" :rules="rules" :label-col="{ style: { width: '80px' } }" autocomplete="off">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="业务日期" name="bizDate">
            <a-date-picker
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              v-model:value="ruleForm.bizDate"
              type="date"
              placeholder="请选择业务日期"
              class="w-[100%]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="下单客户" name="customer">
            <a-form-item-rest>
              <f7-select v-model="ruleForm.customer" f7-type="customer" placeholder="请选择下单客户"></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="经办人" name="operator">
            <a-form-item-rest>
              <f7-select
                v-model="ruleForm.operator"
                f7-type="user"
                :depart-id="ruleForm.operatorDepart"
                relation-depart
                placeholder="请选择经办人"
              ></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="业务部门" name="operatorDepart">
            <depart-select v-model="ruleForm.operatorDepart" placeholder="请选择业务部门"></depart-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="ruleForm.remark"
              placeholder="请输入备注"
              :maxlength="255"
              :rows="4"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>

      <div class="mb-[12px] mt-[12px] flex justify-between items-center">
        <div class="text-[16px] font-bold">计费明细</div>
        <a-button type="primary" ghost @click="addOrderBillChargeDetailList">
          <span class="a-icon-plus mr-[8px]"></span>
          添加
        </a-button>
      </div>
      <a-table
        :data-source="ruleForm.orderBillChargeDetailList"
        :columns="columns"
        :scroll="{ y: 300, x: 1500 }"
        :pagination="false"
      >
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'type'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'orderChargeProject'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'count'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'taxRate'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'amount'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
        </template>
        <template #bodyCell="{ column, record, index }">
          <!-- 类型 -->
          <template v-if="column.dataIndex === 'type'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'type']"
              :rules="{
                required: true,
                message: '请选择类型',
                trigger: 'change'
              }"
            >
              <div class="flex items-center">
                <span
                  class="a-icon-remove remove-btn cursor-pointer font-18 mr-[5px]"
                  @click="rowDel(record, index)"
                ></span>
                <dict-select
                  v-model="record.type"
                  placeholder="请选择类型"
                  code="CT_BASE_ENUM_OrderBillChargeDetail_Type"
                ></dict-select>
              </div>
            </a-form-item>
          </template>
          <!-- 计费项 -->
          <template v-if="column.dataIndex === 'orderChargeProject'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'orderChargeProject']"
              :rules="{
                required: true,
                message: '请选择计费项',
                trigger: 'change'
              }"
            >
              <!-- 固定计费项 -->
              <a-form-item-rest v-if="record.type === 'FixChargeProject'">
                <f7-select
                  v-model="record.orderChargeProject"
                  f7-type="orderBillingItem"
                  placeholder="请选择计费项"
                  @change="(id, value) => rowOrderChargeProjectChange(id, value, record)"
                ></f7-select>
              </a-form-item-rest>
              <!-- 临时计费项 -->
              <a-input-number
                v-if="record.type === 'TempChargeProject'"
                v-model:value="record.orderChargeProject"
                class="!w-[100%]"
                :min="0"
                :precision="2"
                placeholder="请选择计费项"
              ></a-input-number>
            </a-form-item>
          </template>
          <!-- 计费说明 -->
          <template v-if="column.dataIndex === 'orderChargeProjectRule'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'orderChargeProjectRule']"
              :rules="{
                required: false,
                message: '请输入计费说明',
                trigger: 'change'
              }"
            >
              <!-- 固定计费项 -->
              <div v-if="record.type === 'FixChargeProject'">dddd</div>
              <!-- 临时计费项 -->
              <a-input
                v-if="record.type === 'TempChargeProject'"
                v-model:value="record.orderChargeProjectRule"
                class="!w-[100%]"
                placeholder="请输入计费说明"
              ></a-input>
            </a-form-item>
          </template>
          <!-- 消费数量 -->
          <template v-if="column.dataIndex === 'count'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'count']"
              :rules="{
                required: true,
                message: '请输入消费数量',
                trigger: 'change'
              }"
            >
              <a-input-number
                v-model:value="record.count"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入消费数量"
                @change="rowCountChange(record)"
              />
            </a-form-item>
          </template>
          <!-- 单位 -->
          <template v-if="column.dataIndex === 'measureUnit'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'measureUnit']"
              :rules="{
                required: false,
                message: '请输入单位',
                trigger: 'change'
              }"
            >
              <!-- 固定计费项 -->
              <div v-if="record.type === 'FixChargeProject'">{{ record.measureUnit }}</div>
              <!-- 临时计费项 -->
              <a-input
                v-if="record.type === 'TempChargeProject'"
                v-model:value="record.measureUnit"
                placeholder="请输入单位"
                class="w-[100%]"
              ></a-input>
            </a-form-item>
          </template>
          <!-- 平均单价 -->
          <template v-if="column.dataIndex === 'price'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'price']"
              :rules="{
                required: false,
                message: '请输入平均单价',
                trigger: 'change'
              }"
            >
              <!-- 固定计费项 -->
              <div v-if="record.type === 'FixChargeProject'">{{ record.price }}</div>
              <!-- 临时计费项 -->
              <a-input-number
                v-if="record.type === 'TempChargeProject'"
                v-model:value="record.price"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入平均单价"
              />
            </a-form-item>
          </template>
          <!-- 税率 -->
          <template v-if="column.dataIndex === 'taxRate'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'taxRate']"
              :rules="{
                required: true,
                message: '请输入税率',
                trigger: 'blur'
              }"
            >
              <!-- 固定计费项 -->
              <div v-if="record.type === 'FixChargeProject'">{{ record.taxRate }}</div>
              <!-- 临时计费项 -->
              <a-input-number
                v-if="record.type === 'TempChargeProject'"
                v-model:value="record.taxRate"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入税率"
              />
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'depositAmount'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'depositAmount']"
              :rules="{
                required: false,
                message: '请输入定金',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.depositAmount"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入定金"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'amount'">
            <a-form-item
              label=""
              :name="['orderBillChargeDetailList', index, 'amount']"
              :rules="{
                required: true,
                message: '请输入总金额',
                trigger: 'blur'
              }"
            >
              <!-- 固定计费项 -->
              <div v-if="record.type === 'FixChargeProject'">{{ record.amount }}</div>
              <!-- 临时计费项 -->
              <a-input-number
                v-if="record.type === 'TempChargeProject'"
                v-model:value="record.amount"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入总金额"
              />
            </a-form-item>
          </template>
        </template>
      </a-table>
    </a-form>
    <div class="bg-[#f7f8fa] border border-[#E6E9F0] p-[16px] rounded-[8px] mt-[40px]">
      <h2 class="text-[16px] font-bold mb-[8px] text-[#1D335C]">
        含税总金额：{{ renderMoney(computePreview.allAmount) }}
      </h2>
      <div class="text-secondary text-[14px]">
        <span>税额：{{ renderMoney(computePreview.allTaxAmount) }}</span>
        <span class="ml-[10px] mr-[10px]">|</span>
        <span>不含税金额：{{ renderMoney(computePreview.allAmount - computePreview.allTaxAmount) }}</span>
        <span class="ml-[10px] mr-[10px]">|</span>
        <span>定金：{{ renderMoney(computePreview.allDepositAmount) }}</span>
      </div>
    </div>

    <template #footer>
      <a-button type="primary" @click="handleConfirm(0)" :loading="submitLoading">提交</a-button>
      <a-button type="primary" ghost @click="handleStash" :loading="stashLoading">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>
<script setup>
import { Modal, message } from 'ant-design-vue'
import { stash, submit, edit, queryOrderBillChargeDetailByMainId } from '../apis'
import { queryOrderChargeProjectLadderDetailByMainId } from '@/views/billingManage/orderbillingItem/apis'
import { useUserStore } from '@/store/modules/user'
import { renderDict, renderMoney } from '@/utils/render'
import { getF7List } from '@/views/billingManage/orderBillingItem/apis' // 获取订单计费项数据接口
import dayjs from 'dayjs'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}, detailList = []) => {
  visible.value = true
  ruleForm.orderBillChargeDetailList = detailList
  if (data.id) {
    getQueryOrderBillChargeDetailByMainId(data)
  } else {
    ruleForm.manageCompany = userInfo.value.currentCompany || ''
    ruleForm.operator = userInfo.value.id || ''
    ruleForm.operatorDepart = userInfo.value.currentDepart
    ruleForm.bizDate = dayjs(Date.now()).format('YYYY-MM-DD') // 业务日期默认取当天
  }
}
defineExpose({ open })
const store = useUserStore()
const userInfo = computed(() => store.userInfo)
// 获取 计费明细
const getQueryOrderBillChargeDetailByMainId = async (data) => {
  const { result } = await queryOrderBillChargeDetailByMainId(data.id)
  await Promise.all(
    result.map(async (item) => {
      const {
        result: { records }
      } = await getF7List({ id: item.orderChargeProject }) // 获取订单计费项数据
      if (records.length === 1) {
        item.innerCapCount = records[0].innerCapCount // 封顶数量
        item.innerCapAmount = records[0].innerCapAmount // 封顶价格
        item.measureUnit = records[0].measureUnit // 单位
        item.taxRate = records[0].taxRate // 税率
      }

      getStepPriceList(item.orderChargeProject, item)
    })
  )
  data.orderBillChargeDetailList = result
  Object.assign(ruleForm, data)
}
const ruleForm = reactive({
  id: '',
  bizDate: '',
  customer: '',
  operator: '',
  operatorDepart: '',
  remark: '',
  manageCompany: '',
  // number: '',
  // billSource: '',
  // status: '',
  // bizStatus: '',
  // depositAmount: 0,
  // amount: 0,
  // taxAmount: 0,
  // containTaxAmount: 0,
  // createBy: '',
  // createTime: '',
  // updateBy: '',
  // updateTime: '',
  // auditBy: '',
  // auditTime: '',
  // attachmentIds: '',
  // sourceBillId: '',
  // sourceBillEntryId: '',
  // ctrlUnit: '',
  orderBillChargeDetailList: []
})
const rules = computed(() => ({
  bizDate: [{ required: true, message: '请选择下单日期', trigger: ['change'] }],
  customer: [{ required: true, message: '请选择下单客户', trigger: ['change'] }],
  operator: [{ required: true, message: '请选择经办人', trigger: ['change'] }],
  operatorDepart: [{ required: true, message: '请选择业务部门', trigger: ['change'] }]
}))
const columns = [
  { title: '类型', dataIndex: 'type', width: 200, ellipsis: true, fixed: true },
  { title: '计费项', dataIndex: 'orderChargeProject', ellipsis: true, width: 200 },
  { title: '计费说明', dataIndex: 'orderChargeProjectRule' },
  { title: '消费数量', dataIndex: 'count' },
  { title: '单位', dataIndex: 'measureUnit', customRender: ({ text }) => renderDict(text, 'CT_BAS_MeasureUnit') },
  { title: '平均单价', dataIndex: 'price' },
  { title: '税率', dataIndex: 'taxRate' },
  { title: '定金', dataIndex: 'depositAmount' },
  { title: '总金额', dataIndex: 'amount' }
]

const computePreview = computed(() => {
  const obj = {
    allAmount: 0,
    allTaxAmount: 0,
    allDepositAmount: 0
  }
  ruleForm.orderBillChargeDetailList.forEach((item) => {
    obj.allAmount += Number(item.amount)
    const count = (item.taxRate / 100) * Number(item.amount)
    obj.allTaxAmount += count
    obj.allDepositAmount += item.depositAmount
  })
  return obj
})

// 计费明细 新增
const addOrderBillChargeDetailList = () => {
  ruleForm.orderBillChargeDetailList.push({
    id: '',
    type: '',
    orderChargeProject: '',
    orderChargeProjectRule: '',
    count: 0,
    measureUnit: '',
    price: 0,
    taxRate: 0,
    depositAmount: 0,
    amount: 0
    // taxAmount: 0,
    // containTaxAmount: 0,
    // remark: '',
    // sourceBillId: '',
    // sourceBillEntryId: '',
    // ctrlUnit: ''
  })
}
// 计费明细 删除
const rowDel = (row, index) => {
  if (row.id) {
    Modal.confirm({
      title: '确定删除当前计费明细？',
      content: '',
      centered: true,
      onOk: () => {
        ruleForm.orderBillChargeDetailList.splice(index, 1)
      }
    })
    return
  }
  ruleForm.orderBillChargeDetailList.splice(index, 1)
}
// 行数据 计费项选择的回调
const rowOrderChargeProjectChange = (id, value, row) => {
  row.innerCapCount = value.innerCapCount // 封顶数量
  row.innerCapAmount = value.innerCapAmount // 封顶价格
  row.measureUnit = value.measureUnit // 单位
  row.taxRate = value.taxRate // 税率
  getStepPriceList(id, row)
}
const getStepPriceList = async (id, row) => {
  // 根据计费项id获取阶梯价明细
  const { result } = await queryOrderChargeProjectLadderDetailByMainId({ id })
  row.stepPriceList = result.map((item) => {
    return { count: item.count, amount: item.amount }
  })
  // row.orderChargeProjectRule = value.remark // （需要根据规则生成文本）
  if (row.stepPriceList && row.count && row.innerCapCount && row.innerCapAmount) {
    row.amount = calculateFee(row.stepPriceList, row.count, row.innerCapCount, row.innerCapAmount) // 总金额
    row.price = (Number(row.amount) / row.count).toFixed(2)
  }
}
/**
 * 计算分段计费的总金额
 * @param {Array} arr 分段计费规则数组，按顺序包含 {count: 小时数, amount: 单价}
 * @param {number} num 实际使用的小时数
 * @param {number} allNum 封顶小时数
 * @param {number} allCount 封顶金额
 * @returns {number} 计算后的总费用
 */
// 通过计算规则来获取总金额
const calculateFee = (arr, num, allNum, allCount) => {
  // 如果使用时间超过封顶小时数，直接返回封顶金额
  if (num >= allNum) {
    return allCount
  }
  let total = 0
  let remainingHours = num
  // 按顺序计算每个分段的费用
  for (let i = 0; i < arr.length; i++) {
    const segment = arr[i]
    // 如果剩余小时数为0，跳出循环
    if (remainingHours <= 0) break
    // 当前分段可计费的小时数
    const segmentHours = Math.min(segment.count, remainingHours)
    // 计算当前分段的费用并累加
    total += segmentHours * segment.amount
    // 减去已计算的小时数
    remainingHours -= segmentHours
  }
  // 如果还有剩余小时数且没有更多分段，按最后一个分段的单价计算
  if (remainingHours > 0) {
    const lastSegment = arr[arr.length - 1]
    total += remainingHours * lastSegment.amount
  }
  // 如果总费用超过封顶金额，返回封顶金额
  const amount = Math.min(total, allCount)
  return amount.toFixed(2)
}

const rowCountChange = (row) => {
  if (row.stepPriceList && row.count && row.innerCapCount && row.innerCapAmount) {
    row.amount = calculateFee(row.stepPriceList, row.count, row.innerCapCount, row.innerCapAmount) // 总金额
    row.price = (Number(row.amount) / row.count).toFixed(2)
  }
}

const formRef = ref()
const submitLoading = ref(false)
// 提交
const handleConfirm = async (type) => {
  await formRef.value.validate()
  submitLoading.value = true
  try {
    await submit(ruleForm)
    message.success('提交成功')
    emits('loadData')
    if (type) {
      return clearForm()
    }
    handleCancel()
  } finally {
    submitLoading.value = false
  }
}

// 暂存
const stashLoading = ref(false)
const handleStash = async () => {
  await formRef.value.validate()
  stashLoading.value = true
  try {
    const { message: msg, result } = await (ruleForm.id ? edit(ruleForm) : stash(ruleForm))
    message.success(msg)
    getQueryOrderBillChargeDetailByMainId(result)
    emits('loadData')
    stashLoading.value = false
  } finally {
    stashLoading.value = false
  }
}

const clearForm = () => {
  formRef.value.clearValidate()
}
// 取消
const handleCancel = () => {
  clearForm()
  ruleForm.id = ''
  ruleForm.bizDate = ''
  ruleForm.customer = ''
  ruleForm.operator = ''
  ruleForm.operatorDepart = ''
  ruleForm.remark = ''
  ruleForm.manageCompany = ''
  ruleForm.orderBillChargeDetailList = []
  visible.value = false
}
</script>

<style scoped lang="less">
// 自定义表头文本必填
.table-header-col {
  &::after {
    display: inline-block;
    content: '*';
    color: var(--color-red-600);
  }
}
// 移除按钮hover样式
.remove-btn {
  font-size: 16px;
  &:hover {
    color: var(--color-red-600);
  }
}
</style>
