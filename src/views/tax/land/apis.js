import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/landUseTaxJtBill/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/landUseTaxJtBill/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/landUseTaxJtBill/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/landUseTaxJtBill/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/landUseTaxJtBill/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/landUseTaxJtBill/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/landUseTaxJtBill/edit',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/landUseTaxJtBill/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/landUseTaxJtBill/unAudit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/taxaccrual/landUseTaxJtBill/deleteBatch',
    params
  })
}

// 土地使用税计提单-生成计提单
export const houseOwnerJt = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/landUseTaxJtBill/houseOwnerJt',
    params
  })
}

// 土地使用税计提单分录主表ID查询
export const queryLandUseTaxJTBill = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/landUseTaxJtBill/queryLandUseTaxJTBillEntryByMainId',
    params
  })
}
