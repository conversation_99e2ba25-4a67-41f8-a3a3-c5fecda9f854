import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/paymentType/list',
    params
  })
}

export const list = (params) => {
  return request({
    method: 'get',
    url: '/bas/paymentType/queryList',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/paymentType/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/paymentType/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/paymentType/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/paymentType/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/paymentType/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/paymentType/deleteBatch',
    params
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/paymentType/updateEnableDisableStatus',
    data
  })
}

// 款项类型-应收单分录-通主表ID查询
export const queryPaymentTypeReceiveBill = (params) => {
  return request({
    method: 'get',
    url: '/bas/paymentType/queryPaymentTypeReceiveBillByMainId',
    params
  })
}

// 款项类型-缴款说明书分录-通主表ID查询
export const queryPaymentTypePayExplainBook = (params) => {
  return request({
    method: 'get',
    url: '/bas/paymentType/queryPaymentTypePayExplainBookByMainId',
    params
  })
}
