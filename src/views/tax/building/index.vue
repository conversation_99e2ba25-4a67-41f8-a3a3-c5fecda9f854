<template>
  <div>
    <a-tabs v-model:active-key="params.status" size="large" @change="refresh">
      <a-tab-pane v-for="item in statusList" :key="item.value" :tab="item.label"></a-tab-pane>
    </a-tabs>
    <div class="flex items-center justify-between my-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.number"
          placeholder="搜索单据编号"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span v-if="record.status === 'AUDITING'" class="primary-btn" @click="handleAudit(record)">通过</span>
          <span v-else class="primary-btn" @click="handleView(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleView(record)">查看</div>
                </a-menu-item>
                <a-menu-item v-if="record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleUnAudit(record)">取消通过</div>
                </a-menu-item>
                <template v-else>
                  <a-menu-item>
                    <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                  </a-menu-item>
                  <a-menu-item>
                    <div class="primary-btn" @click="handleRemove(record)">删除</div>
                  </a-menu-item>
                </template>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail
      ref="detailRef"
      :data-list="list"
      @edit="handleEdit"
      @audit="handleAudit"
      @refresh="refreshFromDetail"
    ></detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('房产税计提单.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, audit, unAudit, exportExcel, importExcel } from './apis.js'
import { renderDictTag } from '@/utils/render'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'

const statusList = ref([
  { label: '全部', value: '' },
  { label: '暂存', value: 'TEMP' },
  { label: '已提交', value: 'AUDITING' },
  { label: '已通过', value: 'AUDITOK' },
  { label: '未通过', value: 'AUDITNO' }
])

const params = reactive({
  column: 'number',
  order: 'desc',
  status: undefined,
  number: undefined,
  belongYm: undefined,
  bizDate: undefined,
  jtCompany: undefined,
  voucherNumber: undefined,
  voucherType: undefined,
  isIncludeNoAuditContract: undefined,
  remark: undefined,
  createTime: undefined
})

const searchList = [
  { label: '计提年月', name: 'belongYm', type: 'date', picker: 'month', valueFormat: 'YYYY-MM', format: 'YYYY-MM' },
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '税费计提公司', name: 'jtCompany', type: 'company-select' },
  { label: '凭证号', name: 'voucherNumber', type: 's-input' },
  { label: '凭证字', name: 'voucherType', type: 's-input' },
  {
    label: '含未审核合同',
    name: 'isIncludeNoAuditContract',
    type: 'a-select',
    options: [
      { id: 'true', name: '是' },
      { id: 'false', name: '否' }
    ]
  },
  { label: '备注', name: 'remark', type: 's-input' },
  { label: '创建时间', name: 'createTime', type: 'date', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
]

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: 'left' },
  { title: '计提年月', dataIndex: 'belongYm', width: 120 },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '税费计提公司', dataIndex: 'jtCompany_dictText', width: 240 },
  { title: '凭证号', dataIndex: 'voucherNumber', width: 180 },
  { title: '凭证字', dataIndex: 'voucherType', width: 100 },
  {
    title: '含未审核合同',
    dataIndex: 'isIncludeNoAuditContract',
    width: 140,
    customRender: ({ text }) => {
      return text ? '是' : '否'
    }
  },
  { title: '备注', dataIndex: 'remark', width: 200, ellipsis: true },
  { title: '创建时间', dataIndex: 'createTime', width: 170 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除房产税计提单？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success(msg)
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const handleAudit = (data) => {
  Modal.confirm({
    title: '确认通过该房产税计提单？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await audit(data)
      message.success(msg)
      onTableChange(pagination.value)
    }
  })
}

const handleUnAudit = (data) => {
  Modal.confirm({
    title: '确认取消通过该房产税计提单？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await unAudit(data)
      message.success(msg)
      onTableChange(pagination.value)
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('房产税计提单数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
