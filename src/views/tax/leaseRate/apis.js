import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseRateScheme/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseRateScheme/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/leaseRateScheme/deleteBatch',
    params
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/updateEnableDisableStatus',
    data
  })
}

// 款项类型-应收单分录-通主表ID查询
export const queryPaymentTypeReceiveBill = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseRateScheme/queryPaymentTypeReceiveBillByMainId',
    params
  })
}

// 款项类型-缴款说明书分录-通主表ID查询
export const queryPaymentTypePayExplainBook = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseRateScheme/queryPaymentTypePayExplainBookByMainId',
    params
  })
}
