import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/sys/quartzJob/list',
    params
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/sys/quartzJob/deleteBatch',
    params
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/sys/quartzJob/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/sys/quartzJob/edit',
    data
  })
}

export const stop = (params) => {
  return request({
    method: 'get',
    url: '/sys/quartzJob/pause',
    params
  })
}

export const start = (params) => {
  return request({
    method: 'get',
    url: '/sys/quartzJob/resume',
    params
  })
}

export const execute = (params) => {
  return request({
    method: 'get',
    url: '/sys/quartzJob/execute',
    params
  })
}
