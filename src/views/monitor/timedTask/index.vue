<template>
  <div>
    <a-form layout="inline" class="!mb-[16px]">
      <a-form-item>
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新增
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
      </a-form-item>
      <a-form-item label="任务类名" class="!ml-[40px]">
        <s-input v-model="params.jobClassName" placeholder="搜索任务类名" @input="handleInput"></s-input>
      </a-form-item>
      <a-form-item label="任务状态">
        <dict-select
          v-model="params.status"
          code="quartz_status"
          width="170px"
          @change="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></dict-select>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span
            class="text-error cursor-pointer mr-[8px]"
            @click="handleUpdateStatus(record)"
            v-if="record.status === 0"
          >
            停止
          </span>
          <span class="primary-btn" @click="handleUpdateStatus(record)" v-else>启动</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div class="primary-btn" @click="handleExecute(record)">立即执行</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleRemove(record)">删除</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, stop, start, execute } from './apis'
import { message, Modal } from 'ant-design-vue'
import Edit from './components/Edit.vue'
import { renderDictTag } from '@/utils/render'

const params = reactive({
  column: 'createTime',
  order: 'desc',
  jobClassName: '',
  status: ''
})

const columns = [
  { title: '任务类名', dataIndex: 'jobClassName', width: 240, fixed: 'left' },
  { title: 'Cron表达式', dataIndex: 'cronExpression' },
  { title: '参数', dataIndex: 'parameter' },
  { title: '描述', dataIndex: 'description', ellipsis: true },
  { title: '状态', dataIndex: 'status', width: 100, customRender: ({ text }) => renderDictTag(text, 'quartz_status') },
  { title: '操作', dataIndex: 'action', width: 130, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  let paramterType
  try {
    const obj = JSON.parse(data.parameter)
    paramterType = typeof obj === 'object' ? 'json' : 'string'
  } catch {
    paramterType = 'string'
  }
  editRef.value.open({
    id: data.id,
    jobClassName: data.jobClassName,
    cronExpression: data.cronExpression,
    paramterType,
    parameter: data.parameter,
    status: String(data.status),
    description: data.description
  })
}

const handleUpdateStatus = (data) => {
  Modal.confirm({
    title: `确认${data.status === 0 ? '停止' : '启动'}该定时任务？`,
    content: '',
    centered: true,
    onOk: async () => {
      data.status === 0 ? await stop({ id: data.id }) : await start({ id: data.id })
      message.success(data.status === 0 ? '已停止' : '启用成功')
      data.status = data.status === 0 ? -1 : 0
    }
  })
}

const handleExecute = (data) => {
  Modal.confirm({
    title: '确认立即执行该定时任务？',
    content: '',
    centered: true,
    onOk: async () => {
      await execute({ id: data.id })
      message.success('执行成功')
    }
  })
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除该项？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

onMounted(() => {
  onTableChange()
})
</script>
