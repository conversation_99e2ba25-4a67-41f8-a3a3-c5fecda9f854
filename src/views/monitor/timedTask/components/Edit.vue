<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑任务' : '新增任务'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '90px' } }"
      autocomplete="off"
    >
      <a-form-item label="任务类名" name="jobClassName">
        <a-input v-model:value="form.jobClassName" placeholder="请输入任务类名" :maxlength="255" />
      </a-form-item>
      <a-form-item label="Cron表达式" name="cronExpression">
        <a-input v-model:value="form.cronExpression" placeholder="请输入Cron表达式" :maxlength="255">
          <template #suffix>
            <span class="primary-btn" @click.stop="handleChoose">
              <i class="a-icon-setting"></i>
              选择
            </span>
          </template>
        </a-input>
      </a-form-item>
      <a-form-item label="参数类型" name="paramterType">
        <a-select v-model:value="form.paramterType">
          <a-select-option value="json">JSON</a-select-option>
          <a-select-option value="string">字符串</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="参数" name="parameter" v-if="form.paramterType === 'string'">
        <a-textarea v-model:value="form.parameter" placeholder="请输入参数" show-count :maxlength="250" />
      </a-form-item>
      <a-form-item label="参数" name="parameter" v-else>
        <a-form-item-rest>
          <div>
            <div class="flex items-center mb-[10px]" v-for="(item, index) in keyValues" :key="item.id">
              <span class="shrink-0 mr-[8px]">key:</span>
              <a-input size="medium" v-model:value="item.key" placeholder="请输入参数key" :maxlength="100"></a-input>
              <span class="shrink-0 mx-[8px]">value:</span>
              <a-input
                size="medium"
                v-model:value="item.value"
                placeholder="请输入参数value"
                :maxlength="100"
              ></a-input>
              <i class="a-icon-remove text-error cursor-pointer ml-[12px]" @click="handleRemoveJson(index)"></i>
            </div>
          </div>
          <a-button size="medium" type="primary" ghost @click="handleAddJson">
            <i class="a-icon-plus"></i>
            新增
          </a-button>
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="form.status">
          <a-radio value="0">正常</a-radio>
          <a-radio value="-1">停止</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="描述" name="description">
        <a-textarea v-model:value="form.description" placeholder="请输入描述" :maxlength="255" :rows="4" show-count />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { add, edit } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    if (data.paramterType === 'json') {
      const obj = JSON.parse(data.parameter)
      for (const key in obj) {
        keyValues.value.push({ id: Math.random().toString(), key, value: obj[key] })
      }
    }
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  id: '',
  jobClassName: '',
  cronExpression: '',
  paramterType: 'string',
  parameter: '',
  status: '0',
  description: ''
})

const rules = {
  jobClassName: [{ required: true, message: '请输入任务类名', trigger: 'blur' }],
  cronExpression: [{ required: true, message: '请输入Cron表达式', trigger: 'blur' }]
}

const keyValues = ref([])
const handleAddJson = () => {
  keyValues.value.push({
    id: Math.random().toString(),
    key: '',
    value: ''
  })
}
const handleRemoveJson = (index) => {
  keyValues.value.splice(index, 1)
}

const handleChoose = () => {
  message.warning('待开发')
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    if (params.paramterType === 'json') {
      const obj = {}
      keyValues.value.forEach((item) => {
        if (item.key && item.value) {
          obj[item.key] = item.value
        }
      })
      params.parameter = JSON.stringify(obj)
    }
    form.id ? await edit(params) : await add(params)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh', !form.id)
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.jobClassName = ''
  form.cronExpression = ''
  form.paramterType = 'string'
  form.parameter = ''
  form.status = '0'
  form.description = ''
  keyValues.value = []
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
