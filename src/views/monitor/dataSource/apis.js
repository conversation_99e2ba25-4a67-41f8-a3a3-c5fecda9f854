import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/sys/dataSource/list',
    params
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/sys/dataSource/deleteBatch',
    params
  })
}

export const testConnection = (data) => {
  return request({
    method: 'post',
    url: '/online/cgreport/api/testConnection',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/sys/dataSource/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/sys/dataSource/edit',
    data
  })
}
