<template>
  <a-modal
    v-model:open="visible"
    title="数据对比"
    width="1000px"
    wrap-class-name="compare-data-modal common-modal"
    :footer="false"
  >
    <div class="flex items-center mb-[16px]">
      <span>版本对比:</span>
      <a-select
        v-model:value="dataId1"
        :options="options"
        :field-names="{ label: 'dataVersion', value: 'id' }"
        style="width: 80px; margin-left: 10px"
        @change="onchange1"
      ></a-select>
      <a-select
        v-model:value="dataId2"
        :options="options"
        :field-names="{ label: 'dataVersion', value: 'id' }"
        style="width: 80px; margin-left: 10px"
        @change="onchange2"
      ></a-select>
    </div>
    <div class="pb-[24px]">
      <a-table
        :data-source="compareResult"
        :columns="columns"
        :loading="loading"
        :pagination="false"
        :row-class-name="(record) => (!record.isEqual ? 'no-equal' : '')"
        :scroll="{ y: 'calc(60vh - 150px)' }"
      ></a-table>
    </div>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { queryDataVerList, queryCompareList } from '../apis'

const visible = ref(false)
let dataTable = ''
let dataId = ''
const open = (data) => {
  dataTable = data.dataTable
  dataId = data.dataId
  visible.value = true
  loadOptions()
}

const dataId1 = ref('')
const dataId2 = ref('')

const options = ref([])
const loadOptions = async () => {
  const { result } = await queryDataVerList({ dataTable, dataId })
  options.value = result
  dataId1.value = result[0].id
  dataId2.value = result[1].id
  await loadCompareResult()
}

const columns = ref([
  { title: '字段名', dataIndex: 'name' },
  { title: '版本', dataIndex: 'value1' },
  { title: '版本', dataIndex: 'value2' }
])

const loading = ref(false)
const compareResult = ref([])
const loadCompareResult = async () => {
  loading.value = true
  const { result } = await queryCompareList({ dataId1: dataId1.value, dataId2: dataId2.value })
  if (!result.length) return
  compareResult.value = []
  const obj1 = JSON.parse(result[0].dataContent)
  const obj2 = JSON.parse(result[1].dataContent)
  for (const key in obj1) {
    const value1 = String(obj1[key])
    const value2 = String(obj2[key])
    compareResult.value.push({
      name: key,
      value1,
      value2,
      isEqual: value1 === value2
    })
  }
  columns.value[1].title = `版本${options.value.find((i) => i.id === dataId1.value).dataVersion}`
  columns.value[2].title = `版本${options.value.find((i) => i.id === dataId2.value).dataVersion}`
  loading.value = false
}

const onchange1 = (id) => {
  if (id === dataId2.value) {
    message.warning('相同版本号不能比较')
    return
  }
  loadCompareResult()
}
const onchange2 = (id) => {
  if (id === dataId1.value) {
    message.warning('相同版本号不能比较')
    return
  }
  loadCompareResult()
}

defineExpose({ open })
</script>

<style lang="less">
.compare-data-modal {
  .no-equal {
    .ant-table-cell {
      color: var(--color-error);
      background-color: rgba(var(--color-error-rgb), 0.1) !important;
    }
  }
}
</style>
