<template>
  <div>
    <a-tabs size="large" v-model:active-key="activeKey" @change="tabChange">
      <a-tab-pane v-for="item in tabs" :key="item.id" :tab="item.name">
        <template v-if="activeKey === '4'">
          <disk-info v-if="activeKey == 4" style="height: 100%"></disk-info>
        </template>
      </a-tab-pane>
    </a-tabs>
    <div>
      <div class="text-secondary text-[14px] mt-[24px] mb-[24px]">
        <span>上次更新时间：{{ lastUpdateTime }}</span>
        <a-divider type="vertical" />
        <span class="primary-btn" @click="handleUpdate">立即更新</span>
      </div>
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :loading="tableLoading"
        :scroll="{ y: 500, x: 1500 }"
        :pagination="false"
        @change="getInfoList"
      ></a-table>
    </div>
  </div>
</template>
<script setup>
import DiskInfo from './DiskInfo.vue'
// import usePageTable from '@/hooks/usePageTable'
import { getServerInfo, getTextInfo, getMoreInfo } from './apis'
import dayjs from 'dayjs'
onMounted(() => {
  getInfoList(activeKey.value)
})
const tabs = [
  { id: '1', name: '服务器信息' },
  { id: '2', name: 'JVM信息' },
  { id: '3', name: 'Undertow信息' },
  { id: '4', name: '磁盘监控' },
  { id: '5', name: '内存信息' }
]
// const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
// const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
//   onTableFetch({ pageNo: current ?? pageNo, pageSize })
// }

const columns = [
  { title: '参数', dataIndex: 'number', fixed: true },
  { title: '描述', dataIndex: 'bizDate' },
  { title: '当前值', dataIndex: 'customer_dictText' }
]
const activeKey = ref('1')
const tabChange = (key) => {
  if (key !== 4) {
    getInfoList(key)
  }
}
const lastUpdateTime = ref('')
const textInfo = ref({})
const moreInfo = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
// 加载信息
const getInfoList = (infoType) => {
  lastUpdateTime.value = dayjs().format('YYYY年MM月DD日 HH时mm分ss秒')
  getServerInfo(infoType).then((res) => {
    textInfo.value = getTextInfo(infoType)
    moreInfo.value = getMoreInfo(infoType)
    const info = []
    if (infoType === '5') {
      for (const param in res[0].result) {
        const data = res[0].result[param]
        const val = convert(data, unref(textInfo)[param].valueType)
        info.push({ id: param, param, text: 'false value', value: val })
      }
    } else {
      res.forEach((value, id) => {
        let more = unref(moreInfo)[value.name]
        if (!(more instanceof Array)) {
          more = ['']
        }
        more.forEach((item, idx) => {
          const param = value.name + item
          const val = convert(value.measurements[idx].value, unref(textInfo)[param].valueType)
          info.push({ id: param + id, param, text: 'false value', value: val })
        })
      })
    }
    dataSource.value = info
  })
}

// 单位转换
const convert = (value, type) => {
  if (type === 'Number') {
    return Number(value * 100).toFixed(2)
  } else if (type === 'Date') {
    return dayjs(value * 1000).format('YYYY-MM-DD HH:mm:ss')
  } else if (type === 'RAM') {
    return Number(value / 1048576).toFixed(3)
  }
  return value
}

const handleUpdate = () => {
  getInfoList(activeKey.value)
}
</script>
