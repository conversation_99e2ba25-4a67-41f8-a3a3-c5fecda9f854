<template>
  <div>
    <div ref="chartRef" style="width: 100%; height: 400px"></div>
  </div>
</template>
<script setup>
const props = defineProps({ data: {} })
const chartRef = ref(null)
const option = reactive({
  series: [
    {
      type: 'gauge',
      progress: {
        show: true,
        width: 18
      },
      axisLine: {
        lineStyle: {
          width: 18
        }
      },
      axisTick: {
        show: true
      },
      splitLine: {
        length: 15,
        lineStyle: {
          width: 2,
          color: '#999'
        }
      },
      axisLabel: {
        distance: 25,
        color: '#999',
        fontSize: 15
      },
      anchor: {
        show: true,
        showAbove: true,
        size: 25,
        itemStyle: {
          borderWidth: 10
        }
      },
      title: {},
      detail: {
        valueAnimation: true,
        fontSize: 50,
        formatter: '{value}%',
        offsetCenter: [0, '80%']
      },
      data: [
        {
          value: 70,
          name: '本地磁盘'
        }
      ]
    }
  ]
})

const initCharts = () => {
  option.series[0].data[0].name = props.data.name
  option.series[0].data[0].value = props.data.restPPT
}

onMounted(() => {
  initCharts()
})
</script>
