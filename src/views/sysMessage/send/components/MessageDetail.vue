<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" title="消息详情" width="1072px">
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">{{ detailData.esTitle }}</h2>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>{{ detailData.createBy_dictText || '-' }} 发送于 {{ detailData.createTime || '-' }}</span>
      </div>
      <div id="basic" class="mb-[40px]">
        <h2 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">基础信息</h2>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">接收人：{{ detailData.esReceiverName || '-' }}</span>
          <span class="w-[50%]">发送次数：{{ detailData.esSendNum || '-' }}</span>
          <span class="w-[50%]">发送时间：{{ detailData.esSendTime || '-' }}</span>
          <span class="w-[50%]">发送方式：{{ detailData.esType_dictText || '-' }}</span>
          <span class="w-[50%]">发送状态：{{ detailData.esSendStatus_dictText || '-' }}</span>
          <span class="w-[100%]" v-if="detailData.esSendStatus === '2'">
            失败原因：{{ detailData.esResult || '-' }}
          </span>
        </div>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">发送内容</h2>
      <a-typography-paragraph>
        <div class="p-[20px] bg-[#F5F5F5] rounded-[8px]" v-html="detailData.esContent"></div>
      </a-typography-paragraph>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { getMessageById } from '../apis'

const visible = ref(false)
const loading = ref(false)
const detailData = ref({})

/**
 * 打开详情抽屉
 */
const open = async (id) => {
  visible.value = true
  await loadDetail(id)
}

/**
 * 加载消息详情
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const { result } = await getMessageById({ id })
    detailData.value = result
  } finally {
    loading.value = false
  }
}

defineExpose({ open })
</script>
