import request from '@/apis/http'

/**
 * 消息发送-分页列表查询
 * @param {Object} params - 查询参数
 * @param {Object} params.sysMessage - 消息查询条件 (必填)
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 每页大小
 */
export const getMessageList = (params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessage/list',
    params
  })
}

/**
 * 消息发送-通过id查询
 * @param {Object} params - 查询参数
 * @param {string} params.id - 消息ID (必填)
 */
export const getMessageById = (params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessage/queryById',
    params
  })
}

/**
 * 消息发送-发送
 * @param {Object} data - 发送数据
 */
export const sendMessage = (data) => {
  return request({
    method: 'post',
    url: '/sys/message/sysMessage/send',
    data
  })
}

/**
 * 消息发送-重发
 * @param {Object} data - 重发数据
 */
export const resendMessage = (data) => {
  return request({
    method: 'post',
    url: '/sys/message/sysMessage/reSend',
    data
  })
}

/**
 * 消息管理-更新阅读状态
 * @param {Object} params - 参数
 * @param {string} params.id - 消息ID (必填)
 */
export const markAsRead = (params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessage/read',
    params
  })
}

/**
 * 消息管理-批量更新阅读状态
 * @param {Object} params - 参数
 * @param {string} params.ids - 消息ID列表，逗号分隔 (必填)
 */
export const batchMarkAsRead = (params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessage/readBatch',
    params
  })
}

/**
 * 消息管理-一键已读
 */
export const markAllAsRead = () => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessage/readAll'
  })
}

/**
 * 消息管理-获取未读消息数量
 */
export const getUnreadMessageCount = () => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessage/getUnreadMessageCount'
  })
}

/**
 * 消息发送-通过id删除
 * @param {Object} params - 删除参数
 * @param {string} params.id - 消息ID (必填)
 */
export const deleteMessage = (params) => {
  return request({
    method: 'delete',
    url: '/sys/message/sysMessage/delete',
    params
  })
}

/**
 * 消息发送-批量删除
 * @param {Object} params - 删除参数
 * @param {string} params.ids - 消息ID列表，逗号分隔 (必填)
 */
export const batchDeleteMessages = (params) => {
  return request({
    method: 'delete',
    url: '/sys/message/sysMessage/deleteBatch',
    params
  })
}

/**
 * 消息发送-导出Excel
 * @param {string} fileName - 文件名
 * @param {Object} params - 导出参数
 */
export const exportExcel = (fileName, params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessage/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
