import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

/**
 * 消息模板-分页列表查询
 * @param {Object} params - 查询参数
 * @param {Object} params.sysMessageTemplate - 消息模板查询条件 (必填)
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 每页大小
 */
export const getTemplateList = (params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessageTemplate/list',
    params
  })
}

/**
 * 消息模板-通过id查询
 * @param {Object} params - 查询参数
 * @param {string} params.id - 模板ID (必填)
 */
export const getTemplateById = (params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessageTemplate/queryById',
    params
  })
}

/**
 * 消息模板-添加
 * @param {Object} data - 模板数据
 */
export const addTemplate = (data) => {
  return request({
    method: 'post',
    url: '/sys/message/sysMessageTemplate/add',
    data
  })
}

/**
 * 消息模板-编辑
 * @param {Object} data - 模板数据
 */
export const updateTemplate = (data) => {
  return request({
    method: 'put',
    url: '/sys/message/sysMessageTemplate/edit',
    data
  })
}

/**
 * 消息模板-通过id删除
 * @param {Object} params - 删除参数
 * @param {string} params.id - 模板ID (必填)
 */
export const deleteTemplate = (params) => {
  return request({
    method: 'delete',
    url: '/sys/message/sysMessageTemplate/delete',
    params
  })
}

/**
 * 消息模板-批量删除
 * @param {Object} params - 删除参数
 * @param {string} params.ids - 模板ID列表，逗号分隔 (必填)
 */
export const batchDeleteTemplate = (params) => {
  return request({
    method: 'delete',
    url: '/sys/message/sysMessageTemplate/deleteBatch',
    params
  })
}

/**
 * 消息模板-测试发送消息
 * @param {Object} data - 发送数据
 */
export const sendTestMessage = (data) => {
  return request({
    method: 'post',
    url: '/sys/message/sysMessageTemplate/sendMsg',
    data
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/sys/message/sysMessageTemplate/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

export const importExcel = (data, controller) => {
  return advanceUpload('/sys/message/sysMessageTemplate/importExcel', data, controller)
}
