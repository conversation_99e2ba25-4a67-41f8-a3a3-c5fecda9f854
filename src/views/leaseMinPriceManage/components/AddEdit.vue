<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="ruleForm.id ? '编辑租赁底价规则' : '新建租赁底价规则'"
    placement="right"
    width="1072px"
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="ruleForm" ref="formRef" :rules="rules" :label-col="{ style: { width: '70px' } }" autocomplete="off">
      <div class="text-[16px] font-bold mb-[10px]">基础信息</div>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="规则名称" name="name">
            <a-input v-model:value="ruleForm.name" placeholder="请输入规则名称" allow-clear></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="优先级" name="priority">
            <dict-select
              v-model="ruleForm.priority"
              placeholder="请选择优先级"
              code="CT_BASE_ENUM_LeaseBasePriceManagement_Priority"
            ></dict-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="低价" name="basePrice">
            <a-input-number
              v-model:value="ruleForm.basePrice"
              :min="0"
              :precision="2"
              class="!w-[100%]"
              placeholder="请输入低价"
              addon-after="元"
            >
              <template #addonBefore>
                <dict-select
                  class="!w-[150px]"
                  v-model="ruleForm.basePriceUnit"
                  placeholder="请选择低价单位"
                  code="CT_BASE_ENUM_LeaseBasePriceManagement_BasePriceUnit"
                ></dict-select>
              </template>
            </a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="ruleForm.remark"
              placeholder="请输入备注"
              :maxlength="255"
              :rows="4"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>
      <div class="text-[16px] font-bold mb-[10px]">低价规则条件</div>
      <a-row :gutter="24">
        <a-col :span="4">
          <a-form-item label="" name="includeWyProject">
            <a-checkbox v-model:checked="ruleForm.includeWyProject">包含如下项目</a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="20" v-if="ruleForm.includeWyProject">
          <a-form-item label="" name="leaseBasePriceManagementWyProjectListIds">
            <a-form-item-rest>
              <f7-select
                v-model="ruleForm.leaseBasePriceManagementWyProjectListIds"
                f7-type="project"
                multiple
                placeholder="请选择项目"
                @change="leaseBasePriceManagementWyProjectListIdsChange"
              ></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="4">
          <a-form-item label="" name="includeLeaseUnit">
            <a-checkbox v-model:checked="ruleForm.includeLeaseUnit">包含如下租赁单元</a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="20" v-if="ruleForm.includeLeaseUnit">
          <a-form-item label="" name="leaseBasePriceManagementLeaseUnitListIds">
            <a-form-item-rest>
              <f7-select
                v-model="ruleForm.leaseBasePriceManagementLeaseUnitListIds"
                f7-type="leaseUnit"
                multiple
                placeholder="请选择租赁单元"
                @change="leaseBasePriceManagementLeaseUnitListIdsChange"
              ></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button type="primary" @click="handleConfirm(0)" :loading="submitLoading">提交</a-button>
      <a-button type="primary" ghost @click="handleStash" :loading="stashLoading">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>
<script setup>
import { message } from 'ant-design-vue'
import {
  stash,
  submit,
  edit,
  detailById,
  queryLeaseBasePriceManagementWyProjectByMainId,
  queryLeaseBasePriceManagementLeaseUnitByMainId
} from '../apis'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    getDetailById(data.id)
  }
}
defineExpose({ open })
// 获取详情
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  const data1 = await getQueryLeaseBasePriceManagementWyProjectByMainId(id)
  const data2 = await getQueryLeaseBasePriceManagementLeaseUnitByMainId(id)
  const obj = {
    leaseBasePriceManagementWyProjectListIds: data1.map((item) => item.wyProject),
    leaseBasePriceManagementWyProjectList: data1,
    leaseBasePriceManagementLeaseUnitListIds: data2.map((item) => item.leaseUnit),
    leaseBasePriceManagementLeaseUnitList: data2
  }
  Object.assign(ruleForm, result, obj)
}
// 获取详情
const getQueryLeaseBasePriceManagementWyProjectByMainId = async (id) => {
  const { result } = await queryLeaseBasePriceManagementWyProjectByMainId(id)
  return result
}
// 获取详情
const getQueryLeaseBasePriceManagementLeaseUnitByMainId = async (id) => {
  const { result } = await queryLeaseBasePriceManagementLeaseUnitByMainId(id)
  return result
}

const ruleForm = reactive({
  id: '',
  name: '',
  priority: '',
  basePriceUnit: '',
  basePrice: '',
  remark: '',
  includeWyProject: false,
  includeLeaseUnit: false,
  leaseBasePriceManagementWyProjectListIds: [],
  leaseBasePriceManagementWyProjectList: [],
  leaseBasePriceManagementLeaseUnitListIds: [],
  leaseBasePriceManagementLeaseUnitList: []
  // number: '',
  // status: '',
  // basePriceUnit: '',
  // createBy: '',
  // createTime: '',
  // updateBy: '',
  // updateTime: '',
  // auditBy: '',
  // auditTime: '',
  // sourceBillId: '',
  // sourceBillEntryId: '',
  // attachmentIds: '',
  // ctrlUnit: '',
})
const validateBasePrice = (rule, value) => {
  if (!ruleForm.basePriceUnit) return Promise.reject('请选择底价单位')
  if (!value) return Promise.reject('请输入低价')
  return Promise.resolve()
}
const rules = computed(() => ({
  name: [{ required: true, message: '请输入规则名称', trigger: ['change'] }],
  priority: [{ required: true, message: '请选择优先级', trigger: ['change'] }],
  basePrice: [{ required: true, validator: validateBasePrice, trigger: ['blur', 'change'] }]
}))
// 选择 项目
const leaseBasePriceManagementWyProjectListIdsChange = (ids, values) => {
  if (ids.length) {
    ruleForm.leaseBasePriceManagementWyProjectList = values.map((item) => {
      return {
        id: '',
        parent: item.parent,
        wyProject: item.id,
        remark: item.remark,
        ctrlUnit: item.ctrlUnit
      }
    })
    return
  }
  ruleForm.leaseBasePriceManagementLeaseUnitList = []
}
// 选择租赁单元
const leaseBasePriceManagementLeaseUnitListIdsChange = (ids, values) => {
  if (ids.length) {
    ruleForm.leaseBasePriceManagementLeaseUnitList = values.map((item) => {
      return {
        id: '',
        parent: item.parent,
        leaseUnit: item.id,
        remark: item.remark,
        ctrlUnit: item.ctrlUnit
      }
    })
    return
  }
  ruleForm.leaseBasePriceManagementLeaseUnitList = []
}
const formRef = ref()
const submitLoading = ref(false)
// 提交
const handleConfirm = async (type) => {
  await formRef.value.validate()
  submitLoading.value = true
  try {
    await submit(ruleForm)
    message.success('提交成功')
    emits('loadData')
    if (type) {
      return clearForm()
    }
    handleCancel()
  } finally {
    submitLoading.value = false
  }
}

// 暂存
const stashLoading = ref(false)
const handleStash = async () => {
  await formRef.value.validate()
  stashLoading.value = true
  try {
    const { message: msg, result } = await (ruleForm.id ? edit(ruleForm) : stash(ruleForm))
    message.success(msg)
    getDetailById(result)
    emits('loadData')
    stashLoading.value = false
  } finally {
    stashLoading.value = false
  }
}

const clearForm = () => {
  formRef.value.clearValidate()
}
// 取消
const handleCancel = () => {
  ruleForm.id = ''
  ruleForm.name = ''
  ruleForm.priority = ''
  ruleForm.basePriceUnit = ''
  ruleForm.basePrice = ''
  ruleForm.remark = ''
  ruleForm.includeWyProject = false
  ruleForm.includeLeaseUnit = false
  ruleForm.leaseBasePriceManagementWyProjectListIds = []
  ruleForm.leaseBasePriceManagementWyProjectList = []
  ruleForm.leaseBasePriceManagementLeaseUnitListIds = []
  ruleForm.leaseBasePriceManagementLeaseUnitList = []
  clearForm()
  visible.value = false
}
</script>

<style scoped lang="less">
// 自定义表头文本必填
.table-header-col {
  &::after {
    display: inline-block;
    content: '*';
    color: var(--color-red-600);
  }
}
// 移除按钮hover样式
.remove-btn {
  font-size: 16px;
  &:hover {
    color: var(--color-red-600);
  }
}
</style>
