<template>
  <a-drawer
    v-model:open="visible"
    title="租赁底价规则详情"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span v-if="detailData.status !== 'AUDITING'" class="primary-btn" @click="handleEdit">编辑</span>
        <a-dropdown>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down text-[12px]"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="handleDel">删除</div>
              </a-menu-item>
              <a-menu-item v-if="detailData.status !== 'ENABLE'">
                <div class="primary-btn" @click="handleUse(true)">启用</div>
              </a-menu-item>
              <a-menu-item v-if="detailData.status === 'ENABLE'">
                <div class="primary-btn" @click="handleUse(false)">禁用</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detailData.name }}</h2>
        <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detailData.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>规则编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>
      <div class="text-[16px] font-bold text-[#1D335C] mb-[12px]">基础信息</div>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">规则名称：{{ detailData.name || '-' }}</span>
        <span class="w-[50%]">优先级：{{ detailData.priority_dictText || '-' }}</span>
        <span class="w-[100%]">
          底价：{{
            renderDict(detailData.basePriceUnit, 'CT_BASE_ENUM_LeaseBasePriceManagement_BasePriceUnit') || '-'
          }}：{{ renderMoney(detailData.basePrice, 2) }}元
        </span>
        <span class="w-[100%]">备注：{{ detailData.remark || '-' }}</span>
      </div>
      <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">低价规则条件</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span v-if="detailData.includeWyProject" class="w-[100%]">包含如下项目：{{ detailData.wyProjectNames }}</span>
        <span v-if="detailData.includeLeaseUnit" class="w-[100%]">
          包含如下租赁单元：{{ detailData.LeaseUnitNames }}
        </span>
      </div>
    </a-spin>
  </a-drawer>
  <!-- 编辑 -->
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
</template>
<script setup>
import AddEdit from './AddEdit.vue'
import { Modal, message } from 'ant-design-vue'
import {
  detailById,
  delById,
  queryLeaseBasePriceManagementWyProjectByMainId,
  queryLeaseBasePriceManagementLeaseUnitByMainId
} from '../apis'
import { renderDict, renderMoney } from '@/utils/render'
const emits = defineEmits(['loadData', 'handleUse'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    getDetailById(data.id)
  }
}
defineExpose({ open })

// 通过id获取详情
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  result.loading = false
  result.checked = Boolean(result.status === 'ENABLE')
  const data1 = await queryLeaseBasePriceManagementWyProjectByMainId(id)
  const data2 = await queryLeaseBasePriceManagementLeaseUnitByMainId(id)
  result.wyProjectNames = data1.result.map((item) => item.wyProject_dictText).join('，')
  result.LeaseUnitNames = data2.result.map((item) => item.leaseUnit_dictText).join('，')
  detailData.value = result
}

const detailData = ref({
  leaseBasePriceManagementWyProjectList: [],
  leaseBasePriceManagementLeaseUnitList: []
})
const loading = ref(false)

// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
  handleClose()
}

// 删除
const handleDel = () => {
  Modal.confirm({
    title: '确认删除当前租赁底价规则？',
    content: '',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      handleClose()
    }
  })
}
// 禁用启用操作
const handleUse = (type) => {
  emits('handleUse', detailData.value, type)
  setTimeout(() => {
    getDetailById(detailData.value.id)
  }, 100)
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>
