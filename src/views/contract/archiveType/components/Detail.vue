<template>
  <a-drawer
    v-model:open="visible"
    title="归档类型详情"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span class="primary-btn" @click="handleEdit">编辑</span>
        <span class="primary-btn" @click="handleUpdateStatus">
          {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
        </span>
        <span class="primary-btn" @click="handleDelete">删除</span>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.name }}</h2>
        <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
      <div class="text-secondary flex flex-wrap gap-y-[12px]">
        <span class="w-1/2">名称: {{ detail.name }}</span>
        <span class="w-1/2">
          状态:
          <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status" type="dot"></status-tag>
        </span>
        <span class="w-full">说明: {{ detail.remark }}</span>
      </div>
      <h2 class="text-[16px] font-bold mt-[40px] mb-[16px]">归档类型列表</h2>
      <a-table
        :data-source="detail.fileFillTypeEntryList"
        :columns="columns"
        :pagination="false"
        :scroll="{ y: 'calc(100vh - 454px)' }"
      >
        <template #bodyCell="{ column, index }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
        </template>
      </a-table>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, updateStatus, deleteBatch, queryTypeList } from '../apis.js'
import { Modal, message } from 'ant-design-vue'

const emit = defineEmits(['edit', 'refresh'])

const visible = ref(false)
const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const loading = ref(false)
const detail = reactive({
  id: '',
  name: '',
  fileFillTypeEntryList: [],
  status: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: ''
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  const { result: list } = await queryTypeList({ id })
  detail.fileFillTypeEntryList = list
  loading.value = false
}

const columns = [
  { title: '序号', dataIndex: 'index', width: 80, fixed: 'left' },
  { title: '材料类别', dataIndex: 'materialType_dictText' },
  { title: '提交人', dataIndex: 'submitPerson_dictText' },
  { title: '接收人', dataIndex: 'receivePerson_dictText' },
  { title: '备注', dataIndex: 'remark' }
]

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleUpdateStatus = () => {
  Modal.confirm({
    title: `确认${detail.status === 'ENABLE' ? '禁用' : '启用'}该归档类型？`,
    content: detail.status === 'ENABLE' ? '禁用后将无法再被使用，但不影响已创建的数据。' : '',
    centered: true,
    onOk: async () => {
      await updateStatus({
        ids: detail.id,
        status: detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(detail.status === 'ENABLE' ? '已禁用' : '启用成功')
      detail.status = detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      emit('refresh')
    }
  })
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该归档类型？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>
