<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd" v-auth="'biz.contractmanage:ct_biz_clearing:add'">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'biz.contractmanage:ct_biz_clearing:importExcel'">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button
          :loading="exportLoading"
          @click="handleExport"
          v-auth="'biz.contractmanage:ct_biz_clearing:exportXls'"
        >
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button
          v-show="selectedRowKeys.length"
          @click="handleRemove(false)"
          v-auth="'biz.contractmanage:ct_biz_clearing:deleteBatch'"
        >
          批量删除
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.number"
          placeholder="搜索单据编号"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)" v-auth="'biz.contractmanage:ct_biz_clearing:view'">
            查看
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div class="primary-btn" @click="handleAudit(record, true)">审核(临时功能)</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleViewContract(record)">查看合同</div>
                </a-menu-item>
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div class="primary-btn" @click="handleWithdraw(record)">撤回</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleRemove(record)">删除</div>
                </a-menu-item>
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div class="primary-btn" @click="handleAudit(record, false)">反审核</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-dropdown v-if="record.status === 'AUDITOK' && record.sumResultAmount > 0">
            <span class="primary-btn">
              款项处理
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div class="primary-btn" @click="handleRefund(record, true)">退款</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleTransferDeduction(record)">转款抵扣</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleBillAdjust(record)">账单调整</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleWriteOff(record)">核销</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <span
            class="cursor-pointer text-error ml-[10px]"
            v-if="record.status === 'AUDITOK' && record.sumResultAmount < 0"
          >
            发催款通知
          </span>
        </template>
      </template>
    </a-table>
    <contract-detail ref="contractDetailRef" readonly></contract-detail>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail
      ref="detailRef"
      @edit="handleEdit"
      @withdraw="handleWithdraw"
      @viewContract="handleViewContract"
      @audit="handleAudit"
      @remove="handleRemove"
      @refund="handleRefund"
      @adjust="handleBillAdjust"
      @transferDeduction="handleTransferDeduction"
      @writeOff="handleWriteOff"
    ></detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('退租清算导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, audit, unAudit, withdraw } from './apis.js'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag, renderMoney } from '@/utils/render'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'
import { hasPermission } from '@/utils/permission'

const router = useRouter()

const params = reactive({
  column: 'number',
  order: 'desc',
  id: undefined,
  number: undefined,
  manageCompany: undefined,
  bizStatus: undefined,
  status: undefined,
  closingDate: undefined,
  handler: undefined,
  handlerDepart: undefined,
  clearingType: undefined,
  totalAmount: undefined,
  totalReceivable: undefined,
  totalDeductible: undefined,
  contract: undefined,
  signDate: undefined,
  customer: undefined,
  contractType: undefined,
  operator: undefined,
  operatorDepart: undefined,
  startDate: undefined,
  expireDate: undefined,
  leaseUnit: undefined,
  isLiquidatedDamages: undefined,
  liquidatedDamagesAmount: undefined,
  liquidatedDamagesRemark: undefined,
  actualDealAmount: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined
})
const searchList = [
  { label: '客户', name: 'customer', type: 'customer-select' },
  { label: '合同编号', name: 'contract', type: 'contract-select' },
  { label: '应收金额', name: 'receiveAmount', type: 'input' },
  { label: '应退金额', name: 'refundedAmount', type: 'input' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '业务状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '清算类型', name: 'clearingType', type: 'dict-select', code: 'CT_BASE_ENUM_Clearing_ClearingType' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
  { label: '经办人', name: 'handler', type: 'user-select' },
  { label: '租赁单元', name: 'leaseUnit', type: 'lease-unit-select' },
  { label: '计费截止时间', name: 'closingDate', type: 'date' },
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '创建时间', name: 'signDate', type: 'date' },
  { label: '备注', name: 'remark', type: 's-input' },
  { label: '单据ID', name: 'id', type: 'input' }
]

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 120 },
  { title: '合同编号', dataIndex: 'contract_dictText', width: 160 },
  { title: '应收金额', dataIndex: 'receiveAmount', width: 160, customRender: ({ text }) => renderMoney(text) },
  { title: '应退金额', dataIndex: 'refundedAmount', width: 160, customRender: ({ text }) => renderMoney(text) },
  {
    title: '已收待核销金额',
    dataIndex: 'paidWaitConsumedAmount',
    width: 160,
    customRender: ({ text }) => renderMoney(text)
  },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_Clearing_BizStatus', 'dot')
  },
  { title: '清算类型', dataIndex: 'clearingType_dictText', width: 140 },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 130 },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', width: 130 },
  { title: '计费截止时间', dataIndex: 'closingDate', width: 130 },
  { title: '备注', dataIndex: 'remark', width: 150, ellipsis: true },
  { title: '业务日期', dataIndex: 'bizDate', width: 130 },
  { title: '创建时间', dataIndex: 'createTime', width: 140 },
  { title: '操作', dataIndex: 'action', width: 210, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_clearing:edit')) return
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_clearing:view')) return
  detailRef.value.open(data.id)
}

const contractDetailRef = ref()
const handleViewContract = (data) => {
  if (!hasPermission('biz.contractmanage:ct_con_lease:view')) return
  contractDetailRef.value.open(data.contract)
}

// 账单调整
const handleBillAdjust = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_receive_amount_adjust:add')) return
  sessionStorage.setItem('contractId', data.contract)
  router.push({ path: '/contract/billAdjust' })
}

// 申请退款
const handleRefund = () => {
  router.push({ path: '/finance/refund' })
}
// 申请转款抵扣
const handleTransferDeduction = () => {
  router.push({ path: '/finance/transferDeduction' })
}

// 进行收款核销
const handleWriteOff = () => {
  router.push({ path: '/writeOff/records' })
}

const handleWithdraw = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_clearing:edit')) return
  Modal.confirm({
    title: '是否确认撤回该合同退租清算申请？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await withdraw({ id: data.id })
      message.success('已撤回')
      onTableChange(pagination.value)
      if (detailRef.value && detailRef.value.visible) {
        detailRef.value.loadDetail(data.id)
      }
    }
  })
}

const handleAudit = async (data, result) => {
  if (result && !hasPermission('biz.contractmanage:ct_biz_clearing:audit')) return
  if (!result && !hasPermission('biz.contractmanage:ct_biz_clearing:unAudit')) return
  result ? await audit({ id: data.id }) : await unAudit({ id: data.id })
  message.success(result ? '审核成功' : '反审核成功')
  onTableChange(pagination.value)
  if (detailRef.value && detailRef.value.visible) {
    detailRef.value.loadDetail(data.id)
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_clearing:delete')) return
  Modal.confirm({
    title: '确认删除该合同退租清算？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
      if (data && detailRef.value && detailRef.value.visible) {
        detailRef.value.handleClose()
      }
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('退租清算数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  const id = sessionStorage.getItem('clearingId')
  if (id) {
    params.id = id
    sessionStorage.removeItem('clearingId')
  }
  onTableChange()
})
</script>
