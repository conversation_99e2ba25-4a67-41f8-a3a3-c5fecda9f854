<template>
  <a-drawer
    v-model:open="visible"
    class="edit-exit-clearing-drawer common-drawer"
    title="合同退租清算申请"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <h2 class="text-[16px] font-bold mb-[20px]">基础信息</h2>
      <a-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-col="{ style: { width: '94px' } }"
        label-align="left"
        autocomplete="off"
      >
        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker v-model:value="form.bizDate" value-format="YYYY-MM-DD" style="width: 100%"></a-date-picker>
        </a-form-item>
        <a-form-item label="经办人" name="handler">
          <a-form-item-rest>
            <f7-select
              v-model="form.handler"
              f7-type="user"
              :depart-id="form.handlerDepart"
              relation-depart
            ></f7-select>
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="经办部门" name="handlerDepart">
          <depart-select v-model="form.handlerDepart" @change="onDepartChange"></depart-select>
        </a-form-item>
        <a-form-item label="计费截止日期" name="closingDate">
          <a-date-picker
            v-model:value="form.closingDate"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            @change="onDateChange"
          ></a-date-picker>
        </a-form-item>
        <a-form-item label="清算类型" name="clearingType">
          <dict-select v-model="form.clearingType" code="CT_BASE_ENUM_Clearing_ClearingType" disabled></dict-select>
        </a-form-item>
        <div></div>
        <a-form-item label="合同" name="contract" class="form-item-full">
          <a-form-item-rest>
            <f7-select
              v-model="form.contract"
              f7-type="contract"
              @change="loadContractDetail"
              width="382px"
            ></f7-select>
          </a-form-item-rest>
        </a-form-item>
        <div class="flex flex-wrap gap-[12px] text-secondary mb-[24px] pl-[94px]" v-if="contractDetail.id">
          <span class="w-[calc(50%-6px)]">签约客户: {{ contractDetail.customer_dictText }}</span>
          <span class="w-[calc(50%-6px)]">签约日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">物业管理公司: {{ contractDetail.manageCompany_dictText }}</span>
          <span class="w-[calc(50%-6px)]">合同类型: {{ contractDetail.contractType_dictText }}</span>
          <span class="w-[calc(50%-6px)]">业务员: {{ contractDetail.operator_dictText }}</span>
          <span class="w-[calc(50%-6px)]">租赁单元: {{ contractDetail.leaseUnit_dictText }}</span>
          <span class="w-[calc(50%-6px)]">合同开始日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">合同结束日期: {{ contractDetail.expireDate }}</span>
        </div>
        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea
            v-model:value="form.remark"
            show-count
            :maxlength="255"
            :auto-size="{ minRows: 4, maxRows: 4 }"
          ></a-textarea>
        </a-form-item>
        <div class="w-full mb-[20px]" v-if="billList.length">
          <h4 class="text-[16px] font-bold mt-[40px] mb-[16px] w-full">清算明细</h4>
          <div class="flex gap-x-[16px]">
            <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
              <div class="detail-item-header">
                <span>应收</span>
                <strong class="clearing-money">{{ billList[0].receiveAmount }}</strong>
              </div>
              <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
                <li
                  class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                  v-for="item in billList[0].detailBillEntryVOList"
                  :key="item.id"
                >
                  <div>
                    <p>{{ item.incomeBelongYm }} {{ item.paymentType_dictText }}</p>
                    <small class="text-tertiary text-[12px]">
                      {{ item.receiveBeginDate }} - {{ item.receiveEndDate }}
                    </small>
                  </div>
                  <div>
                    <p class="text-right">{{ item.paymentAmount }}</p>
                    <small class="text-tertiary text-[12px]">
                      应收{{ item.actualReceiveAmount }} | 已收{{ item.paid }}
                    </small>
                  </div>
                </li>
              </ul>
              <a-empty
                description="暂无应收数据"
                class="py-[20px]"
                v-show="!billList[0].detailBillEntryVOList.length"
              ></a-empty>
            </section>
            <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
              <div class="detail-item-header">
                <span>应退</span>
                <strong class="clearing-money">{{ billList[1].refundedAmount }}</strong>
              </div>
              <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
                <li
                  class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                  v-for="item in billList[1].detailBillEntryVOList"
                  :key="item.id"
                >
                  <div>
                    <p>{{ item.paymentType_dictText }}</p>
                    <small class="text-tertiary text-[12px]">{{ item.number }}</small>
                  </div>
                  <div>
                    <p class="text-right">{{ item.paymentAmount }}</p>
                    <small class="text-tertiary text-[12px]">
                      应收{{ item.actualReceiveAmount }} | 已收{{ item.paid }}
                    </small>
                  </div>
                </li>
              </ul>
              <a-empty
                description="暂无应退数据"
                class="py-[20px]"
                v-show="!billList[1].detailBillEntryVOList.length"
              ></a-empty>
            </section>
            <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
              <div class="detail-item-header">
                <span>已收待核销</span>
                <strong class="clearing-money">{{ billList[2].paidWaitConsumedAmount }}</strong>
              </div>
              <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
                <li
                  class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                  v-for="item in billList[2].payExplainBookVOList"
                  :key="item.id"
                >
                  <div>
                    <p>{{ item.receiveDate }}</p>
                    <small class="text-tertiary text-[12px]">{{ item.number }}</small>
                  </div>
                  <div>
                    <p class="text-right">{{ item.sumWaitConsumedAmt }}</p>
                    <small class="text-tertiary text-[12px]">
                      到账{{ item.sumAmt }} | 已核销{{ item.sumConsumedAmt }}
                    </small>
                  </div>
                </li>
              </ul>
              <a-empty
                description="暂无已收待核销数据"
                class="py-[20px]"
                v-show="!billList[2].payExplainBookVOList.length"
              ></a-empty>
            </section>
          </div>
        </div>
        <div>
          <a-checkbox v-model:checked="form.isLiquidatedDamages" @change="onCheckboxChange">存在违约</a-checkbox>
        </div>
        <div v-if="form.isLiquidatedDamages" class="w-full mt-[20px]">
          <a-form-item label="违约金额" name="liquidatedDamagesAmount">
            <a-input v-model:value="form.liquidatedDamagesAmount">
              <template #suffix>元</template>
            </a-input>
          </a-form-item>
          <a-form-item label="违约说明" name="liquidatedDamagesRemark" class="form-item-full">
            <a-textarea
              v-model:value="form.liquidatedDamagesRemark"
              show-count
              :maxlength="255"
              :auto-size="{ minRows: 4, maxRows: 4 }"
            ></a-textarea>
          </a-form-item>
        </div>
      </a-form>
      <div
        class="mt-[20px] rounded-[8px] border-[#e6e9f0] border-solid border bg-[#f7f8fa] p-[16px]"
        v-if="computedResult.title"
      >
        <div class="flex items-center">
          <span>{{ computedResult.title }}</span>
          <strong class="clearing-money">{{ computedResult.result }}</strong>
        </div>
        <div class="mt-[16px] text-secondary">{{ computedResult.description }}</div>
      </div>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        :loading="confirmLoading"
        @click="handleSubmit"
        v-auth="'biz.contractmanage:ct_biz_clearing:submit'"
      >
        提交
      </a-button>
      <a-button
        type="primary"
        :loading="saveLoading"
        ghost
        @click="handleSave"
        v-if="['', 'TEMP'].includes(form.status)"
      >
        暂存
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, submit, detail, save, getBillDetail } from '../apis.js'
import { detail as getContractDetail, queryContractLeaseUnits } from '@/views/contract/management/apis'
import { message } from 'ant-design-vue'
import { validateMoney } from '@/utils/validate'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import Decimal from 'decimal.js'

const emit = defineEmits(['refresh'])

const { userInfo } = useUserStore()

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  } else {
    form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD')
    form.closingDate = form.bizDate
    form.handler = userInfo.value.id
    form.handlerDepart = userInfo.value.currentDepart
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    form[key] = result[key]
  }
  if (result.contract) {
    loadContractDetail(result.contract)
  }
  loading.value = false
}

const form = reactive({
  id: '',
  number: '',
  manageCompany: '',
  closingDate: '',
  handler: '',
  handlerDepart: '',
  clearingType: '',
  contract: '',
  isLiquidatedDamages: false,
  liquidatedDamagesAmount: '',
  liquidatedDamagesRemark: '',
  signDate: '',
  customer: '',
  contractType: '',
  operator: '',
  operatorDepart: '',
  startDate: '',
  expireDate: '',
  leaseUnit: '',
  remark: '',
  status: ''
})

const rules = {
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  handler: [{ required: true, message: '请选择经办人', trigger: 'change' }],
  handlerDepart: [{ required: true, message: '请选择经办部门', trigger: 'change' }],
  closingDate: [{ required: true, message: '请选择计费截止日期', trigger: 'change' }],
  contract: [{ required: true, message: '请选择合同', trigger: 'change' }],
  clearingType: [{ required: true, message: '请选择清算类型', trigger: 'change' }],
  liquidatedDamagesAmount: [{ required: true, validator: validateMoney(true, '违约金额'), trigger: 'blur' }],
  liquidatedDamagesRemark: [{ required: true, message: '请填写违约说明', trigger: 'blur' }]
}

const onDepartChange = () => {
  form.handler = ''
}

const contractDetail = reactive({})
const loadContractDetail = async (id) => {
  const { result } = await getContractDetail({ id })
  Object.assign(contractDetail, result)
  form.contract = id
  form.signDate = result.signDate
  form.customer = result.customer
  form.contractType = result.contractType
  form.contractNumber = result.contractNumber
  form.manageCompany = result.manageCompany
  form.operator = result.operator
  form.operatorDepart = result.operatorDepart
  form.startDate = result.startDate
  form.expireDate = result.expireDate
  const { result: list } = await queryContractLeaseUnits({ id })
  form.leaseUnit = list.map((item) => item.leaseUnit).join(',')
  contractDetail.leaseUnit_dictText = list.map((item) => item.leaseUnit_dictText).join(',')
  if (form.closingDate && form.contract) {
    form.clearingType = new Date(contractDetail.expireDate) > new Date(form.closingDate) ? '-1' : '0'
    loadBillDetail()
  }
}

// 监听计费截止日期的选择
const onDateChange = () => {
  if (form.closingDate) {
    if (form.contract) {
      form.clearingType = new Date(contractDetail.expireDate) > new Date(form.closingDate) ? '-1' : '0'
      loadBillDetail()
    }
  }
}

// 清算明细列表
const billList = ref([])
const loadBillDetail = async () => {
  const { result } = await getBillDetail(form)
  billList.value = result.clearingDetailBillVOList
}

// 计算结果
const computedResult = reactive({
  title: '',
  result: 0,
  description: ''
})

watch([billList, () => form.liquidatedDamagesAmount], ([list, money]) => {
  if (!(list && list.length)) {
    computedResult.title = ''
    computedResult.description = ''
    return
  }
  const paidWaitConsumedAmount = list[2].paidWaitConsumedAmount // 累计已收待核销
  const refundedAmount = list[1].refundedAmount // 累计应退
  const receiveAmount = list[0].receiveAmount // 累计应收
  const liquidatedDamagesAmount = money ? Number(money) : 0 // 违约金
  const result = new Decimal(paidWaitConsumedAmount)
    .plus(new Decimal(refundedAmount))
    .sub(new Decimal(receiveAmount))
    .sub(new Decimal(liquidatedDamagesAmount))
    .toNumber()
  if (result === 0) {
    computedResult.title = '已结清'
    computedResult.result = 0
    computedResult.description = ''
    return
  }
  computedResult.title = result > 0 ? '应退: ' : '合计欠款: '
  computedResult.result = Math.abs(result)
  const str = liquidatedDamagesAmount ? ` - [违约金]${liquidatedDamagesAmount}` : '' // 违约金信息
  computedResult.description = `[累计已收待核销]${paidWaitConsumedAmount} + [累计应退]${refundedAmount} - [累计应收]${receiveAmount}${str} = ${result}`
})

// 勾选了存在违约以后，需要把页面滚动到最底部
const onCheckboxChange = async (val) => {
  if (val) {
    await nextTick()
    const body = document.querySelector('.edit-exit-clearing-drawer .ant-drawer-body')
    body.scrollTop = body.scrollHeight - body.clientHeight
  }
}

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  const fields = []
  for (const key in rules) {
    if (form[key]) {
      fields.push(key)
    }
  }
  await formRef.value.validateFields(fields)
  try {
    saveLoading.value = true
    if (form.id) {
      await edit(form)
    } else {
      const { result } = await save(form)
      form.id = result.id
      form.number = result.number
      form.status = result.status
    }
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  try {
    confirmLoading.value = true
    await submit(form)
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (typeof form[key] === 'boolean') {
      form[key] = false
    } else {
      form[key] = ''
    }
  }
  contractDetail.id = ''
  billList.value = []
  formRef.value.clearValidate()
  visible.value = false
}

onMounted(() => {
  const contractId = sessionStorage.getItem('clearing-contractId')
  if (contractId) {
    open()
    form.contract = contractId
    loadContractDetail(contractId)
    sessionStorage.removeItem('clearing-contractId')
  }
})

defineExpose({ open })
</script>

<style lang="less">
.edit-exit-clearing-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 40px;
  }
  .ant-form-item {
    width: calc(50% - 20px);
  }
  .form-item-full {
    width: 100%;
  }
  .detail-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e0e0e0;
    padding: 16px;
    background-color: #f7f8fa;
    & > span {
      font-size: 16px;
    }
  }
  .clearing-money {
    color: var(--color-error);
    font-size: 16px;
    line-height: 1;
    margin-left: 10px;
    &::before {
      content: '￥';
      font-size: 12px;
    }
  }
}
</style>
