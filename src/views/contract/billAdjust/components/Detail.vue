<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="合同账单调整详情"
    placement="right"
    width="1072px"
    destroy-on-close
    :root-style="{ zIndex: 900 }"
    @close="handleClose"
  >
    <template #extra>
      <a-dropdown>
        <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
          <span>操作</span>
          <i class="a-icon-arrow-down ml-[4px]"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item><div class="primary-btn" @click="handleAudit(true)">审核(临时功能)</div></a-menu-item>
            <a-menu-item><div class="primary-btn" @click="handleViewContract">查看合同</div></a-menu-item>
            <a-menu-item v-if="['AUDITING'].includes(detail.status)">
              <div class="primary-btn" @click="handleWithdraw">撤回</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
              <div class="primary-btn" @click="handleEdit">编辑</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK'].includes(detail.status)">
              <div class="primary-btn" @click="handleRemove">删除</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITOK'].includes(detail.status)">
              <div class="primary-btn" @click="handleAudit(detail, false)">反审核</div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">合同账单调整</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #base>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
            <span class="w-[50%]">合同编码: {{ detail.contractNumber }}</span>
            <span class="w-[50%]">签约客户: {{ detail.customer_dictText }}</span>
            <span class="w-[50%]">签约日期: {{ detail.signDate }}</span>
            <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
            <span class="w-[50%]">合同类型: {{ detail.contractType_dictText }}</span>
            <span class="w-[50%]">业务员: {{ detail.operator_dictText }}</span>
            <span class="w-[50%]">业务部门: {{ detail.operatorDepart_dictText }}</span>
            <span class="w-[50%]">合同开始日期: {{ detail.startDate }}</span>
            <span class="w-[50%]">合同结束日期: {{ detail.expireDate }}</span>
          </div>
        </template>
        <template #detail>
          <a-table :data-source="detail.billList" :columns="columns" :pagination="false" bordered :scroll="{ x: 1800 }">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'addReduceAmount'">
                <span v-if="record.addReduceType === 'Add'" class="text-success">
                  {{ record.addReduceType_dictText }}{{ renderMoney(record.addReduceAmount) }}
                </span>
                <span v-if="record.addReduceType === 'Reduce'" class="text-error">
                  {{ record.addReduceType_dictText }}{{ renderMoney(record.addReduceAmount) }}
                </span>
              </template>
            </template>
          </a-table>
        </template>
        <template #attachment>
          <file-list :biz-id="detail.id"></file-list>
        </template>
        <template #result>
          <div
            class="border border-solid border-[#e0e0e0] p-[16px] rounded-[8px]"
            v-if="computedResult.title && detail.status !== 'TEMP'"
          >
            <div class="flex items-center justify-between">
              <strong class="text-[18px]">{{ computedResult.title }}</strong>
              <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status" type="dot"></status-tag>
            </div>
            <div class="mt-[16px] text-secondary">{{ computedResult.description }}</div>
          </div>
          <div v-if="detail.status === 'TEMP'" class="text-tertiary">暂存状态下没有处理进展</div>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, adjustDetail } from '../apis.js'
import Decimal from 'decimal.js'
import { renderMoney, renderBoolean } from '@/utils/render'

const emit = defineEmits(['edit', 'withdraw', 'viewContract', 'audit', 'remove'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadData(id)
}

const loading = ref(false)
const loadData = async (id) => {
  loading.value = true
  await Promise.all([loadDetail(id), loadBillList(id)])
  loading.value = false
}

const detail = reactive({
  id: '',
  actualDealAmount: '',
  bizDate: '',
  contract: '',
  contractType_dictText: '',
  contract_dictText: '',
  createBy_dictText: '',
  createTime: '',
  customer_dictText: '',
  expireDate: '',
  leaseUnit_dictText: '',
  manageCompany_dictText: '',
  number: '',
  operator_dictText: '',
  operatorDepart_dictText: '',
  signDate: '',
  startDate: '',
  status: '',
  billList: [] // 账单明细
})
const loadDetail = async (id) => {
  const { result } = await getDetail({ id })
  for (const key in detail) {
    if (key !== 'billList') {
      detail[key] = result[key]
    }
  }
}

const loadBillList = async (id) => {
  const { result } = await adjustDetail({ id })
  detail.billList = result
  handleCompute()
}

const tabList = [
  { title: '基础信息', name: 'base' },
  { title: '调整明细', name: 'detail' },
  { title: '附件信息', name: 'attachment' },
  { title: '处理进展', name: 'result' }
]

const columns = [
  { title: '单据编号', dataIndex: 'detailBill', width: 200, fixed: 'left' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  { title: '是否押金', dataIndex: 'isDeposit', customRender: ({ text }) => renderBoolean(text) },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text) },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text) },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  { title: '增减金额', dataIndex: 'addReduceAmount' },
  { title: '调整原因', dataIndex: 'adjustRemark', width: 200, fixed: 'right' }
]

const computedResult = reactive({
  title: '', // 显示应退: xx元/调整后待核销合计: xx元
  description: '' // [累计款项金额] 6030.00 - [本次减免金额] 1600.00 + [本次增加金额] 520.00 - [累计已核销] 6500.00 = -1550.00
})
// 结算结果
const handleCompute = () => {
  if (!detail.billList.length) {
    computedResult.title = ''
    computedResult.description = ''
  }
  let total = new Decimal(0) // 实际应收金额
  let reduce = new Decimal(0) // 本次减免金额
  let add = new Decimal(0) // 本次增加金额
  let receive = new Decimal(0) // 已收金额
  detail.billList.forEach((item) => {
    total = total.plus(new Decimal(item.paymentAmount))
    receive = receive.plus(new Decimal(item.paid))
    if (item.addReduceType === 'Add') {
      add = add.plus(new Decimal(Number(item.addReduceAmount)))
    } else {
      reduce = reduce.plus(new Decimal(Number(item.addReduceAmount)))
    }
  })
  let result
  if (reduce.toNumber() >= add.toNumber()) {
    // 显示: 已收金额+本次减免金额-实际应收金额
    result = receive.plus(reduce).sub(total).toNumber()
    computedResult.description = `[已收金额]${renderMoney(receive.toNumber())} + [本次减免金额]${renderMoney(reduce.toNumber())} - [实际应收金额]${renderMoney(total.toNumber())} = ${renderMoney(result)}`
  } else {
    // 显示: 已收金额-本次增加金额-实际应收金额
    result = receive.sub(add).sub(total).toNumber()
    computedResult.description = `[已收金额]${renderMoney(receive.toNumber())} - [本次增加金额]${renderMoney(add.toNumber())} - [实际应收金额]${renderMoney(total.toNumber())} = ${renderMoney(result)}`
  }
  computedResult.title =
    result > 0 ? `应退: ${renderMoney(result)}` : `调整后应收款合计: ${renderMoney(Math.abs(result))}`
}

const handleEdit = () => {
  emit('edit', detail)
  handleClose()
}

const handleViewContract = () => {
  emit('viewContract', detail)
}

const handleWithdraw = () => {
  emit('withdraw', detail)
}

const handleAudit = (result) => {
  emit('audit', detail, result)
}
const handleRemove = () => {
  emit('remove', detail)
}

const handleClose = () => {
  visible.value = false
  detail.billList = [] // 账单明细
}

defineExpose({ open, visible, loadData, handleClose })
</script>
