import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/receiveAmountAdjust/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/receiveAmountAdjust/queryById',
    params
  })
}

export const adjustDetail = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/receiveAmountAdjust/queryReceiveAmountAdjustDetailBillByMainId',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/receiveAmountAdjust/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/receiveAmountAdjust/importExcel',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/receiveAmountAdjust/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/receiveAmountAdjust/unAudit',
    data
  })
}

export const save = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/receiveAmountAdjust/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/receiveAmountAdjust/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/receiveAmountAdjust/edit',
    data
  })
}

export const withdraw = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/receiveAmountAdjust/back',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/contractmanage/receiveAmountAdjust/deleteBatch',
    params
  })
}

export const billPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/receiveAmountAdjust/f7ReceiveAmountAdjustDetailList',
    params
  })
}
