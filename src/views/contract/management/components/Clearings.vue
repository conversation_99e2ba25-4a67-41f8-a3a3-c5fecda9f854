<template>
  <div class="w-full mb-[20px]" v-if="billList.length">
    <h4 class="text-[16px] font-bold mb-[16px] w-full">清算明细</h4>
    <div class="flex gap-x-[16px]">
      <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
        <div class="detail-item-header">
          <span>应收</span>
          <strong class="clearing-money">{{ renderMoney(billList[0].receiveAmount) }}</strong>
        </div>
        <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
          <li
            class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
            v-for="item in billList[0].detailBillEntryVOList"
            :key="item.id"
          >
            <div>
              <p>{{ item.incomeBelongYm }} {{ item.paymentType_dictText }}</p>
              <small class="text-tertiary text-[12px]">{{ item.receiveBeginDate }} - {{ item.receiveEndDate }}</small>
            </div>
            <div>
              <p class="text-right">{{ renderMoney(item.paymentAmount) }}</p>
              <small class="text-tertiary text-[12px]">
                应收{{ renderMoney(item.actualReceiveAmount) }} | 已收{{ renderMoney(item.paid) }}
              </small>
            </div>
          </li>
        </ul>
        <a-empty
          description="暂无应收数据"
          class="py-[20px]"
          v-show="!billList[0].detailBillEntryVOList.length"
        ></a-empty>
      </section>
      <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
        <div class="detail-item-header">
          <span>应退</span>
          <strong class="clearing-money">{{ renderMoney(billList[1].refundedAmount) }}</strong>
        </div>
        <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
          <li
            class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
            v-for="item in billList[1].detailBillEntryVOList"
            :key="item.id"
          >
            <div>
              <p>{{ item.paymentType_dictText }}</p>
              <small class="text-tertiary text-[12px]">{{ item.number }}</small>
            </div>
            <div>
              <p class="text-right">{{ item.paymentAmount }}</p>
              <small class="text-tertiary text-[12px]">
                应收{{ renderMoney(item.actualReceiveAmount) }} | 已收{{ renderMoney(item.paid) }}
              </small>
            </div>
          </li>
        </ul>
        <a-empty
          description="暂无应退数据"
          class="py-[20px]"
          v-show="!billList[1].detailBillEntryVOList.length"
        ></a-empty>
      </section>
      <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
        <div class="detail-item-header">
          <span>已收待核销</span>
          <strong class="clearing-money">{{ renderMoney(billList[2].paidWaitConsumedAmount) }}</strong>
        </div>
        <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
          <li
            class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
            v-for="item in billList[2].payExplainBookVOList"
            :key="item.id"
          >
            <div>
              <p>{{ item.receiveDate }}</p>
              <small class="text-tertiary text-[12px]">{{ item.number }}</small>
            </div>
            <div>
              <p class="text-right">{{ renderMoney(item.sumWaitConsumedAmt) }}</p>
              <small class="text-tertiary text-[12px]">
                到账{{ renderMoney(item.sumAmt) }} | 已核销{{ renderMoney(item.sumConsumedAmt) }}
              </small>
            </div>
          </li>
        </ul>
        <a-empty
          description="暂无已收待核销数据"
          class="py-[20px]"
          v-show="!billList[2].payExplainBookVOList.length"
        ></a-empty>
      </section>
    </div>
  </div>
  <div v-if="detail.liquidatedDamagesAmount">
    <h2 class="text-[16px] font-bold mt-[40px] mb-[16px]">违约情况</h2>
    <div class="text-secondary">
      <span>违约金额: {{ renderMoney(detail.liquidatedDamagesAmount) }}</span>
      <p class="mt-[12px]">违约说明: {{ detail.liquidatedDamagesRemark }}</p>
    </div>
  </div>
  <template v-if="detail.sumResultStatus">
    <h2 class="text-[16px] font-bold mt-[40px] mb-[12px]">处理方式及进展</h2>
    <div
      class="rounded-[8px] border-[#e6e9f0] border-solid border bg-[#f7f8fa] p-[16px] flex items-center justify-between"
    >
      <div>
        <div class="flex items-center mb-[8px]">
          <span>{{ detail.sumResultStatus }}:</span>
          <strong class="clearing-money">{{ renderMoney(detail.sumResultAmount) }}</strong>
        </div>
        <div class="text-secondary">{{ detail.sumResultRemark }}</div>
      </div>
      <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
    </div>
  </template>
</template>

<script setup>
import { getClearingDetail } from '../apis.js'
import { renderMoney } from '@/utils/render'

const { contractId } = defineProps({
  contractId: { required: true, type: String }
})

const detail = reactive({
  status: '',
  sumResultAmount: 0
})
const billList = computed(() => detail.clearingDetailBillVOList || [])

const loading = ref(false)
const loadDetail = async () => {
  loading.value = true
  const { result } = await getClearingDetail({ id: contractId })
  Object.assign(detail, result)
  loading.value = false
}

watch(
  () => contractId,
  (val) => {
    if (val) {
      loadDetail()
    }
  }
)
</script>

<style lang="less" scoped>
.detail-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px;
  background-color: #f7f8fa;
  & > span {
    font-size: 16px;
  }
}
.clearing-money {
  color: var(--color-error);
  font-size: 16px;
  line-height: 1;
  margin-left: 10px;
  &::before {
    content: '￥';
    font-size: 12px;
  }
}
</style>
