<template>
  <a-drawer
    v-model:open="visible"
    class="edit-contract-drawer common-drawer"
    :title="dialogTitle"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <circle-steps
        :current="current"
        :step-list="['基础信息及租赁单元', '合同款项', '账单明细', '附件信息']"
        width="900px"
        class="mx-auto mb-[40px]"
      ></circle-steps>
      <step-1 ref="step1Ref" :form="form" v-show="current === 1"></step-1>
      <step-2 ref="step2Ref" :form="form" v-show="current === 2" @toPriceIncreasePage="toPriceIncreasePage"></step-2>
      <step-3 ref="step3Ref" :form="form" v-show="current === 3"></step-3>
      <step-4 ref="step4Ref" :form="form" v-show="current === 4"></step-4>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        :loading="confirmLoading"
        @click="handleSubmit"
        v-auth="'biz.contractmanage:ct_con_lease:submit'"
      >
        提交
      </a-button>
      <a-button @click="handleChangeStep('previous')" v-show="current > 1">上一步</a-button>
      <a-button type="primary" @click="handleChangeStep('next')" v-show="current < 4">下一步</a-button>
      <a-button
        type="primary"
        :loading="saveLoading"
        ghost
        @click="handleSave"
        v-if="operateType === 'edit' && ['', 'TEMP'].includes(form.status)"
      >
        暂存
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import {
  edit,
  submit,
  detail,
  save,
  queryBillList,
  queryContractLeaseUnits,
  queryContractLeaseFunds,
  queryOverlayContractList
} from '../apis.js'
import { message, Modal } from 'ant-design-vue'
import Step1 from './Step1.vue'
import Step2 from './Step2.vue'
import Step3 from './Step3.vue'
import Step4 from './Step4.vue'
import Decimal from 'decimal.js'
import dayjs from 'dayjs'
import userStore from '@/store/modules/user.js'

const emit = defineEmits(['refresh'])

const router = useRouter()

const { userInfo } = userStore()

const step1Ref = ref()
const step2Ref = ref()
const step3Ref = ref()

const visible = ref(false)
const operateType = ref('edit') // edit=新增/编辑 change=合同变更 renew=合同续签
const dialogTitle = computed(() => {
  if (operateType.value === 'edit') return form.id ? '编辑合同' : '新建合同'
  return operateType.value === 'change' ? '变更合同' : '续签合同'
})

/**
 * 打开弹窗
 * @param id 合同id，用于查询合同详情
 * @param opeType edit=新增/编辑 change=合同变更 renew=合同续签
 * @param params 合同变更时需要的额外参数
 */
const open = async (id, opeType = 'edit', params) => {
  visible.value = true
  operateType.value = opeType
  if (id) {
    await loadData(id)
    if (opeType === 'change') {
      form.initContract = params.initContract
      form.originalContract = params.originalContract
      form.changeReason = params.changeReason
      form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD')
      form.contractNumber = `${form.contractNumber}-1`
      form.number = ''
    } else if (operateType.value === 'renew') {
      form.contractNumber = ''
      form.signDate = ''
      form.startDate = ''
      form.expireDate = ''
      form.attachmentIds = ''
      form.number = ''
      form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD')
    }
  } else {
    form.manageCompany = userInfo.value.currentCompany || ''
    form.operator = userInfo.value.id || ''
    form.operatorDepart = userInfo.value.currentDepart
    form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD') // 业务日期默认取当天
  }
}

const current = ref(1)

const loading = ref(false)
const loadData = async (id) => {
  loading.value = true
  await Promise.all([loadDetail(id), loadLeaseUnits(id), loadFundList(id)])
  loading.value = false
}

// 获取合同详情数据
const loadDetail = async (id) => {
  const { result } = await detail({ id })
  for (const key in form) {
    if (!Array.isArray(form[key]) && !['attachmentIds'].includes(key)) {
      form[key] = result[key]
    }
  }
  if (operateType.value !== 'edit') {
    form.id = ''
  }
}

// 获取租赁单元列表
const loadLeaseUnits = async (id) => {
  const { result } = await queryContractLeaseUnits({ id })
  form.contractLeaseUnitsList = result.map((item) => {
    item.id = item.leaseUnit
    return item
  })
}

// 获取款项列表
const loadFundList = async (id) => {
  const { result } = await queryContractLeaseFunds({ id })
  form.contractLeaseFundsList = result.map((item) => {
    return {
      ...item,
      dateRange: [item.startDate, item.expireDate]
    }
  })
}

const form = reactive({
  id: '',
  number: '',
  contractNumber: '',
  status: '',
  bizStatus: '',
  bizDate: '',
  signDate: '',
  customer: '',
  contractType: '',
  manageCompany: '',
  rentScheme: '', // 关联招租方案
  operator: '',
  operatorDepart: '',
  pricedType: '', // 定价类型
  startDate: '',
  expireDate: '',
  terminateDate: '', // 中止日期
  terminateReason: '', // 中止原因
  changeReason: '', // 变更原因
  originalContract: '', // 变更时，原合同id
  // initContract: '', // 合同变更最初合同
  // contractVersion: 0, // 合同版本
  // isLatestContractVersion: true, // 是否最新版本合同
  // totalArea: 0, // 总面积
  // totalRental: 0, // 合计租金
  // totalRemission: 0, // 合计租金减免
  remark: '',
  attachmentIds: '',
  contractLeaseUnitsList: [], // 租赁单元
  contractLeaseFundsList: [], // 合同款项
  contractDetailBillsVOList: [] // 第三步，账单明细预览，只查看，无需提交
})

/**
 * 上一步/下一步
 * @param {string} type previous | next
 */
const handleChangeStep = async (type) => {
  if (type === 'previous') {
    current.value--
  } else {
    if (current.value === 1) {
      await step1Ref.value.validate()
    } else if (current.value === 2) {
      const step2Result = await step2Ref.value.validate()
      if (!step2Result) return
      // 计算总面积
      form.totalArea = form.contractLeaseUnitsList
        .reduce((total, item) => {
          return total.plus(new Decimal(item.leaseArea || 0))
        }, new Decimal(0))
        .toFixed(2)
      const { result } = await queryBillList(getParams())
      form.contractDetailBillsVOList = result.contractDetailBillsVOList
    }
    current.value++
  }
}

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  if (current.value === 2) {
    const step2Result = await step2Ref.value.validate(true)
    if (!step2Result) return
  }
  try {
    saveLoading.value = true
    if (form.id) {
      await edit(getParams())
    } else {
      const { result } = await save(getParams())
      for (const key in form) {
        if (!Array.isArray(form[key]) && !['attachmentIds'].includes(key)) {
          form[key] = result[key]
        }
      }
    }
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const getParams = () => {
  return {
    id: form.id,
    number: form.number,
    contractNumber: form.contractNumber,
    bizDate: form.bizDate,
    signDate: form.signDate,
    customer: form.customer,
    contractType: form.contractType,
    manageCompany: form.manageCompany,
    rentScheme: form.rentScheme,
    operator: form.operator,
    operatorDepart: form.operatorDepart,
    pricedType: form.pricedType,
    startDate: form.startDate,
    expireDate: form.expireDate,
    terminateDate: form.terminateDate,
    terminateReason: form.terminateReason,
    changeReason: form.changeReason,
    originalContract: form.originalContract,
    initContract: form.initContract,
    remark: form.remark,
    attachmentIds: form.attachmentIds,
    contractLeaseUnitsList: form.contractLeaseUnitsList.map((item) => ({
      id: '',
      leaseUnit: item.id,
      detailAddress: item.detailAddress,
      collectionCompany: item.collectionCompany,
      ownerCompany: item.ownerCompany,
      leaseArea: item.leaseArea,
      areaManager: item.areaManager,
      leaseUse: item.leaseUse,
      propertyUse: item.propertyUse,
      firefightingRate: item.firefightingRate,
      remark: item.remark
    })), // 租赁单元
    contractLeaseFundsList: form.contractLeaseFundsList.map((item) => {
      return {
        ...item,
        id: '',
        periodCount: item.period,
        amountPerPeriod: new Decimal(Number(item.period || 0)).times(Number(item.amountPerMonth || 0)).toString(),
        startDate: item.dateRange && item.dateRange.length ? item.dateRange[0] : '',
        expireDate: item.dateRange && item.dateRange.length ? item.dateRange[1] : ''
      }
    }) // 合同款项
  }
}

// 检查所有步骤是否正确填写完成
const validateAllSteps = async () => {
  try {
    await step1Ref.value.validate()
  } catch {
    current.value = 1
    message.warning('请检查基础信息及租赁单元是否正确填写完整')
    return
  }
  const step2Result = await step2Ref.value.validate()
  if (!step2Result) {
    current.value = 2
    return
  }
  return true
}
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  const isValid = await validateAllSteps()
  if (!isValid) return
  const { message } = await queryOverlayContractList(getParams())
  if (!message) {
    requestSubmitContract()
    return
  }
  Modal.confirm({
    title: '系统提示',
    content: `有历史合同编号: 【${message}】与当前合同有存在相同租赁单元且租期有重叠情况，是否确认继续创建合同？`,
    centered: true,
    onOk: () => {
      requestSubmitContract()
    }
  })
}

const requestSubmitContract = async () => {
  try {
    confirmLoading.value = true
    await submit(getParams())
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (Array.isArray(form[key])) {
      form[key] = []
    } else {
      form[key] = ''
    }
  }
  step1Ref.value.clearValidate()
  current.value = 1
  visible.value = false
}

// 前往价格递增方式管理页面
const toPriceIncreasePage = async () => {
  await handleSave()
  handleCancel()
  router.push({ path: '/contract/priceIncrease' })
}

onMounted(() => {
  const rentSchemeInfo = sessionStorage.getItem('rentSchemeInfo')
  if (rentSchemeInfo) {
    open()
    const data = JSON.parse(rentSchemeInfo)
    form.rentScheme = data.id // 关联招租方案
    form.customer = data.customer
    form.contractLeaseUnitsList = data.leaseUnitList
    sessionStorage.removeItem('rentSchemeInfo')
  }
})

defineExpose({ open })
</script>
