<template>
  <a-modal
    v-model:open="visible"
    title="中止合同"
    width="600px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    centered
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :z-index="1010"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      autocomplete="off"
      label-align="left"
      :label-col="{ style: { width: 74 } }"
    >
      <a-form-item label="中止日期" name="terminateDate">
        <a-date-picker v-model:value="form.terminateDate" value-format="YYYY-MM-DD" style="width: 100%"></a-date-picker>
      </a-form-item>
      <a-form-item label="中止原因" name="terminateReason">
        <a-textarea
          v-model:value="form.terminateReason"
          show-count
          :maxlength="200"
          :auto-size="{ minRows: 4, maxRows: 4 }"
        ></a-textarea>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { abort } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (data) => {
  form.id = data.id
  form.bizStatus = data.bizStatus
  form.status = data.status
  form.expireDate = data.expireDate
  visible.value = true
}

const form = reactive({
  id: '',
  bizStatus: '',
  expireDate: '',
  status: '',
  terminateDate: '',
  terminateReason: ''
})
const rules = {
  terminateDate: [{ required: true, message: '请选择中止日期', trigger: 'change' }],
  terminateReason: [{ required: true, message: '请输入中止原因', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    await abort(form)
    handleCancel()
    message.success('中止申请已提交')
    emit('refresh')
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  formRef.value.resetFields()
  visible.value = false
}

defineExpose({ open })
</script>
