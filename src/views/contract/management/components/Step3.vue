<template>
  <div>
    <strong class="text-[16px]">合同账单明细预览</strong>
    <div class="my-[20px]">
      <a-radio-group v-model:value="paymentType" button-style="solid">
        <a-radio-button
          v-for="item in form.contractDetailBillsVOList"
          :key="item.paymentType"
          :value="item.paymentType"
        >
          {{ item.paymentType_dictText }}
        </a-radio-button>
      </a-radio-group>
    </div>
    <a-table
      :data-source="tableData"
      :columns="columns"
      :pagination="false"
      :scroll="{ x: 2400, y: 'calc(100vh - 410px)' }"
    ></a-table>
  </div>
</template>

<script setup>
import { renderBoolean, renderMoney } from '@/utils/render'

const { form } = defineProps({
  form: { required: true, type: Object }
})

const paymentType = ref('全部')
const tableData = computed(() => {
  if (!(form.contractDetailBillsVOList && form.contractDetailBillsVOList.length)) return []
  const data = form.contractDetailBillsVOList.find((i) => i.paymentType === paymentType.value)
  return data ? data.contractDetailBillsList : []
})

const columns = [
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  { title: '是否押金', dataIndex: 'isDeposit', customRender: ({ text }) => renderBoolean(text) },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '减免金额', dataIndex: 'remission', customRender: ({ text }) => renderMoney(text) },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text) },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text) },
  { title: '实际应收金额', dataIndex: 'actualReceiveAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '已转应收金额', dataIndex: 'transfered', customRender: ({ text }) => renderMoney(text) },
  { title: '未转应收金额', dataIndex: 'transferdBalance', customRender: ({ text }) => renderMoney(text) },
  { title: '已抵扣金额', dataIndex: 'transferDeduction', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '已退款金额', dataIndex: 'refunded', customRender: ({ text }) => renderMoney(text) || '-' },
  {
    title: '尾差已处理金额',
    dataIndex: 'offDifference',
    width: 140,
    customRender: ({ text }) => renderMoney(text) || '-'
  },
  { title: '可抵退转金额', dataIndex: 'balance', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '已提房产税', dataIndex: 'houseTax', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' }
]
</script>
