<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="合同详情"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleClose"
  >
    <template #extra v-if="!readonly">
      <a-dropdown>
        <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
          <span>操作</span>
          <i class="a-icon-arrow-down ml-[4px]"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <div class="primary-btn" @click="handleAudit(true)">审核(临时功能)</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITING'].includes(detail.status)">
              <div class="primary-btn" @click="handleWithdraw">撤回</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
              <div class="primary-btn" @click="handleEdit">编辑</div>
            </a-menu-item>
            <a-menu-item v-if="['InExecution', 'Modified'].includes(detail.bizStatus)">
              <div class="primary-btn" @click="handleTermination">中止</div>
            </a-menu-item>
            <a-menu-item v-if="['Suspended'].includes(detail.bizStatus)">
              <div class="primary-btn" @click="handleCancelTermination">取消中止</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK'].includes(detail.status)">
              <div class="primary-btn" @click="handleRemove">删除</div>
            </a-menu-item>
            <a-menu-item v-if="['InExecution', 'Modified'].includes(detail.bizStatus)">
              <div class="primary-btn" @click="handleChange">变更</div>
            </a-menu-item>
            <a-menu-item v-if="!['NotEffective', null, undefined].includes(detail.bizStatus)">
              <div class="primary-btn" @click="handleRenew">续签</div>
            </a-menu-item>
            <a-menu-item v-if="['InExecution'].includes(detail.bizStatus)">
              <div class="primary-btn" @click="handleAdjust">账单调整</div>
            </a-menu-item>
            <a-menu-item v-if="['Suspended'].includes(detail.bizStatus)">
              <div class="primary-btn" @click="handleClearing">退租清算</div>
            </a-menu-item>
            <a-menu-item v-if="['InExecution', 'Modified', 'Suspended', 'Cleared'].includes(detail.bizStatus)">
              <div class="primary-btn" @click="handleAudit(false)">反审核</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITOK'].includes(detail.status)">
              <div class="primary-btn" @click="handleViewBill">查看明细账单</div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.contractNumber }}</h2>
        <status-tag
          dict-code="CT_BASE_ENUM_Contract_BizStatus"
          :dict-value="detail.bizStatus"
          v-if="detail.bizStatus"
        ></status-tag>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status" v-else></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #base>
          <base-info :detail="detail"></base-info>
        </template>
        <template #bill>
          <billing-detail :detail="detail"></billing-detail>
        </template>
        <template #writeOff>
          <write-off :detail="detail"></write-off>
        </template>
        <template #clearing>
          <clearings :contract-id="detail.id"></clearings>
        </template>
        <template #change v-if="tabList.length === 5">
          <a-table
            :data-source="detail.changeList"
            :columns="changeColumns"
            :pagination="false"
            :scroll="{ y: '50vh' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <span class="primary-btn" @click="handleViewChange(record)">查看</span>
              </template>
            </template>
          </a-table>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import {
  detail as getDetail,
  queryContractLeaseUnits,
  queryContractLeaseFunds,
  queryBillList,
  getWriteOffList,
  getChangeLog
} from '../apis.js'
import { renderDictTag } from '@/utils/render'
import BaseInfo from './BaseInfo.vue'
import BillingDetail from './BillingDetail.vue'
import WriteOff from './WriteOff.vue'
import Clearings from './Clearings.vue'

defineProps({
  readonly: { type: Boolean, default: false }
})

const emit = defineEmits([
  'edit',
  'abort',
  'change',
  'renew',
  'withdraw',
  'cancelAbort',
  'viewBill',
  'viewOldContract',
  'audit',
  'adjust',
  'clearing',
  'remove'
])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadData(id)
}

const loading = ref(false)
const loadData = async (id) => {
  loading.value = true
  await Promise.all([
    loadDetail(id),
    loadLeaseUnitList(id),
    loadPaymentList(id),
    loadWriteOffList(id),
    loadChangeList(id)
  ])
  loadBillList()
  loading.value = false
}

const detail = reactive({
  id: '',
  leaseUnitsList: [], // 租赁单元
  paymentList: [], // 合同款项
  billList: [], // 账单明细
  writeOffList: [], // 核销记录
  changeList: [] // 变更记录
})
const loadDetail = async (id) => {
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
}

const tabList = computed(() => {
  const list = [
    { title: '基础信息', name: 'base', showTitle: false },
    { title: '账单明细', name: 'bill' },
    { title: '核销记录', name: 'writeOff' },
    { title: '退租清算', name: 'clearing', showTitle: false }
  ]
  return detail.changeList.length ? list.concat([{ title: '变更记录', name: 'change' }]) : list
})

const loadLeaseUnitList = async (id) => {
  const { result } = await queryContractLeaseUnits({ id })
  detail.leaseUnitsList = result
}

const loadPaymentList = async (id) => {
  const { result } = await queryContractLeaseFunds({ id })
  detail.paymentList = result
}

// 获取合同变更记录
const loadChangeList = async (id) => {
  const { result } = await getChangeLog({ id })
  detail.changeList = result
}

const loadBillList = async () => {
  const { result } = await queryBillList({
    id: detail.id,
    number: detail.number,
    contractNumber: detail.contractNumber,
    bizDate: detail.bizDate,
    signDate: detail.signDate,
    customer: detail.customer,
    contractType: detail.contractType,
    manageCompany: detail.manageCompany,
    operator: detail.operator,
    operatorDepart: detail.operatorDepart,
    pricedType: detail.pricedType,
    startDate: detail.startDate,
    expireDate: detail.expireDate,
    terminateDate: detail.terminateDate,
    terminateReason: detail.terminateReason,
    changeReason: detail.changeReason,
    originalContract: detail.originalContract,
    totalArea: detail.totalArea,
    remark: detail.remark,
    attachmentIds: detail.attachmentIds,
    contractLeaseUnitsList: detail.leaseUnitsList, // 租赁单元
    contractLeaseFundsList: detail.paymentList
  })
  detail.billList = result.contractDetailBillsVOList
}

const loadWriteOffList = async (id) => {
  const { result } = await getWriteOffList({ id })
  detail.writeOffList = result
}

const handleEdit = () => {
  emit('edit', detail)
  handleClose()
}
const handleTermination = () => {
  emit('abort', detail)
}
const handleCancelTermination = () => {
  emit('cancelAbort', detail)
}
const handleRenew = () => {
  emit('renew', detail)
  handleClose()
}
const handleWithdraw = () => {
  emit('withdraw', detail)
}
const handleViewBill = () => {
  emit('viewBill', detail)
  handleClose()
}
const handleChange = () => {
  emit('change', detail)
  handleClose()
}
const handleAdjust = () => {
  emit('adjust', detail)
  handleClose()
}
const handleClearing = () => {
  emit('clearing', detail)
  handleClose()
}
const handleAudit = (result) => {
  emit('audit', detail, result)
}
const handleRemove = () => {
  emit('remove', detail)
}

const handleClose = () => {
  visible.value = false
  detail.leaseUnitsList = [] // 租赁单元
  detail.paymentList = [] // 合同款项
  detail.billList = [] // 账单明细
  detail.writeOffList = []
  detail.changeList = []
}

const changeColumns = [
  { title: '合同编号', dataIndex: 'contractNumber' },
  { title: '业务时间', dataIndex: 'bizDate' },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_Contract_BizStatus', 'dot')
  },
  { title: '签约时间', dataIndex: 'signDate' },
  { title: '变更原因', dataIndex: 'changeReason' },
  { title: '操作', dataIndex: 'action' }
]

const handleViewChange = (data) => {
  emit('viewOldContract', data)
}

defineExpose({ open, loadData, visible, handleClose })
</script>
