<template>
  <a-drawer
    v-model:open="visible"
    class="edit-breach-dispose-drawer common-drawer"
    title="合同违约处置"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <h2 class="text-[16px] font-bold mb-[20px]">基础信息</h2>
      <a-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-col="{ style: { width: '74px' } }"
        label-align="left"
        autocomplete="off"
      >
        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker v-model:value="form.bizDate" value-format="YYYY-MM-DD" style="width: 100%"></a-date-picker>
        </a-form-item>
        <a-form-item label="违约金额" name="amount">
          <a-input v-model:value="form.amount" :maxlength="7">
            <template #suffix>元</template>
          </a-input>
        </a-form-item>
        <a-form-item label="合同" name="contract">
          <a-form-item-rest>
            <f7-select
              v-model="form.contract"
              f7-type="contract"
              @change="loadContractDetail"
              width="402px"
            ></f7-select>
          </a-form-item-rest>
        </a-form-item>
        <div class="flex flex-wrap gap-[12px] pl-[74px] text-secondary" v-if="contractDetail.id">
          <span class="w-[calc(50%-6px)]">签约客户: {{ contractDetail.customer_dictText }}</span>
          <span class="w-[calc(50%-6px)]">签约日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">物业管理公司: {{ contractDetail.manageCompany_dictText }}</span>
          <span class="w-[calc(50%-6px)]">合同类型: {{ contractDetail.contractType_dictText }}</span>
          <span class="w-[calc(50%-6px)]">业务员: {{ contractDetail.operator_dictText }}</span>
          <span class="w-[calc(50%-6px)]">租赁单元: {{ contractDetail.leaseUse_dictText }}</span>
          <span class="w-[calc(50%-6px)]">合同开始日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">合同结束日期: {{ contractDetail.expireDate }}</span>
        </div>
      </a-form>
      <h4 class="text-[16px] font-bold mt-[40px] mb-[16px]">附件</h4>
      <files-upload v-model="form.attachmentIds" :biz-id="form.id"></files-upload>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        :loading="confirmLoading"
        @click="handleSubmit"
        v-auth="'biz.contractmanage:ct_biz_liquidated_damages:submit'"
      >
        提交
      </a-button>
      <a-button
        type="primary"
        :loading="saveLoading"
        ghost
        @click="handleSave"
        v-if="['', 'TEMP'].includes(form.status)"
      >
        暂存
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, submit, detail, save } from '../apis.js'
import { detail as getContractDetail, queryContractLeaseUnits } from '@/views/contract/management/apis'
import { message } from 'ant-design-vue'
import { validateMoney } from '@/utils/validate'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    form[key] = result[key] || ''
  }
  loading.value = false
  loadContractDetail(result.contract)
}

const form = reactive({
  id: '',
  number: '',
  manageCompany: '',
  bizDate: '',
  contract: '',
  contractClearing: '',
  isCharge: true, // 是否收取违约金
  signDate: '',
  customer: '',
  contractType: '',
  operator: '',
  operatorDepart: '',
  startDate: '',
  expireDate: '',
  leaseUnit: '',
  pricedType: '',
  amount: '',
  remark: '',
  attachmentIds: ''
})

const rules = {
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  amount: [{ required: true, validator: validateMoney(true, '违约金额'), trigger: 'blur' }],
  contract: [{ required: true, message: '请选择合同', trigger: 'change' }]
}

const contractDetail = reactive({})
const loadContractDetail = async (id) => {
  if (!id) {
    contractDetail.id = ''
    return
  }
  const { result } = await getContractDetail({ id })
  Object.assign(contractDetail, result)
  form.manageCompany = result.manageCompany
  form.signDate = result.signDate
  form.customer = result.customer
  form.contractType = result.contractType
  form.operator = result.operator
  form.operatorDepart = result.operatorDepart
  form.startDate = result.startDate
  form.expireDate = result.expireDate
  form.pricedType = result.pricedType
  const { result: leaseUnitList } = await queryContractLeaseUnits({ id })
  form.leaseUnits = leaseUnitList.map((item) => item.id).join(',')
  contractDetail.leaseUse_dictText = leaseUnitList.map((item) => item.leaseUnit_dictText).join(',')
}

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  const fields = []
  for (const key in rules) {
    if (form[key]) {
      fields.push(key)
    }
  }
  await formRef.value.validateFields(fields)
  try {
    saveLoading.value = true
    if (form.id) {
      await edit(form)
    } else {
      const { result } = await save(form)
      form.id = result.id
      form.number = result.number
      form.status = result.status
    }
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    await submit(form)
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (typeof form[key] === 'boolean') {
      form[key] = true
    } else {
      form[key] = ''
    }
  }
  contractDetail.id = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-breach-dispose-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 40px;
  }
  .ant-form-item {
    width: calc(50% - 20px);
    &:last-child {
      width: 100%;
    }
  }
}
</style>
