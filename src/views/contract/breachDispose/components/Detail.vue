<template>
  <a-drawer
    v-model:open="visible"
    title="合同违约处置"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
    :root-style="{ zIndex: 900 }"
  >
    <template #extra>
      <a-dropdown>
        <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
          <span>操作</span>
          <i class="a-icon-arrow-down ml-[4px]"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <div class="primary-btn" @click="handleAudit(true)">审核(临时功能)</div>
            </a-menu-item>
            <a-menu-item>
              <div class="primary-btn" @click="handleViewContract">查看合同</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITING'].includes(detail.status)">
              <div class="primary-btn" @click="handleWithdraw">撤回</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
              <div class="primary-btn" @click="handleEdit">编辑</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK'].includes(detail.status)">
              <div class="primary-btn" @click="handleRemove">删除</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITOK'].includes(detail.status)">
              <div class="primary-btn" @click="handleAudit(false)">反审核</div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">合同违约处置</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h4 class="text-[16px] font-bold mb-[12px]">合同基础信息</h4>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
        <span class="w-[50%]">违约金额: {{ detail.amount ? `${detail.amount}元` : '' }}</span>
        <span class="w-[50%]">合同编码: {{ detail.contract_dictText }}</span>
        <span class="w-[50%]">签约客户: {{ detail.customer_dictText }}</span>
        <span class="w-[50%]">签约日期: {{ detail.signDate }}</span>
        <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-[50%]">合同类型: {{ detail.contractType_dictText }}</span>
        <span class="w-[50%]">业务员: {{ detail.operator_dictText }}</span>
        <span class="w-[50%]">合同开始日期: {{ detail.startDate }}</span>
        <span class="w-[50%]">合同结束日期: {{ detail.expireDate }}</span>
        <span class="w-[50%]">应收日期: {{ detail.receiveDate }}</span>
      </div>
      <h4 class="text-[16px] font-bold mt-[40px] mb-[12px]">违约租赁单元</h4>
      <a-table :data-source="leaseUnitList" :columns="leaseUnitColumns" :pagination="false" :scroll="{ x: 1500 }">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <span class="primary-btn mr-[10px]" @click="viewUnitDetail(record)">单元详情</span>
          </template>
        </template>
      </a-table>
      <h4 class="text-[16px] font-bold mt-[44px] mb-[12px]">附件</h4>
      <file-list :biz-id="detail.id"></file-list>
    </a-spin>
  </a-drawer>
  <lease-unit-detail ref="leaseUnitDetailRef"></lease-unit-detail>
</template>

<script setup>
import { detail as getDetail, queryBreachLeaseUnit } from '../apis.js'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'

const emit = defineEmits(['edit', 'refresh', 'viewContract', 'audit', 'remove'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const loading = ref(false)
const detail = reactive({
  id: ''
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  await loadLeaseUnitList(result.contract)
  loading.value = false
}

const leaseUnitList = ref([])
const loadLeaseUnitList = async (contract) => {
  const { result } = await queryBreachLeaseUnit({ contract })
  leaseUnitList.value = result
}

const leaseUnitColumns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit_dictText', fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const leaseUnitDetailRef = ref()
const viewUnitDetail = (data) => {
  leaseUnitDetailRef.value.open({ id: data.leaseUnit })
}

const handleViewContract = () => {
  emit('viewContract', detail)
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleWithdraw = () => {
  emit('withdraw', detail)
}

const handleAudit = (result) => {
  emit('audit', detail, result)
}

const handleRemove = () => {
  emit('remove', detail)
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>
