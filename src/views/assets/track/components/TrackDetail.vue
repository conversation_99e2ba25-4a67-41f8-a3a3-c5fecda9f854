<template>
  <a-drawer v-model:open="visible" title="资产跟踪" class="common-detail-drawer" placement="right" width="1072px">
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <!-- 审核中不允许编辑 -->
        <span
          class="primary-btn"
          @click="handleEdit"
          v-if="detailData.status !== 'AUDITING'"
          v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:edit'"
        >
          编辑
        </span>
        <a-dropdown>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down text-[12px]"></i>
          </span>
          <template #overlay>
            <a-menu>
              <!-- 审核中没有审核记录 -->
              <a-menu-item>
                <div class="primary-btn" v-if="detailData.status !== 'AUDITING'">查看审核记录</div>
              </a-menu-item>
              <a-menu-item>
                <div
                  v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:unAudit'"
                  class="primary-btn"
                  @click="counterApproval"
                >
                  反审核
                </div>
              </a-menu-item>
              <!-- 审核中不能被删除 -->
              <a-menu-item>
                <div
                  v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:delete'"
                  class="primary-btn"
                  @click="handleDel"
                  v-if="detailData.status !== 'AUDITING'"
                >
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <!-- ：{{ detailData.name }} -->
        <h2 class="text-[18px] font-bold mr-[12px]">
          {{ renderDict(detailData.trackingType, 'CT_BASE_ENUM_TrackingType') }}资产跟踪
        </h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detailData.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>

      <div>
        <div class="text-[16px] font-bold mb-[12px] text-secondary">跟踪信息</div>
        <!-- 闲置 -->
        <div v-if="detailData.trackingType === 'Idle'" class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">资产：{{ detailData.houseOwner_dictText || '-' }}</span>
          <span class="w-[50%]">闲置面积（㎡）：{{ detailData.unUsedArea || '-' }}</span>
          <span class="w-[50%]">闲置状态开始时间：{{ detailData.unUsedBeginDate || '-' }}</span>
          <span class="w-[50%]">闲置状态结束时间：{{ detailData.unUsedEndDate || '-' }}</span>
          <span class="w-[50%]">闲置原因：{{ detailData.unUsedReason || '-' }}</span>
          <span class="w-[50%]">
            闲置时间：{{ renderDict(detailData.unUsedTime, 'CT_BASE_ENUM_IdleAssetTrackingInfo_UnUsedTime') || '-' }}
          </span>
          <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
        <!-- 占用 -->
        <div v-if="detailData.trackingType === 'Occupy'" class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">资产：{{ detailData.houseOwner_dictText || '-' }}</span>
          <span class="w-[50%]">被占用面积（㎡）：{{ renderMoney(detailData.occupyArea, 4) || '-' }}</span>
          <span class="w-[50%]">占用人：{{ detailData.occupyPerson || '-' }}</span>
          <span class="w-[50%]">占用原因：{{ detailData.occupyReason || '-' }}</span>
          <span class="w-[50%]">占用状态开始时间：{{ detailData.occupyBeginDate || '-' }}</span>
          <span class="w-[50%]">占用状态结束时间：{{ detailData.occupyEndDate || '-' }}</span>
          <span class="w-[50%]">其他情况：{{ '-' }}</span>
          <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
        <!-- 借用 -->
        <div v-if="detailData.trackingType === 'Borrow'" class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[100%]">业务时间：{{ detailData.bizDate || '-' }}</span>
          <span class="w-[100%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
        <!-- 自用 -->
        <div v-if="detailData.trackingType === 'Self'" class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[100%]">业务时间：{{ detailData.bizDate || '-' }}</span>
          <span class="w-[100%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
      </div>
      <div class="mt-[40px]">
        <div
          v-if="['Idle', 'Occupy'].includes(detailData.trackingType)"
          class="flex justify-between items-center mb-[12px]"
        >
          <div class="text-[16px] font-bold text-secondary">盘活记录</div>
          <a-button type="primary" ghost @click="addRecords">
            <span class="a-icon-plus mr-[8px]"></span>
            添加记录
          </a-button>
        </div>
        <h2
          v-if="['Borrow', 'Self'].includes(detailData.trackingType)"
          class="text-[16px] font-bold mb-[12px] text-secondary"
        >
          {{ detailData.trackingType === 'Borrow' ? '借用资产' : '自用资产' }}
        </h2>
        <a-table
          :data-source="tableList"
          :columns="columns"
          :scroll="{
            y: 300,
            x: ['Idle', 'Occupy'].includes(detailData.trackingType)
              ? 900
              : detailData.trackingType === 'Borrow'
                ? 1500
                : 1800
          }"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <template v-if="['Idle', 'Occupy'].includes(detailData.trackingType)">
                <span class="primary-btn" @click="rowEdit(record)">编辑</span>
                <span class="primary-btn" @click="rowDel(record)">删除</span>
              </template>
              <template v-if="['Borrow', 'Self'].includes(detailData.trackingType)">
                <span
                  v-auth="'biz.basicdatadeal:ct_gzb_borrow_asset_tracking:finished'"
                  v-if="record.status === 'UnFinish'"
                  class="primary-btn"
                  @click="rowFinish(record)"
                >
                  结束
                </span>
                <span
                  v-auth="'biz.basicdatadeal:ct_gzb_borrow_asset_tracking:unFinished'"
                  v-if="record.status === 'Finish'"
                  class="primary-btn"
                  @click="rowUnFinish(record)"
                >
                  反结束
                </span>
              </template>
            </template>
          </template>
        </a-table>
      </div>
    </a-spin>

    <template #footer v-if="['Idle', 'Occupy'].includes(detailData.trackingType)">
      <!-- v-if="detailData.status === 'UnFinish'" v-if="detailData.status === 'Finish'" -->
      <a-button v-auth="'biz.basicdatadeal:ct_gzb_borrow_asset_tracking:finished'" type="primary" @click="rowFinish">
        结束
      </a-button>
      <a-button
        v-auth="'biz.basicdatadeal:ct_gzb_borrow_asset_tracking:unFinished'"
        type="primary"
        @click="rowUnFinish"
      >
        反结束
      </a-button>
    </template>
  </a-drawer>
  <!-- 编辑 -->
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
  <!-- 盘活记录编辑 -->
  <asset-revitalization-info-edit
    ref="assetRevitalizationInfoEditRef"
    @refresh="getRecords(detailData.id)"
  ></asset-revitalization-info-edit>
  <!-- 资产结束操作 -->
  <assets-finish
    ref="assetFinishRef"
    :type="detailData.trackingType"
    @refresh="getRecords(detailData.id)"
  ></assets-finish>
</template>
<script setup>
import {
  idleDelById,
  occupyDelById,
  borrowDelById,
  selfDelById,
  idleQueryById,
  occupyQueryById,
  borrowQueryById,
  selfQueryById,
  queryIdleAssetActivateByMainId,
  queryOccupyAssetActivateByMainId,
  queryBorrowAssetTrackingInfoByMainId,
  querySelfAssetTrackingInfoByMainId,
  idleUnAudit,
  occupyUnAudit,
  borrowUnAudit,
  selfUnAudit,
  idleDealFinish,
  occupyDealFinish,
  borrowDealFinish,
  selfDealFinish,
  idleEdit,
  occupyEdit
} from '../apis'

import { detailById } from '@/views/assets/manage/apis'
import AssetRevitalizationInfoEdit from './AssetRevitalizationInfoEdit.vue'
import AssetsFinish from './AssetsFinish.vue'
import AddEdit from './AddEdit.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDict, renderMoney } from '@/utils/render'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  detailData.value.trackingType = data.trackingType
  visible.value = true
  if (data.id) {
    getDetailById(data.id)
  }
}
defineExpose({ open })
const tableList = ref([])
// 盘活记录列数组
const columns = computed(() => {
  let recordList = []
  switch (detailData.value.trackingType) {
    case 'Idle':
      recordList = [
        { title: '盘活措施填报日期', dataIndex: 'fillDate', width: 180, fixed: 'left' },
        { title: '已采取的盘活管理措施', dataIndex: 'ctrlMeasure' },
        { title: '下一步盘活建议', dataIndex: 'activateAdvise' },
        { title: '盘活方式', dataIndex: 'activateMethod_dictText' },
        { title: '是否已盘活', dataIndex: 'isActivate_dictText' },
        { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
      ]
      break
    case 'Occupy':
      recordList = [
        { title: '盘活措施填报日期', dataIndex: 'fillDate', width: 180, fixed: 'left' },
        { title: '已采取的盘活管理措施', dataIndex: 'ctrlMeasure' },
        { title: '下一步盘活建议', dataIndex: 'activateAdvise' },
        { title: '盘活方式', dataIndex: 'activateMethod_dictText' },
        { title: '是否已盘活', dataIndex: 'isActivate_dictText' },
        { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
      ]
      break
    case 'Borrow':
      recordList = [
        { title: '资产', dataIndex: 'houseOwner_dictText', width: 180, fixed: 'left' },
        { title: '借用开始时间', dataIndex: 'borrowBeginDate', width: 120 },
        { title: '借用结束时间', dataIndex: 'borrowEndDate', width: 120 },
        { title: '借用人', dataIndex: 'borrowPerson' },
        { title: '借用原因', dataIndex: 'borrowReason', width: 100 },
        { title: '改变用途', dataIndex: 'changeUse' },
        { title: '备注', dataIndex: 'remark' },
        {
          title: '状态',
          dataIndex: 'status',
          customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_SelfAssetTrackingInfo_Status')
        },
        { title: '产权号', dataIndex: 'ownerNumber' },
        { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', width: 120 },
        { title: '资产权属公司', dataIndex: 'ownerCompany_dictText', width: 120 },
        { title: '建筑面积（㎡）', dataIndex: 'structureArea' },
        { title: '资产类型', dataIndex: 'assetsType_dictText' },
        { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
      ]
      break
    case 'Self':
      recordList = [
        { title: '资产', dataIndex: 'houseOwner_dictText', width: 180, fixed: 'left' },
        { title: '自用开始时间', dataIndex: 'selfBeginDate', width: 120 },
        { title: '自用结束时间', dataIndex: 'selfEndDate', width: 120 },
        { title: '使用用途', dataIndex: 'selfUse' },
        { title: '有收益', dataIndex: 'profitable' },
        { title: '定制办公用房标准', dataIndex: 'devStandards', width: 180 },
        { title: '超标', dataIndex: 'outLimit' },
        { title: '备注', dataIndex: 'remark' },
        {
          title: '状态',
          dataIndex: 'status',
          customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_SelfAssetTrackingInfo_Status')
        },
        { title: '产权号', dataIndex: 'ownerNumber' },
        { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', width: 120 },
        { title: '资产权属公司', dataIndex: 'ownerCompany_dictText', width: 120 },
        { title: '建筑面积（㎡）', dataIndex: 'structureArea' },
        { title: '资产类型', dataIndex: 'assetsType_dictText' },
        { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
      ]
      break
  }
  return recordList
})

const detailData = ref({})
const loading = ref(false)

// 通过id获取详情
const requestFuncObj = computed(() => {
  const func = {
    delById: null,
    queryById: null,
    unAudit: null,
    queryAssetActivateByMainId: null
  }
  switch (detailData.value.trackingType) {
    case 'Idle':
      func.queryById = idleQueryById
      func.delById = idleDelById
      func.unAudit = idleUnAudit
      func.queryAssetActivateByMainId = queryIdleAssetActivateByMainId
      break
    case 'Occupy':
      func.queryById = occupyQueryById
      func.delById = occupyDelById
      func.unAudit = occupyUnAudit
      func.queryAssetActivateByMainId = queryOccupyAssetActivateByMainId
      break
    case 'Borrow':
      func.queryById = borrowQueryById
      func.delById = borrowDelById
      func.unAudit = borrowUnAudit
      func.queryAssetActivateByMainId = queryBorrowAssetTrackingInfoByMainId
      break
    case 'Self':
      func.queryById = selfQueryById
      func.delById = selfDelById
      func.unAudit = selfUnAudit
      func.queryAssetActivateByMainId = querySelfAssetTrackingInfoByMainId
      break
  }
  return func
})
// 获取详情
const getDetailById = async (id) => {
  const { result } = await requestFuncObj.value.queryById(id)
  detailData.value = result
  getRecords(id)
}
// 获取盘活记录
const getRecords = async (id) => {
  const { result } = await requestFuncObj.value.queryAssetActivateByMainId(id)
  // 借用和自用才要
  if (['Borrow', 'Self'].includes(detailData.value.trackingType)) {
    // 使用Promise.all等待所有异步操作完成
    await Promise.all(
      result.map(async (item) => {
        const data = await detailById(item.houseOwner)
        item.ownerNumber = data.result.ownerNumber
        item.collectionCompany_dictText = data.result.collectionCompany_dictText
        item.ownerCompany_dictText = data.result.ownerCompany_dictText
        item.structureArea = data.result.structureArea
        item.assetsType_dictText = data.result.assetsType_dictText
      })
    )
  }
  tableList.value = result
}

// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
}
// 反审批
const counterApproval = () => {
  Modal.confirm({
    title: '确认进行反审批？',
    content: '反审批后数据将重新变回待审批状态',
    centered: true,
    onOk: async () => {
      const data = await requestFuncObj.value.unAudit({ id: detailData.value.id })
      message.success(data.message)
      emits('loadData')
      visible.value = false
    }
  })
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '确认删除当前资产跟踪？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await requestFuncObj.value.delById(detailData.value.id)
      message.success(data.message)
      emits('loadData')
      visible.value = false
    }
  })
}

const assetRevitalizationInfoEditRef = ref()
// 盘活记录添加
const addRecords = () => {
  assetRevitalizationInfoEditRef?.value.open(detailData.value, tableList.value)
}
// 盘活记录编辑
const rowEdit = (row) => {
  assetRevitalizationInfoEditRef?.value.open(detailData.value, tableList.value, JSON.parse(JSON.stringify(row)))
}
// 盘活记录删除
const rowDel = (row) => {
  Modal.confirm({
    title: '确认删除盘活记录？',
    content: '',
    centered: true,
    onOk: async () => {
      if (detailData.value.trackingType === 'Idle') {
        detailData.value.idleAssetActivateList = tableList.value.filter((item) => item.id !== row.id)
      } else {
        detailData.value.occupyAssetActivateList = tableList.value.filter((item) => item.id !== row.id)
      }
      const data = await (detailData.value.trackingType === 'Idle'
        ? idleEdit(detailData.value)
        : occupyEdit(detailData.value))
      message.success(data.message)
      getRecords(detailData.value.id)
    }
  })
}
// 资产 结束操作
const assetFinishRef = ref()
const rowFinish = (row = '') => {
  assetFinishRef?.value.open(detailData.value, JSON.parse(JSON.stringify(tableList.value)), row.id)
}
const rowUnFinish = (row) => {
  const a = Modal.confirm({
    title: '确认反结束？',
    content: '',
    centered: true,
    onOk: async () => {
      const params = detailData.value
      let requestFunc = null
      switch (detailData.value.trackingType) {
        case 'Idle':
          requestFunc = idleDealFinish
          params.idleAssetActivateList = tableList.value
          params.unUsedEndDate = ''
          break
        case 'Occupy':
          requestFunc = occupyDealFinish
          params.occupyAssetActivateList = tableList.value
          params.occupyEndDate = ''
          break
        case 'Borrow':
          requestFunc = borrowDealFinish
          params.borrowAssetTrackingInfoList = tableList.value.filter((item) => item.id === row.id)
          params.borrowAssetTrackingInfoList[0].borrowEndDate = ''
          break
        case 'Self':
          requestFunc = selfDealFinish
          params.selfAssetTrackingInfoList = tableList.value.filter((item) => item.id === row.id)
          params.selfAssetTrackingInfoList[0].selfEndDate = ''
          break
      }
      try {
        const data = await requestFunc(params)
        message.success(data.message)
        getRecords(detailData.value.id)
      } finally {
        a.destroy()
      }
    }
  })
}
</script>
