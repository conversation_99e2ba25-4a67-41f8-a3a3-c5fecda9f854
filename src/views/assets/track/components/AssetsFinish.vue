<template>
  <a-modal
    v-model:open="visible"
    title="结束跟踪"
    width="400px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form
        :model="ruleForm"
        ref="formRef"
        :rules="rules"
        :label-col="{ style: { width: '100px' } }"
        autocomplete="off"
      >
        <a-form-item label="结束日期" name="endDate">
          <a-date-picker
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="ruleForm.endDate"
            type="date"
            placeholder="请选择结束日期"
            class="w-[100%]"
          />
        </a-form-item>
        <a-form-item v-if="['Idle', 'Occupy'].includes(type)" label="备注说明" name="remark">
          <a-textarea
            v-model:value="ruleForm.remark"
            placeholder="请输入备注"
            :maxlength="255"
            :rows="4"
            show-count
            allow-clear
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { idleDealFinish, occupyDealFinish, borrowDealFinish, selfDealFinish } from '../apis'
import { message } from 'ant-design-vue'
const emit = defineEmits(['refresh'])
const { type } = defineProps({
  type: { type: String, default: 'chart' }
})
const visible = ref(false)
// 借用和自用的时候有id
const open = (detailData, tableList, id = '') => {
  visible.value = true
  submitPar.value = detailData
  if (type === 'Idle') {
    submitPar.value.idleAssetActivateList = tableList
    return
  }
  if (type === 'Occupy') {
    submitPar.value.occupyAssetActivateList = tableList
    return
  }
  if (type === 'Borrow') {
    return (submitPar.value.borrowAssetTrackingInfoList = tableList.filter((item) => item.id === id))
  }
  submitPar.value.selfAssetTrackingInfoList = tableList.filter((item) => item.id === id)
}
defineExpose({ open })
const submitPar = ref({})
const ruleForm = ref({
  endDate: '',
  remark: ''
})
const rules = {
  endDate: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}
const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    let requestFunc = null
    switch (type) {
      case 'Idle':
        requestFunc = idleDealFinish
        submitPar.value.unUsedEndDate = ruleForm.value.endDate
        submitPar.value.remark = ruleForm.value.remark
        break
      case 'Occupy':
        requestFunc = occupyDealFinish
        submitPar.value.occupyEndDate = ruleForm.value.endDate
        submitPar.value.remark = ruleForm.value.remark
        break
      case 'Borrow':
        requestFunc = borrowDealFinish
        submitPar.value.borrowAssetTrackingInfoList[0].borrowEndDate = ruleForm.value.endDate
        break
      case 'Self':
        requestFunc = selfDealFinish
        submitPar.value.selfAssetTrackingInfoList[0].selfEndDate = ruleForm.value.endDate
        break
    }
    const data = await requestFunc(submitPar.value)
    confirmLoading.value = false
    handleCancel()
    message.success(data.message)
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}
const resetForm = () => {
  submitPar.value = {}
  ruleForm.value.endDate = ''
  ruleForm.value.remark = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}
</script>
