<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button
          v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:add'"
          class="mb-[10px]"
          type="primary"
          @click="handleAdd"
        >
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button
          v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:importExcel'"
          class="mb-[10px]"
          @click="handleImport"
        >
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div
                  v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:delete'"
                  class="primary-btn"
                  @click="handleRemove(false)"
                >
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]" label="">
          <s-input
            v-model="search.number"
            placeholder="搜索单据编号"
            class="ml-[10px] !w-[280px]"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <a-form-item>
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:view'" class="primary-btn" @click="rowView(record)">
            查看
          </span>
          <span
            v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:edit'"
            v-if="record.status !== 'AUDITING'"
            class="primary-btn"
            @click="rowEdit(record)"
          >
            编辑
          </span>
          <!-- 待审批 -->
          <span
            v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:edit'"
            class="primary-btn"
            v-if="record.status === 'AUDITING'"
            @click="rowBack(record)"
          >
            撤回
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <!--  审核不通过，审核通过，待审核 -->
                <a-menu-item v-if="['AUDITING', 'AUDITNO', 'AUDITOK'].includes(record.status)">
                  <div class="primary-btn" @click="rowViewApprove(record)">查看审批</div>
                </a-menu-item>
                <!-- 审核不通过 -->
                <a-menu-item v-if="['AUDITNO'].includes(record.status)">
                  <div
                    v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:submit'"
                    class="primary-btn"
                    @click="rowResubmit(record, 1)"
                  >
                    重新提交
                  </div>
                </a-menu-item>
                <!-- 暂存 -->
                <a-menu-item v-if="record.status === 'TEMP'">
                  <div
                    v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:submit'"
                    class="primary-btn"
                    @click="rowResubmit(record)"
                  >
                    提交
                  </div>
                </a-menu-item>
                <!-- 暂存 审核不通过 撤回 -->
                <a-menu-item v-if="['TEMP', 'AUDITNO', 'BACK'].includes(record.status)">
                  <div
                    v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:delete'"
                    class="primary-btn"
                    @click="handleRemove(record)"
                  >
                    删除
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <detail ref="detailRef" @load-data="onTableChange"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产处置单"
      :download-fn="() => exportExcel('资产处置单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import usePageTable from '@/hooks/usePageTable'
import { renderDict, renderDictTag } from '@/utils/render'
import AddEdit from './components/AddEdit.vue'
import Detail from './components/Detail.vue'
import useTableSelection from '@/hooks/useTableSelection'
import { getPage, submit, deleteBatch, back, exportExcel, importExcel } from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
  // 资产详情跳转过来新增
  if (route.query.adding) {
    addEditRef?.value.open()
  }
})

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter })
}
const route = useRoute()
const search = ref({
  column: 'number',
  order: 'desc',
  number: ''
})
const searchFilter = ref({
  manageCompany: '',
  assetsSource: '',
  bizDate: '',
  houseDealDate: '',
  transferMethod: '',
  transferor: '',
  decision: '',
  status: ''
})
const searchList = reactive([
  { label: '管理公司', name: 'manageCompany', type: 'companySelect', placeholder: '请选择管理公司' },
  { label: '资产来源', name: 'assetsSource', type: 'dic', code: 'CT_BAS_AssetsSource', placeholder: '请选择资产来源' },
  { label: '业务日期', name: 'bizDate', type: 'date', placeholder: '请选择业务日期' },
  { label: '房产处置日期', name: 'houseDealDate', type: 'date', placeholder: '请选择房产处置日期' },
  {
    label: '转让方式',
    name: 'transferMethod',
    type: 'dic',
    code: 'CT_BAS_TransferMethod',
    placeholder: '请选择转让方式'
  },
  { label: '转让方', name: 'transferor', type: 'companySelect', placeholder: '请选择转让方' },
  {
    label: '内部决议情况',
    name: 'decision',
    type: 'dic',
    code: 'CT_BASE_ENUM_HouseDealBill_Decision',
    placeholder: '请选择内部决议情况'
  },
  { label: '数据状态', name: 'status', type: 'dic', code: 'CT_BASE_ENUM_AuditStatus', placeholder: '请选择数据状态' }
  // { label: '资产编号', name: 'number' }
])

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: true },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 150, ellipsis: true },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '资产来源',
    dataIndex: 'assetsSource',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_AssetsSource'),
    ellipsis: true
  },
  { title: '业务日期', dataIndex: 'bizDate' },
  { title: '处置说明', dataIndex: 'dealExplain', ellipsis: true },
  { title: '房产处置日期', dataIndex: 'houseDealDate' },
  {
    title: '转让方式',
    dataIndex: 'transferMethod',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_TransferMethod')
  },
  { title: '转让方', dataIndex: 'transferor_dictText', ellipsis: true },
  { title: '审核时间', dataIndex: 'auditTime' },
  { title: '审核人', dataIndex: 'auditBy' },
  { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)
const addEditRef = ref()
// 新增
const handleAdd = () => {
  addEditRef?.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 编辑
const rowEdit = (row) => {
  addEditRef?.value.open(row)
}

// 撤回操作
const rowBack = (row) => {
  Modal.confirm({
    title: '确定撤回？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await back({ id: row.id })
      message.success(data.message)
      const pageNo = pagination.value.current
      onTableChange({ pageNo })
    }
  })
}

const rowViewApprove = () => {}
// 提交（重新提交）
const rowResubmit = (row, type = 0) => {
  Modal.confirm({
    title: type ? '确认重新提交？' : '确认提交？',
    content: '',
    centered: true,
    onOk: async () => {
      await submit(row)
      message.success(type ? '重新提交成功' : '提交成功')
      const pageNo = pagination.value.current
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
const handleRemove = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前资产处置？' : '确认批量删除选中资产处置？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('资产处置数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
