<template>
  <a-drawer v-model:open="visible" title="资产详情" class="common-detail-drawer" placement="right" width="1072px">
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <!-- 暂存、撤回、不通过的才能编辑提交？ -->
        <span
          v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)"
          v-auth="'bas:ct_bas_house_owner:edit'"
          class="primary-btn"
          @click="handleEdit"
        >
          编辑
        </span>
        <a-dropdown>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down text-[12px]"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div
                  v-if="['AUDITOK'].includes(detailData.status)"
                  v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:add'"
                  class="primary-btn"
                  @click="turnToPage(1)"
                >
                  资产处置
                </div>
              </a-menu-item>
              <a-menu-item>
                <div
                  v-if="['AUDITOK'].includes(detailData.status)"
                  v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:add'"
                  class="primary-btn"
                  @click="turnToPage(2)"
                >
                  资产跟踪
                </div>
              </a-menu-item>
              <a-menu-item>
                <div
                  v-if="['TEMP', 'BACK'].includes(detailData.status)"
                  v-auth="'bas:ct_bas_house_owner:delete'"
                  class="primary-btn"
                  @click="handleDel"
                >
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detailData.name }}</h2>
        <status-tag dict-code="CT_BASE_ENUM_HouseOwner_AuditStatus" :dict-value="detailData.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>资产编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>
      <!-- 锚点导航 -->
      <anchor-tabs :tab-list="navList" height="calc(100vh - 295px)">
        <template #baseInfo>
          <h2 class="text-[16px] font-bold mb-[12px]">资产照片</h2>
          <div class="flex flex-wrap gap-y-[12px] gap-x-[12px] text-[#495A7A]">
            <div class="text-center" v-if="detailData.ownerCover">
              <a-image :width="120" :src="detailData.ownerCover"></a-image>
              <div>产权封面</div>
            </div>
            <div class="text-center" v-if="detailData.ownerInside">
              <a-image :width="120" :src="detailData.ownerInside"></a-image>
              <div>产权证内页</div>
            </div>
            <div class="text-center" v-if="detailData.ownerRealImage">
              <a-image :width="120" :src="detailData.ownerRealImage"></a-image>
              <div>资产外观实拍</div>
            </div>
          </div>
          <h2 class="text-[16px] font-bold mb-[12px] mt-[12px]">资产基础信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">资产名称：{{ detailData.name || '-' }}</span>
            <span class="w-[50%]">产权分类：{{ detailData.treeId_dictText || '-' }}</span>
            <span class="w-[50%]">所属项目：{{ detailData.wyProject_dictText || '-' }}</span>
            <span class="w-[50%]">
              地址：{{ renderRegion(detailData.pcaCode) }}{{ detailData.detailAddress || '-' }}
            </span>
            <span class="w-[50%]">租金归集公司：{{ detailData.collectionCompany_dictText || '-' }}</span>
            <span class="w-[50%]">资产权属公司：{{ detailData.ownerCompany_dictText || '-' }}</span>
            <span class="w-[50%]">物业管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
            <span class="w-[50%]">资产类型：{{ renderDict(detailData.assetsType, 'CT_BAS_AssetsType') || '-' }}</span>
            <span class="w-[50%]">
              业务状态：{{ renderDict(detailData.bizStatus, 'CT_BASE_ENUM_HouseOwner_BizStatus') || '-' }}
            </span>
            <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
          </div>
          <h2 class="text-[16px] font-bold mb-[12px] mt-[12px]">产权信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">产权号：{{ detailData.ownerNumber || '-' }}</span>
            <span class="w-[50%]">权证获得日期：{{ detailData.warrantsDate || '-' }}</span>
            <span class="w-[50%]">取得来源：{{ detailData.acquisitionMethod_dictText || '-' }}</span>
            <span class="w-[50%]">
              产权情况：{{
                renderDict(detailData.propertyRightStatus, 'CT_BASE_ENUM_HouseOwner_PropertyRightStatus') || '-'
              }}
            </span>
            <span class="w-[50%]">代管委托方：{{ detailData.proxies || '-' }}</span>
            <span class="w-[50%]">房地权证合一：{{ renderBoolean(detailData.isUnionCertificate) || '-' }}</span>
            <span class="w-[50%]">产权用途：{{ renderDict(detailData.propertyUse, 'CT_BAS_PropertyUse') || '-' }}</span>
            <span class="w-[50%]">使用权类型：{{ renderDict(detailData.landNature, 'CT_BAS_LandNature') || '-' }}</span>
          </div>
          <h2 class="text-[16px] font-bold mb-[12px] mt-[12px]">土地信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">土地取得价格：{{ detailData.landPrice || '-' }}</span>
            <span class="w-[50%]">
              土地建设情况：{{ renderDict(detailData.landConstructionSituation, 'CT_BAS_LandCS') || '-' }}
            </span>
            <span class="w-[50%]">租赁土地租金：{{ detailData.landRent || '-' }}</span>
            <span class="w-[50%]">地价款（租金）欠款金额：{{ detailData.arrearsAmount || '-' }}</span>
          </div>
          <h2 class="text-[16px] font-bold mb-[12px] mt-[12px]">房屋建筑物信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">
              房产类型：{{ renderDict(detailData.houseType, 'CT_BASE_ENUM_HouseOwner_HouseType') || '-' }}
            </span>
            <span class="w-[50%]">建筑面积：{{ renderMoney(detailData.structureArea, 4) || '-' }}</span>
            <span class="w-[50%]">宗地面积（㎡）：{{ renderMoney(detailData.floorArea, 4) || '-' }}</span>
            <span class="w-[50%]">
              建筑结构：{{ renderDict(detailData.buildStructrue, 'CT_BAS_BuildStructrue') || '-' }}
            </span>
            <span class="w-[50%]">建筑年份：{{ detailData.buildYear || '-' }}</span>
            <span class="w-[50%]">
              <span>层数/总层数：</span>
              <span>{{ detailData.layerNum || '-' }}</span>
              <span v-if="detailData.totalLayerNum">/{{ detailData.totalLayerNum }}</span>
            </span>
            <span class="w-[50%]">层高（m）：{{ detailData.layerHight || '-' }}</span>
            <span class="w-[50%]">户型：{{ detailData.houseModel || '-' }}</span>
            <span class="w-[50%]">
              消防等级（㎡）：{{ renderDict(detailData.firefightingRate, 'CT_BAS_FirefightingRate') || '-' }}
            </span>
            <span class="w-[50%]">
              房屋安全等级：{{ renderDict(detailData.houseSafeRate, 'CT_BAS_HouseSafeRate') || '-' }}
            </span>
          </div>
        </template>
        <template #taxInfo>
          <h2 class="text-[16px] font-bold mb-[12px]">税务信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">产权比例（%）：{{ renderMoney(detailData.propertyRate, 4) || '-' }}</span>
            <span class="w-[50%]">房产税计税原值：{{ detailData.houseTaxOrgValue || '-' }}</span>
            <span class="w-[50%]">增值税率（%）：{{ renderMoney(detailData.addTaxRate, 4) || '-' }}</span>
            <span class="w-[50%]">从价月税率（%）：{{ renderMoney(detailData.leaseMonthRate, 4) || '-' }}</span>
            <span class="w-[50%]">已租赁面积（㎡）：{{ renderMoney(detailData.haveLeaseArea, 4) || '-' }}</span>
            <span class="w-[50%]">土地面积（㎡）：{{ renderMoney(detailData.landArea, 4) || '-' }}</span>
            <span class="w-[50%]">
              土地使用税年收费标准：{{ renderMoney(detailData.landUseMonthlyRate, 2) || '-' }}
            </span>
            <span class="w-[50%]">取得日期：{{ detailData.getDate || '-' }}</span>
            <span class="w-[50%]">处置日期：{{ detailData.dealDate || '-' }}</span>
            <span class="w-[50%]">接收日期：{{ detailData.receiveDate || '-' }}</span>
            <span class="w-[50%]">视同销售计提税费：{{ getDicName(detailData.stSaleJTRate, isOrNotDic) || '-' }}</span>
          </div>
        </template>
        <template #fileInfo>
          <h2 class="text-[16px] font-bold mb-[12px]">附件</h2>
          <file-list :biz-id="detailData.id"></file-list>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <!-- 编辑 -->
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
  <!-- 资产处置 新增 -->
  <assets-disposal-add-edit v-if="visible" ref="assetsDisposalAddEditRef"></assets-disposal-add-edit>
  <!-- 资产跟踪 新增 -->
  <assets-track-add-edit v-if="visible" ref="assetsTrackAddEditRef"></assets-track-add-edit>
</template>
<script setup>
// import AssetsDisposalAddEdit from '@/views/assets/disposal/components/AddEdit.vue'
import AssetsTrackAddEdit from '@/views/assets/track/components/AddEdit.vue'
import { isOrNotDic, getDicName } from '@/store/modules/dict.js'
import { renderDict, renderMoney, renderRegion, renderBoolean } from '@/utils/render'
import { Modal, message } from 'ant-design-vue'
import { delById, detailById } from '../apis'
import AddEdit from './AddEdit.vue'
import { getFileAccessHttpUrl } from '@/apis/common'

const AssetsDisposalAddEdit = defineAsyncComponent(() => import('@/views/assets/disposal/components/AddEdit.vue'))

const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} id 列表行id用于查看详情
 */
const open = (id) => {
  visible.value = true
  if (id) {
    getDetailById(id)
  }
}
defineExpose({ open })

// 通过id获取详情
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  result.ownerCover = getFileAccessHttpUrl(result.ownerCover)
  result.ownerInside = getFileAccessHttpUrl(result.ownerInside)
  result.ownerRealImage = getFileAccessHttpUrl(result.ownerRealImage)
  detailData.value = result
}
// 导航项
const navList = [
  { name: 'baseInfo', title: '基础信息', showTitle: false },
  { name: 'taxInfo', title: '税务信息', showTitle: false },
  { name: 'fileInfo', title: '附件信息', showTitle: false }
]

const detailData = ref({
  attachmentList: []
})
const loading = ref(false)

// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '确认删除当前资产？',
    content: '',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      visible.value = false
    }
  })
}
// 页面跳转
// const router = useRouter()
const assetsDisposalAddEditRef = ref()
const assetsTrackAddEditRef = ref()
const turnToPage = (type) => {
  // 资产处置
  if (type === 1) {
    assetsDisposalAddEditRef?.value.open({}, [
      {
        id: '',
        houseOwner: detailData.value.id,
        houseOwner_dictText: detailData.value.name,
        detailAddress: `${renderRegion(detailData.value.pcaCode)}${detailData.value.detailAddress}`,
        transferArea: '',
        evaluatePrice: '',
        transferee: '',
        dealExplain: '',
        deal: true,
        parent: '',
        sourceBillId: '',
        sourceBillEntryId: ''
      }
    ])
    // // 带查询参数
    // router.push({ path: '/assets/disposal', query: { adding: true } })
    return
  }
  // 资产跟踪
  assetsTrackAddEditRef.value.open({}, detailData.value)
  // if (type === 2) {
  // 带查询参数
  // router.push({ path: '/assets/track', query: { adding: true } })
  // }
}
</script>
