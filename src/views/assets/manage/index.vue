<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button v-auth="'bas:ct_bas_house_owner:add'" class="mb-[10px]" type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button v-auth="'bas:ct_bas_house_owner:importExcel'" class="mb-[10px]" @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'bas:ct_bas_house_owner:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="handleGenLeaseUnit(false)">生成租赁单元</div>
              </a-menu-item>
              <a-menu-item>
                <div v-auth="'bas:ct_bas_house_owner:deleteBatch'" class="primary-btn" @click="handleRemove(false)">
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]" label="类别">
          <assets-category-tree-filter class="!w-[280px]" @tree-node-change="handleTreeNodeChange" />
        </a-form-item>
        <a-form-item class="!mb-[10px]" label="">
          <s-input v-model="search.name" placeholder="搜索资产名称" class="!w-[280px]" @input="handleInput"></s-input>
        </a-form-item>
        <a-form-item>
          <search-more
            v-model="searchFilter"
            :search-list="searchList"
            @filterItemChange="filterItemChange"
            @searchChange="onTableChange"
          ></search-more>
        </a-form-item>
      </a-form>
      <columns-set class="!mb-[10px]" :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2500 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'layerNum'">
          <div>
            <span>{{ record.layerNum }}</span>
            <span v-if="record.layerNum && record.totalLayerNum">/</span>
            <span>{{ record.totalLayerNum }}</span>
          </div>
        </template>

        <!--暂存:TEMP；已撤回:BACK；审核中:AUDITING;审核不通过:AUDITNO;审核通过:AUDITOK;启用:ENABLE;禁用:DISABLE;关闭:CLOSED;意向:INTEND-->
        <template v-if="column.dataIndex === 'action'">
          <span v-auth="'bas:ct_bas_house_owner:view'" class="primary-btn" @click="rowView(record)">查看</span>
          <!--  暂存 已撤回 审核不通过 才有 -->
          <span
            v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)"
            v-auth="'bas:ct_bas_house_owner:edit'"
            class="primary-btn"
            @click="rowEdit(record)"
          >
            编辑
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <!-- 审核通过的资产才能生成租赁单元 -->
                <a-menu-item v-if="record.status === 'AUDITOK'">
                  <div v-auth="'bas:ct_bas_lease_unit:add'" class="primary-btn" @click="handleGenLeaseUnit(record)">
                    生成租赁单元
                  </div>
                </a-menu-item>
                <a-menu-item>
                  <div v-auth="'bas:ct_bas_lease_unit:list'" class="primary-btn" @click="rowViewGenLeaseUnit(record)">
                    查看租赁单元
                  </div>
                </a-menu-item>
                <!-- 审核中才有审核操作（临时） -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div v-auth="'bas:ct_bas_house_owner:audit'" class="primary-btn" @click="rowVerify(record)">
                    审核通过(临时)
                  </div>
                </a-menu-item>
                <!-- 审核通过才能 反审核 -->
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div v-auth="'bas:ct_bas_house_owner:unAudit'" class="primary-btn" @click="rowReverse(record)">
                    反审核
                  </div>
                </a-menu-item>
                <!-- 审核中才有撤回操作 -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div v-auth="'bas:ct_bas_house_owner:edit'" class="primary-btn" @click="rowBack(record)">撤回</div>
                </a-menu-item>
                <!-- 暂存 已撤回 才有删除 -->
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div v-auth="'bas:ct_bas_house_owner:delete'" class="primary-btn" @click="handleRemove(record)">
                    删除
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <!-- 详情 -->
    <detail ref="detailRef" @load-data="onTableChange"></detail>
    <!-- 租赁单元生成 -->
    <edit-lease-unit ref="leaseUnitRef" @refresh="onTableChange"></edit-lease-unit>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产数据"
      :download-fn="() => exportExcel('资产数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import EditLeaseUnit from '@/views/leaseUnit/manage/components/EditLeaseUnit.vue'
import AssetsCategoryTreeFilter from './components/AssetsCategoryTreeFilter.vue'
import { renderDict, renderMoney, renderDictTag, renderRegion } from '@/utils/render'
import areaList from '@/json/region.json'
import usePageTable from '@/hooks/usePageTable'
import AddEdit from './components/AddEdit.vue'
import Detail from './components/Detail.vue'
import useTableSelection from '@/hooks/useTableSelection'
import {
  getPage,
  genLeaseUnit,
  deleteBatch,
  getQueryWyBuildingByMainId,
  getQueryWyFloorByMainId,
  exportExcel,
  importExcel,
  viewRelativeLeaseUnit,
  back,
  audit,
  unAudit
} from './apis'
import { isOrNotDic } from '@/store/modules/dict.js'
import { Modal, message } from 'ant-design-vue'
import { projectPage } from '@/views/projects/apis.js'
import { getFilesById } from '@/apis/common'

onMounted(() => {
  onTableChange()
})
/**
 * 处理树节点变更
 */
const handleTreeNodeChange = (nodeId) => {
  search.value.treeId = nodeId
  onTableChange({ current: 1 })
}
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
    item.allAddress = `${renderRegion(item.pcaCode)}${item.detailAddress}`
  })
  return list
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({
    pageNo: current ?? pageNo,
    pageSize,
    ...search.value,
    ...searchFilter.value,
    pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(',') : ''
  })
}
const defaultColumns = [
  { title: '资产名称', dataIndex: 'name', width: 180, fixed: true },
  {
    title: '单据审核状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '资产编号', dataIndex: 'number', width: 150 },
  { title: '权证号', dataIndex: 'ownerNumber', ellipsis: true },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  {
    title: '使用权类型',
    dataIndex: 'landNature',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
  },
  { title: '地址', dataIndex: 'allAddress', width: 200, ellipsis: true },

  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_BizStatus')
  },
  {
    title: '建筑面积（㎡）',
    dataIndex: 'structureArea',
    customRender: ({ text }) => renderMoney(text, 4)
  },
  { title: '宗地面积（㎡）', dataIndex: 'floorArea', customRender: ({ text }) => renderMoney(text, 4) },
  {
    title: '房产类型',
    dataIndex: 'houseType',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_HouseType')
  },
  {
    title: '资产类型',
    dataIndex: 'assetsType',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_AssetsType')
  },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', ellipsis: true },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
  { title: '层数/总层数', dataIndex: 'layerNum' },
  { title: '备注', dataIndex: 'remark', ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const search = ref({
  column: 'number',
  order: 'desc',
  treeId: '',
  name: ''
})
const searchFilter = ref({
  number: undefined,
  wyProject: '',
  wyBuilding: '',
  wyFloor: '',
  pcaCode: [],
  collectionCompany: '',
  ownerCompany: '',
  manageCompany: '',
  assetsType: '',
  bizStatus: '',
  status: '',
  acquisitionMethod: '',
  propertyRightStatus: '',
  propertyUse: '',
  landNature: '',
  landConstructionSituation: '',
  houseType: '',
  buildStructrue: '',
  houseModel: undefined,
  firefightingRate: '',
  houseSafeRate: '',
  createTime: undefined,
  createBy: '',
  updateTime: undefined,
  updateBy: '',
  auditTime: undefined,
  auditBy: '',
  detailAddress: undefined,
  warrantsDate: undefined,
  isUnionCertificate: undefined,
  buildYear: undefined
})

const wyBuildingPar = reactive({ id: '' })
const getQueryWyBuildingByMainIdFunc = computed(() => {
  wyBuildingPar.id
  return () => getQueryWyBuildingByMainId(wyBuildingPar)
})
const wyFloorPar = reactive({ id: '' })
const getQueryWyFloorByMainIdFunc = computed(() => {
  wyFloorPar.id
  return () => getQueryWyFloorByMainId(wyFloorPar)
})
const searchList = reactive([
  { label: '资产编号', name: 'number', type: 's-input', placeholder: '请输入资产编号' },
  { label: '权证号', name: 'ownerNumber', type: 'input', placeholder: '请输入权证号' },
  {
    label: '关联项目',
    name: 'wyProject',
    type: 'api',
    placeholder: '请选择关联项目',
    listFunc: projectPage
  },
  {
    label: '楼栋',
    name: 'wyBuilding',
    type: 'api',
    placeholder: '请选择楼栋',
    listFunc: getQueryWyBuildingByMainIdFunc
  },
  {
    label: '楼层',
    name: 'wyFloor',
    type: 'api',
    placeholder: '请选择楼层',
    listFunc: getQueryWyFloorByMainIdFunc
  },
  { label: '区域', name: 'pcaCode', type: 'cascader', placeholder: '请选择区域', list: areaList },
  { label: '租金归集公司', name: 'collectionCompany', type: 'companySelect', placeholder: '请选择租金归集公司' },
  { label: '资产权属公司', name: 'ownerCompany', type: 'companySelect', placeholder: '请选择资产权属公司' },
  { label: '物业管理公司', name: 'manageCompany', type: 'companySelect', placeholder: '请选择物业管理公司' },
  { label: '资产类型', name: 'assetsType', type: 'dic', placeholder: '请选择资产类型', code: 'CT_BAS_AssetsType' },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dic',
    placeholder: '请选择业务状态',
    code: 'CT_BASE_ENUM_HouseOwner_BizStatus'
  },
  { label: '启用状态', name: 'status', type: 'dic', placeholder: '请选择启用状态', code: 'CT_BASE_ENUM_AuditStatus' },
  {
    label: '取得来源',
    name: 'acquisitionMethod',
    type: 'dic',
    placeholder: '请选择取得来源',
    code: 'CT_BAS_AcquisitionMethod'
  },
  {
    label: '产权情况',
    name: 'propertyRightStatus',
    type: 'dic',
    placeholder: '请选择产权情况',
    code: 'CT_BASE_ENUM_HouseOwner_PropertyRightStatus'
  },
  { label: '产权用途', name: 'propertyUse', type: 'dic', placeholder: '请选择产权用途', code: 'CT_BAS_PropertyUse' },
  { label: '使用权类型', name: 'landNature', type: 'dic', placeholder: '请选择使用权类型', code: 'CT_BAS_LandNature' },
  {
    label: '土地建设情况',
    name: 'landConstructionSituation',
    type: 'dic',
    placeholder: '请选择土地建设情况',
    code: 'CT_BAS_LandCS'
  },
  {
    label: '房产类型',
    name: 'houseType',
    type: 'dic',
    placeholder: '请选择房产类型',
    code: 'CT_BASE_ENUM_HouseOwner_HouseType'
  },
  {
    label: '建筑结构',
    name: 'buildStructrue',
    type: 'dic',
    placeholder: '请选择房产类型',
    code: 'CT_BAS_BuildStructrue'
  },
  { label: '户型', name: 'houseModel', type: 'input', placeholder: '请输入户型' },
  {
    label: '消防等级',
    name: 'firefightingRate',
    type: 'dic',
    placeholder: '请选择消防等级',
    code: 'CT_BAS_FirefightingRate'
  },
  {
    label: '房屋安全等级',
    name: 'houseSafeRate',
    type: 'dic',
    placeholder: '请选择房屋安全等级',
    code: 'CT_BAS_HouseSafeRate'
  },
  { label: '创建时间', name: 'createTime', type: 'date', placeholder: '请选择创建时间' },
  {
    label: '创建人',
    name: 'createBy',
    type: 'userSelect',
    placeholder: '请选择创建人'
  },
  { label: '最近修改时间', name: 'updateTime', type: 'date', placeholder: '请选择最近修改时间' },
  {
    label: '最近修改人',
    name: 'updateBy',
    type: 'userSelect',
    placeholder: '请选择最近修改人'
  },
  { label: '审核时间', name: 'auditTime', type: 'date', placeholder: '请选择审核时间' },
  {
    label: '审核人',
    name: 'auditBy',
    type: 'userSelect',
    placeholder: '请选择审核人'
  },
  { label: '详细地址', name: 'detailAddress', type: 'input', placeholder: '请输入详细地址' },
  { label: '权证获得日期', name: 'warrantsDate', type: 'date', placeholder: '请选择权证获得日期' },
  {
    label: '房地权证合一',
    name: 'isUnionCertificate',
    type: 'select',
    placeholder: '请选择房地权证合一',
    list: isOrNotDic
  },
  { label: '建筑年份', name: 'buildYear', type: 'year', format: 'YYYY', placeholder: '请选择建筑年份' }
])

// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row.id)
}
// 编辑
const rowEdit = async (row) => {
  const result = await getFilesById(row.id)
  const ruleForm = { ...row, attachmentIds: result.map((item) => item.id).join(',') }
  addEditRef?.value.open(ruleForm)
}

// 查看租赁单元
const router = useRouter()
const rowViewGenLeaseUnit = async (row) => {
  const { result } = await viewRelativeLeaseUnit(row.id)
  // 有关联租赁单元
  if (result.length) {
    return router.push({ path: '/leaseUnit/manage', query: { id: row.id } })
  }
  message.warning('暂无关联租赁单元')
}
// 批量生成租赁单元
const leaseUnitRef = ref()
const handleGenLeaseUnit = (data) => {
  Modal.confirm({
    title: data ? '确认生成租赁单元？' : '确认批量生成租赁单元？',
    content: '',
    centered: true,
    onOk: async () => {
      // 单个生成
      if (data) {
        leaseUnitRef?.value.open(
          {
            virtualLeaseUnit: true,
            houseOwner: data.id,
            houseOwner_dictText: data.name,
            wyProject: data.wyProject,
            wyBuilding: data.wyBuilding,
            wyFloor: data.wyFloor,
            wyProjectArray: [data.wyProject, data.wyBuilding, data.wyFloor].filter(Boolean),
            province: data.province,
            city: data.city,
            area: data.area,
            pcaCode: data.pcaCode ? data.pcaCode.split(',') : [],
            detailAddress: data.detailAddress,
            id: undefined,
            name: '',
            propertyUse: data.propertyUse,
            assetType: '', // 资产的资产类型和租赁单元的资产类型不一致
            ownerCompany: data.ownerCompany,
            collectionCompany: data.collectionCompany,
            manageCompany: data.manageCompany,
            landNature: data.landNature,
            treeId: '',
            supportFacility: '',
            remark: data.remark,
            useType: '',
            leaseArea: '',
            leaseUse: '',
            areaManager: '',
            effectDate: '',
            expireDate: '',
            houseType: data.houseType,
            structureArea: data.structureArea,
            floorArea: data.floorArea,
            buildStructrue: data.buildStructrue,
            buildYear: data.buildYear,
            layerNum: data.layerNum,
            layerHight: data.layerHight,
            houseModel: data.houseModel,
            firefightingRate: data.firefightingRate,
            houseSafeRate: data.houseSafeRate,
            houseTaxOrgValue: data.houseTaxOrgValue,
            addTaxRate: data.addTaxRate,
            invoiceAddress: '',
            waterShareFormulas: [],
            attachmentIds: data.attachmentIds,
            currentLayer: '',
            totalLayer: data.totalLayerNum
          },
          true
        )
        return
      }
      // 批量生成
      const responseData = await genLeaseUnit(selectedRowKeys.value.join(','))
      message.success(responseData.message)
      clearSelection()
      onTableChange()
    }
  })
}
// 审核操作（临时）
const rowVerify = (row) => {
  Modal.confirm({
    title: '确认审核通过？',
    content: '',
    async onOk() {
      const data = await audit({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
// 反审核
const rowReverse = (row) => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await unAudit({ id: row.id })
      message.success(data.message)
      onTableChange({ pageNo: pagination.value.current, pageSize: pagination.value.pageSize })
    }
  })
}
// 撤回
const rowBack = (row) => {
  // if (!hasPermission('biz.funds:ct_fun_refund_req_bill:back')) return
  Modal.confirm({
    title: '确认撤回该资产？',
    content: '',
    async onOk() {
      const data = await back({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}

// 批量删除
const handleRemove = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前资产？' : '确认批量删除选中资产？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

// 筛选项字段和对应的值
const filterItemChange = (id, name) => {
  // 关联项目
  if (name === 'wyProject') {
    searchList.forEach((item) => {
      if (item.name === 'wyBuilding') {
        wyBuildingPar.id = id
      }
    })
  }
  // 楼栋
  if (name === 'wyBuilding') {
    searchList.forEach((item) => {
      if (item.name === 'wyFloor') {
        wyFloorPar.id = id
      }
    })
  }
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('资产数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(',') : '',
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
