<template>
  <div>
    <div class="flex border border-[#E6E9F0] rounded-[8px]">
      <div class="!w-[316px] p-[20px] border-r-1 border-r-[#E6E9F0]">
        <div class="flex justify-between items-center pb-[20px]">
          <h2 class="font-bold text-[18px]">服务类型</h2>
          <div class="cursor-pointer text-primary text-[14px]" @click="handleServeTypeAdd">
            <span class="a-icon-plus font-bold mr-[4px]"></span>
            <span>添加类型</span>
          </div>
        </div>
        <draggable v-model="tabList" handle=".a-icon-move" item-key="id">
          <template #item="{ element }">
            <div
              class="group flex justify-between items-center border rounded-[8px] p-[16px] mb-[16px]"
              :class="
                search.serviceType === element.itemValue
                  ? ' border-[#165dff] bg-[#eaf0fe]'
                  : 'border-[#e6e9f0] bg-[#f7f8fa] '
              "
              @click="handleClick(element)"
            >
              <div class="flex text-[14px]">
                <i class="a-icon-move cursor-move text-secondary mr-[10px]"></i>
                <div>{{ element.itemText }}</div>
              </div>
              <div class="text-[14px] hidden group-hover:block">
                <span
                  class="a-icon-shanchu text-primary mr-[10px] cursor-pointer"
                  @click="handleServeTypeDel(element)"
                ></span>
                <span class="a-icon-edit text-primary cursor-pointer" @click="handleServeTypeEdit(element)"></span>
              </div>
            </div>
          </template>
        </draggable>
      </div>

      <div class="!w-[calc(100%-316px)] m-[20px]">
        <h2 class="font-bold text-[18px]">{{ activeTitle }}</h2>
        <div class="flex justify-between !mt-[24px] !mb-[14px]">
          <a-form autocomplete="off" layout="inline">
            <a-button class="mb-[10px]" type="primary" @click="handleAdd">
              <span class="a-icon-plus mr-[8px]"></span>
              新建
            </a-button>
            <a-button class="mb-[10px]" @click="handleImport">
              <span class="a-icon-import-right mr-[8px]"></span>
              导入
            </a-button>
            <a-button class="mb-[10px]" :loading="exportLoading" @click="handleExport">
              <span class="a-icon-export-right mr-[8px]"></span>
              导出
            </a-button>
            <a-dropdown v-if="selectedRowKeys.length">
              <a-button class="mb-[10px]">
                <span>批量操作</span>
                <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <div class="primary-btn" @click="handleRemove(false)">删除</div>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
              <i class="a-icon-refresh"></i>
              刷新
            </a-button>
            <a-form-item label="搜索" class="!ml-[40px]">
              <s-input
                v-model="search.name"
                placeholder="搜索名称"
                class="ml-[10px] !w-[280px]"
                @input="handleInput"
              ></s-input>
            </a-form-item>
            <a-form-item>
              <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
            </a-form-item>
          </a-form>
          <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
        </div>
        <a-table
          :data-source="list"
          :columns="columns"
          :loading="tableLoading"
          :scroll="{ y: tableHeight, x: 1000 }"
          :pagination="pagination"
          row-key="id"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          @change="onTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
            </template>
            <template v-if="column.dataIndex === 'action'">
              <span class="primary-btn" @click="rowEdit(record)">编辑</span>
              <span class="primary-btn" @click="handleRemove(record)">删除</span>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <!-- 服务类型新增和编辑 -->
    <service-type-add-edit
      ref="serviceTypeAddEditRef"
      @load-data="getOrderChargeProjectServiceTypeList"
    ></service-type-add-edit>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入订单计费项"
      :download-fn="() => exportExcel('订单计费项数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import draggable from 'vuedraggable'
import {
  getPage,
  deleteBatch,
  exportExcel,
  importExcel,
  orderChargeProjectServiceTypeList,
  serviceTypeDelete,
  updateStatus
} from './apis'
import AddEdit from './components/AddEdit.vue'
import ServiceTypeAddEdit from './components/ServiceTypeAddEdit.vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message, Modal } from 'ant-design-vue'
import { renderMoney } from '@/utils/render'
onMounted(() => {
  getOrderChargeProjectServiceTypeList()
})
const activeTitle = ref('')
const getOrderChargeProjectServiceTypeList = async () => {
  const { result } = await orderChargeProjectServiceTypeList({ pageNo: 1, pageSize: 100 })
  tabList.value = result.records
  if (result.records.length) {
    search.value.serviceType = result.records[0].itemValue
    activeTitle.value = result.records[0].itemText
    onTableChange()
  }
}
const handleClick = (row) => {
  search.value.serviceType = row.itemValue
  activeTitle.value = row.itemText
  onTableChange()
}
// 启用禁用操作
const handleStatusChange = async (row, val) => {
  if (row.loading) return
  try {
    row.loading = true
    const data = await updateStatus({ ids: row.id, status: val ? 'ENABLE' : 'DISABLE' })
    row.loading = false
    message.success(data.message)
    onTableChange()
    // data.status = val ? 'ENABLE' : 'DISABLE'
  } catch {
    row.loading = false
    row.checked = !val
  }
}
// 添加服务类型
const serviceTypeAddEditRef = ref()
const handleServeTypeAdd = () => {
  serviceTypeAddEditRef.value.open()
}
// 编辑服务类型
const handleServeTypeEdit = (row) => {
  serviceTypeAddEditRef.value.open(row)
}
// 删除服务类型
const handleServeTypeDel = (row) => {
  Modal.confirm({
    title: '确认删除当前服务类型吗？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await serviceTypeDelete({ id: row.id })
      message.success(data.message)
      getOrderChargeProjectServiceTypeList()
    }
  })
}

const tabList = ref([])
const search = ref({
  column: 'number',
  order: 'desc',
  name: '',
  serviceType: ''
})
const searchFilter = ref({
  number: ''
})
const searchList = reactive([{ label: '计费项编码', name: 'number', type: 'input', placeholder: '请输入计费项编码' }])
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
const defaultColumns = [
  { title: '计费项编码', dataIndex: 'number', width: 180, fixed: true },
  { title: '计费项名称', dataIndex: 'name' },
  { title: '启用状态', dataIndex: 'status' },
  { title: '税率%', dataIndex: 'taxRate', customRender: ({ text }) => renderMoney(text, 4) },
  { title: '备注', dataIndex: 'remark' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value })
}
// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open({ serviceType: search.value.serviceType })
}
// 编辑
const rowEdit = (row) => {
  addEditRef?.value.open(row)
}
// 批量删除
const handleRemove = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前订单计费项？' : '确认批量删除选中订单计费项？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('订单计费项数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
