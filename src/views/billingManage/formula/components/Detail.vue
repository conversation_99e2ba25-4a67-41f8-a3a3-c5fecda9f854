<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="水电分摊公式详情"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <a-dropdown>
        <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
          <span>操作</span>
          <i class="a-icon-arrow-down ml-[4px]"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item><div class="primary-btn" @click="handleEdit">编辑</div></a-menu-item>
            <a-menu-item>
              <div class="primary-btn" @click="handleStatusChange">
                {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
              </div>
            </a-menu-item>
            <a-menu-item><div class="primary-btn" @click="handleDelete">删除</div></a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.name }}</h2>
        <status-tag :dict-value="detail.status" dict-code="CT_BASE_ENUM_BaseStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <div class="formula-generator">
        <div class="formula-generator__editor">
          <div class="formula-generator__editor-title">
            <div>公式</div>
            <div>
              <span class="primary-btn ml-[10px]" @click="showScript">查看脚本</span>
            </div>
          </div>
          <div class="formula-generator__editor-content">
            <FormulaCodemirror v-model="detail.formulaZn" disabled />
          </div>
        </div>
      </div>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">公式名称: {{ detail.name }}</span>
        <span class="w-[50%]">计算结果: {{ detail.calculResult_dictText }}</span>
        <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-full">计算逻辑解释: {{ detail.calcalLoginExplain }}</span>
        <span class="w-full">备注: {{ detail.remark }}</span>
      </div>
    </a-spin>
  </a-drawer>

  <formula-detail ref="formulaDetailRef" />
</template>

<script setup>
import { detail as getDetail, deleteBatch } from '../apis.js'
import useFormula from '../useFormual.js'
import FormulaDetail from './FormulaDetail.vue'
import { Modal, message } from 'ant-design-vue'

const { getScriptZn, getScript } = useFormula()

const emit = defineEmits(['edit', 'refresh', 'statusChange'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const loading = ref(false)
const detail = reactive({})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  result.formulaZn = getScriptZn(result.formula)
  Object.assign(detail, result)
  loading.value = false
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该水电分摊公式？',
    content: '',
    centered: true,
    onOk: async () => {
      const { message: msg } = await deleteBatch({ ids: detail.id })
      message.success(msg)
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleStatusChange = () => {
  emit('statusChange', detail, detail.status !== 'ENABLE')
}

const handleClose = () => {
  visible.value = false
}

const formulaDetailRef = ref(null)
const showScript = () => {
  const formula = getScript(detail.formulaZn)
  formulaDetailRef?.value?.open(formula, '查看脚本')
}

defineExpose({ open })
</script>

<style lang="less" scoped>
.formula-generator {
  width: 100%;
  margin-bottom: 15px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  &__editor {
    flex-basis: 200px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    &-title {
      display: flex;
      align-items: center;
      padding: 5px 10px;
    }
    &-content {
      flex: 1;
      overflow: auto;
    }
  }
}
</style>
