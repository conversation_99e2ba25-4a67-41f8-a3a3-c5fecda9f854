<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.name"
          placeholder="搜索名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'formula'">
          <span class="primary-btn" @click="handleViewFormula(record)">查看</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item><div class="primary-btn" @click="handleEdit(record)">编辑</div></a-menu-item>
                <a-menu-item><div class="primary-btn" @click="handleRemove(record)">删除</div></a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail ref="detailRef" @edit="handleEdit" @statusChange="handleStatusChange" @refresh="refreshFromDetail"></detail>
    <formula-detail ref="formulaDetailRef"></formula-detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('水电分摊公式.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, enable, disable, deleteBatch, exportExcel, importExcel } from './apis.js'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'
import FormulaDetail from './components/FormulaDetail.vue'

const params = reactive({
  column: 'createTime',
  order: 'desc',
  name: undefined,
  calculResult: undefined,
  status: undefined,
  calcalLoginExplain: undefined,
  formula: undefined,
  number: undefined
})

const searchList = [
  { label: '计算结果', name: 'calculResult', type: 's-input' },
  { label: '启用状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_BaseStatus' },
  {
    label: '计算逻辑解释',
    name: 'calcalLoginExplain',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_WaterShareFormulaManagement_CalculResult'
  },
  { label: '公式', name: 'formula', type: 's-input' },
  { label: '公式编号', name: 'number', type: 's-input' }
]

const defaultColumns = [
  { title: '公式名称', dataIndex: 'name', width: 160, fixed: 'left' },
  { title: '计算结果', dataIndex: 'calculResult_dictText', width: 100 },
  { title: '启用状态', dataIndex: 'status', width: 100 },
  { title: '计算逻辑解释', dataIndex: 'calcalLoginExplain', width: 200 },
  { title: '公式', dataIndex: 'formula', width: 80 },
  { title: '公式编号', dataIndex: 'number', width: 120 },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 120 },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

const formulaDetailRef = ref()
const handleViewFormula = (data) => {
  formulaDetailRef.value.open(data.formula, '查看公式')
}

const handleStatusChange = (data, val) => {
  if (data.loading) return
  if (!val) {
    Modal.confirm({
      title: '确定禁用？',
      content: '禁用后将无法再被使用，但不影响已创建数据。',
      centered: true,
      onOk: () => {
        confirmUpdate(data, val)
      },
      onCancel: () => {
        data.loading = false
        data.checked = !val
      }
    })
    return
  }
  confirmUpdate(data, val)
}

const confirmUpdate = async (data, val) => {
  try {
    data.loading = true
    val ? await enable(data.id) : await disable(data.id)
    data.loading = false
    message.success('保存成功')
    data.status = val ? 'ENABLE' : 'DISABLE'
    refresh()
  } catch {
    data.loading = false
    data.checked = !val
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除水电分摊公式信息？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('水电分摊公式数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
