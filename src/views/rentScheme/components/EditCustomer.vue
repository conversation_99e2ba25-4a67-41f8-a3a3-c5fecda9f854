<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑竞标客户' : '新增竞标客户'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '70px' } }"
      autocomplete="off"
    >
      <a-form-item name="customerType">
        <a-radio-group v-model:value="form.customerType">
          <a-radio v-for="item in customerTypeList" :key="item.value" :value="item.value">{{ item.label }}</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="客户名称" name="name">
        <a-auto-complete
          v-model:value="form.name"
          :options="customerList"
          placeholder="请输入客户名称"
          @select="handleSelectCustomer"
          @search="loadCustomerList"
        >
          <template #option="{ value, id }">
            <span>{{ value }}</span>
            <span class="text-tertiary text-[12px] ml-[6px]">{{ id ? '(系统客户)' : '' }}</span>
          </template>
        </a-auto-complete>
      </a-form-item>
      <a-form-item label="联系人" name="linkman">
        <a-input v-model:value="form.linkman" placeholder="请输入联系人" :maxlength="60" />
      </a-form-item>
      <a-form-item label="手机号码" name="linkmanPhone">
        <a-input v-model:value="form.linkmanPhone" placeholder="请输入手机号码" :maxlength="11" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark" show-count :maxlength="200" :auto-size="{ minRows: 4, maxRows: 4 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { validatePhone } from '@/utils/validate'
import { addCustomer, editCustomer } from '../apis'
import { useDictStore } from '@/store/modules/dict'
import { getCustomerList } from '@/views/customer/manage/apis'

const { detailId } = defineProps({
  detailId: { required: true, type: String }
})

const emits = defineEmits(['refresh'])

const { getDictItems } = useDictStore()
const customerTypeList = getDictItems('CT_BASE_ENUM_Customer_CustomerType')

const visible = ref(false)
const open = async (data) => {
  form.parent = detailId
  if (data) {
    Object.assign(form, data)
    // 如果是系统客户，则为customerList赋值
    if (data.customer) {
      const { result } = await getCustomerList({ id: data.customer })
      customerList.value = [
        {
          value: result.records[0].name,
          id: result.records[0].id,
          customerType: result.records[0].customerType,
          linkman: result.records[0].linkman,
          linkmanPhone: result.records[0].linkmanPhone
        }
      ]
    }
  }
  visible.value = true
}
const form = reactive({
  id: '',
  parent: '',
  name: '',
  customerType: 'company',
  customer: '', // 系统客户id
  linkman: '',
  linkmanPhone: '',
  remark: '',
  isSuccessBidding: false
})

const rules = {
  name: [{ required: true, message: '请输入竞标客户名称', trigger: 'blur' }],
  phone: [{ required: false, validator: validatePhone(false, '手机号码'), trigger: 'blur' }]
}

let timer
const customerList = ref([])
const loadCustomerList = (keyword) => {
  clearTimeout(timer)
  timer = setTimeout(async () => {
    if (!(keyword && keyword.length > 1)) {
      customerList.value = []
      return
    }
    const { result } = await getCustomerList({ pageNo: 1, pageSize: 100, name: keyword })
    const inputData = {
      value: keyword,
      id: '',
      customerType: 'company',
      linkman: '',
      linkmanPhone: ''
    }
    customerList.value = [inputData].concat(
      result.records.map((item) => ({
        value: item.name,
        id: item.id,
        customerType: item.customerType,
        linkman: item.linkman,
        linkmanPhone: item.linkmanPhone
      }))
    )
  }, 500)
}
const handleSelectCustomer = (_, option) => {
  form.customer = option.id
  form.name = option.value
  form.customerType = option.customerType
  form.linkman = option.linkman
  form.linkmanPhone = option.linkmanPhone
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await editCustomer(form) : await addCustomer(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.id = ''
  form.parent = ''
  form.name = ''
  form.customerType = 'company'
  form.linkman = ''
  form.linkmanPhone = ''
  form.remark = ''
  form.isSuccessBidding = false
  form.customer = ''
  customerList.value = []
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
