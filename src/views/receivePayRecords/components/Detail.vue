<template>
  <a-drawer v-model:open="visible" title="收付款记录" class="common-detail-drawer" placement="right" width="1072px">
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span class="primary-btn" @click="handleEdit">编辑</span>
        <a-dropdown>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down text-[12px]"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="turnToPage">核销记录</div>
              </a-menu-item>
              <a-menu-item>
                <div class="primary-btn" @click="handleDel">删除</div>
              </a-menu-item>
              <a-menu-item>
                <div class="primary-btn" @click="handleUnAudit">反审批</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <!-- ：{{ detailData.name }} -->
        <h2 class="text-[18px] font-bold mr-[12px]">收付款记录</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detailData.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>
      <!-- 锚点导航 -->
      <anchor-tabs :tab-list="navList" height="calc(100vh - 295px)">
        <template #baseInfo>
          <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">实收金额：{{ renderMoney(detailData.actualReceiveAmt, 2) || '-' }}</span>
            <span class="w-[50%]">手续费：{{ renderMoney(detailData.serviceCharge, 2) || '-' }}</span>
            <span class="w-[50%]">收款公司：{{ detailData.manageCompany_dictText || '-' }}</span>
            <span class="w-[50%]">发生时间：{{ detailData.receiveDate || '-' }}</span>
            <span class="w-[50%]">所属客户：{{ detailData.customer_dictText || '-' }}</span>
            <span class="w-[50%]">实际来款人：{{ detailData.actualPayor || '-' }}</span>
            <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
            <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
            <span class="w-[50%]">
              业务状态：{{ renderDict(detailData.bizStatus, 'CT_BASE_ENUM_PayExplainBook_BizStatus') || '-' }}
            </span>
            <span class="w-[50%]">已核销金额：{{ renderMoney(curTotalWriteOff, 2) || '-' }}</span>
            <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
          </div>
        </template>
        <template #writeoffInfo>
          <div class="flex justify-between items-center mb-[12px]">
            <h2 class="text-[16px] font-bold">对应核销明细</h2>
            <div>
              <span class="mr-[10px]">{{ showText }}</span>
              <a-button
                v-if="['NotConsumed', 'PartlyConsumed'].includes(detailData.bizStatus)"
                type="primary"
                @click="handleContinueWriteOff"
              >
                继续核销
              </a-button>
            </div>
          </div>
          <a-table :data-source="detailData.list" :columns="columns" :scroll="{ y: 300, x: 1500 }" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <span class="primary-btn" @click="rowView(record)">应收详情</span>
              </template>
            </template>
          </a-table>
        </template>
        <template #fileInfo>
          <file-list :biz-id="detailData.id"></file-list>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
  <!-- 核销 -->
  <write-off-add-edit ref="writeOffAddEditRef"></write-off-add-edit>
</template>
<script setup>
import WriteOffAddEdit from '@/views/writeOff/records/components/AddEdit.vue'
import AddEdit from './AddEdit.vue'
import { delById, detailById, getUnConsumedPage, queryPayExplainBookEntryByMainId, unAudit } from '../apis'
import { Modal, message } from 'ant-design-vue'
import { renderDict, renderMoney } from '@/utils/render'
const emits = defineEmits(['loadData'])
const visible = ref(false)
// 导航项
const navList = [
  { name: 'baseInfo', title: '基础信息', showTitle: false },
  { name: 'writeoffInfo', title: '核销明细', showTitle: false },
  { name: 'fileInfo', title: '附件' }
]
const columns = [
  // { title: '应收单编号', dataIndex: 'number', width: 200, fixed: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 100, fixed: true },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', ellipsis: true },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  { title: '应收开始日期', dataIndex: 'receiveBeginDate' },
  { title: '应收结束日期', dataIndex: 'receiveEndDate' },
  { title: '已核销', dataIndex: 'consumedAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '待核销', dataIndex: 'notConsumedAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '合同编号', dataIndex: 'contractNum', ellipsis: true },
  { title: '业务部门', dataIndex: 'operatorDepart_dictText', ellipsis: true },
  { title: '业务员', dataIndex: 'operator_dictText' },
  { title: '总表表号', dataIndex: 'waterEleTableNum' },
  { title: '分表表号', dataIndex: 'subWaterEleTableNum' },
  { title: '收款金额', dataIndex: 'receiveAmt', customRender: ({ text }) => renderMoney(text, 2) }
]
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    getDetailById(data.id)
  }
}
defineExpose({ open })
// 通过id获取详情
const detailData = ref({ list: [] })
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  detailData.value = { ...result, list: [] }
  getQueryPayExplainBookEntryByMainId(id)
}
//
const getQueryPayExplainBookEntryByMainId = async (id) => {
  const { result } = await queryPayExplainBookEntryByMainId(id)
  detailData.value.list = result
}

// 累计待核销
const curTotalPendingWriteOff = computed(() => {
  let totalCount = 0
  detailData.value.list.forEach((item) => {
    totalCount += item.notConsumedAmt
  })
  return totalCount
})
// 累计已核销
const curTotalWriteOff = computed(() => {
  let totalCount = 0
  detailData.value.list.forEach((item) => {
    totalCount += item.consumedAmt
  })
  return totalCount
})
const showText = computed(() => {
  if (curTotalPendingWriteOff.value > 0) {
    return `剩余待核销：${curTotalPendingWriteOff.value}`
  }
  if (curTotalPendingWriteOff.value === 0) {
    return '已全部核销'
  }
  return `累计核销超出实收金额，请下调：${curTotalPendingWriteOff.value}`
})

const loading = ref(false)

// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '确认删除当前收付款记录？',
    content: '',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      visible.value = false
    }
  })
}
// 反审批
const handleUnAudit = () => {
  Modal.confirm({
    title: '确认反审核当前收付款记录？',
    content: '',
    centered: true,
    onOk: async () => {
      const { result } = await unAudit(detailData.value)
      message.success(result)
      getDetailById(detailData.value.id)
      emits('loadData')
      // visible.value = false
    }
  })
}
// 查看核销记录
const router = useRouter()
const turnToPage = () => {
  return router.push({ path: '/writeOff/records', query: { id: detailData.value.id } })
}
const receivableDetailRef = ref()
// 查看
const rowView = (row) => {
  receivableDetailRef?.value.open(row)
}

// 继续核销
const writeOffAddEditRef = ref()
const handleContinueWriteOff = async () => {
  if (detailData.value.status === 'AUDITOK' && ['NotConsumed', 'PartlyConsumed'].includes(detailData.value.bizStatus)) {
    const { result } = await getUnConsumedPage({ parent: detailData.value.id })
    writeOffAddEditRef.value.open(result.records)
    return
  }
  message.warning('只有审核通过且待核销的收付款记录才能核销')
}
</script>
