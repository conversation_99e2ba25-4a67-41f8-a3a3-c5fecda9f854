<template>
  <a-drawer
    class="common-drawer"
    v-model:open="visible"
    :title="title"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '100px' } }"
      autocomplete="off"
    >
      <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="实收金额" name="actualReceiveAmt">
            <a-input-number
              class="!w-[100%]"
              v-model:value="ruleForm.actualReceiveAmt"
              placeholder="请输入实收金额"
              suffix="元"
              :min="0"
              :precision="2"
            ></a-input-number>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="手续费" name="serviceCharge">
            <a-input-number
              class="!w-[100%]"
              v-model:value="ruleForm.serviceCharge"
              placeholder="请输入手续费"
              suffix="元"
              :min="0"
              :precision="2"
            ></a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="物业管理公司" name="manageCompany">
            <company-select v-model="ruleForm.manageCompany" placeholder="请选择管理公司" disabled></company-select>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="收款日期" name="receiveDate">
            <a-date-picker
              class="w-[100%]"
              v-model:value="ruleForm.receiveDate"
              picker="date"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              placeholder="请选择收款日期"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="所属客户" name="customer">
            <a-form-item-rest>
              <f7-select
                v-model="ruleForm.customer"
                f7-type="customer"
                placeholder="请选择所属客户"
                @change="customerChange"
              ></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="实际来款人" name="actualPayor">
            <a-input v-model:value="ruleForm.actualPayor" placeholder="请输入实际来款人" allow-clear></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="经办人" name="operator">
            <a-form-item-rest>
              <f7-select
                v-model="ruleForm.operator"
                f7-type="user"
                :depart-id="ruleForm.operatorDepart"
                relation-depart
                placeholder="请选择经办人"
              ></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="业务部门" name="operatorDepart">
            <depart-select v-model="ruleForm.operatorDepart" placeholder="请选择业务部门"></depart-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="ruleForm.remark"
              placeholder="请输入备注"
              :maxlength="255"
              show-count
              :rows="4"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <div class="flex justify-between items-center mb-[12px] mt-[16px]">
        <h2 class="text-[16px] font-bold">对应核销明细</h2>
        <a-button type="primary" ghost @click="addDetail">
          <span class="a-icon-plus mr-[8px]"></span>
          添加明细
        </a-button>
      </div>

      <a-table
        :data-source="ruleForm.payExplainBookEntryList"
        :columns="columns"
        :loading="false"
        :scroll="{ y: 200, x: 1900 }"
        :pagination="false"
      >
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'receiveAmt'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
        </template>
        <template #bodyCell="{ column, index, record }">
          <template v-if="column.dataIndex === 'paymentType_dictText'">
            <div>
              <span class="a-icon-remove cursor-pointer !mr-[8px]" @click="rowDel(index)"></span>
              <span>{{ record.paymentType_dictText }}</span>
            </div>
          </template>
          <!-- 收款金额 -->
          <template v-if="column.dataIndex === 'receiveAmt'">
            <a-form-item
              label=""
              :name="['payExplainBookEntryList', index, 'receiveAmt']"
              :rules="{
                required: true,
                message: '请输入收款金额',
                trigger: 'blur'
              }"
            >
              <a-input-number
                class="!w-[100%]"
                v-model:value="record.receiveAmt"
                :min="0"
                :precision="2"
                placeholder="请输入收款金额"
                @change="rowChange(record)"
              ></a-input-number>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'remark'">
            <a-input class="!w-[100%] !mb-[24px]" v-model:value="record.remark" placeholder="请输入备注"></a-input>
          </template>
        </template>
      </a-table>

      <h2 class="text-[16px] font-bold mb-[12px] mt-[40px]">附件</h2>
      <files-upload v-model="ruleForm.attachmentIds" :biz-id="ruleForm.id"></files-upload>
    </a-form>
    <div class="bg-[#f7f8fa] border border-[#E6E9F0] p-[16px] rounded-[8px] mt-[40px]" v-if="ruleForm.actualReceiveAmt">
      <div
        class="text-[16px] font-bold mb-[8px]"
        :class="ruleForm.actualReceiveAmt - curTotalWriteOff < 0 ? 'text-error' : 'text-[#1D335C] '"
      >
        {{ showText }}
      </div>
      <div class="text-[14px] text-secondary">
        [本次合计收款] {{ ruleForm.actualReceiveAmt }} - [本次累计核销] {{ curTotalWriteOff }} =
        {{ resultCount }}
      </div>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleConfirm">提交</a-button>
      <a-button type="primary" ghost @click="handleStash">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
    <!-- 选择应收单明细弹窗 -->
    <f7-modal
      f7-type="receiveBillDetail"
      :extra-params="extraParams"
      :multiple="true"
      ref="f7ModalRef"
      @updateValue="selectChange"
    ></f7-modal>
  </a-drawer>
</template>
<script setup>
import { message, Modal } from 'ant-design-vue'
import { submit, add, edit, detailById, queryPayExplainBookEntryByMainId } from '../apis'
import { useUserStore } from '@/store/modules/user'
import dayjs from 'dayjs'
import { renderDict } from '@/utils/render'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  if (data.id) {
    getDetail(data.id)
  } else {
    ruleForm.manageCompany = userInfo.value.currentCompany || ''
    ruleForm.operator = userInfo.value.id || ''
    ruleForm.operatorDepart = userInfo.value.currentDepart
    ruleForm.receiveDate = dayjs(Date.now()).format('YYYY-MM-DD')
  }
  visible.value = true
}
defineExpose({ open })
const store = useUserStore()
const userInfo = computed(() => store.userInfo)
const getDetail = async (id) => {
  const { result } = await detailById(id)
  const data = await queryPayExplainBookEntryByMainId(id)
  Object.assign(ruleForm, result, { payExplainBookEntryList: data.result })
}
const title = computed(() => {
  return ruleForm.id ? '编辑收付款' : '新建收付款'
})
const ruleForm = reactive({
  id: '',
  manageCompany: '',
  number: '',
  customer: '',
  billSource: '',
  actualPayor: '',
  receiveDate: '',
  actualReceiveAmt: undefined,
  serviceCharge: undefined,
  // sumAmt: undefined,
  operator: '',
  operatorDepart: '',
  status: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  auditBy: '',
  auditTime: '',
  attachmentIds: '',
  sourceBillId: '',
  sourceBillEntryId: '',
  ctrlUnit: '',
  payExplainBookEntryList: []
})
const rules = computed(() => {
  return {
    actualReceiveAmt: [{ required: true, message: '请输入实收金额', trigger: ['blur'] }],
    serviceCharge: [{ required: true, message: '请输入手续费', trigger: ['blur'] }],
    manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: ['change'] }],
    receiveDate: [{ required: true, message: '请选择发生日期', trigger: ['change'] }],
    customer: [{ required: true, message: '请选择所属客户', trigger: ['change'] }],
    // 1387bug
    actualPayor: [{ required: false, message: '请选择实际来款人', trigger: ['change'] }],
    operator: [{ required: true, message: '请选择经办人', trigger: ['change'] }],
    operatorDepart: [{ required: true, message: '请选择业务部门', trigger: ['change'] }]
  }
})
const extraParams = computed(() => {
  return {
    customer: ruleForm.customer
  }
})

const columns = [
  // { title: '应收单编号', dataIndex: 'number', width: 200, fixed: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 100, fixed: true }, // 提交所需字段同名
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', ellipsis: true }, // 提交所需字段同名
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' }, // 提交所需字段同名
  { title: '应收开始日期', dataIndex: 'receiveBeginDate' }, // 提交所需字段同名
  { title: '应收结束日期', dataIndex: 'receiveEndDate' }, // 提交所需字段同名
  { title: '已核销', dataIndex: 'consumedAmt' }, // 提交所需字段同名
  { title: '待核销', dataIndex: 'notConsumedAmt' }, // 提交所需字段同名
  { title: '合同编号', dataIndex: 'contractNum', ellipsis: true }, // 提交所需字段同名
  { title: '业务部门', dataIndex: 'operatorDepart_dictText', ellipsis: true }, // 提交所需字段同名
  { title: '业务员', dataIndex: 'operator_dictText', ellipsis: true }, // 提交所需字段同名
  { title: '总表表号', dataIndex: 'waterEleTableNum' }, // 提交所需字段同名
  { title: '分表表号', dataIndex: 'subWaterEleTableNum' }, // 提交所需字段同名
  {
    title: '开票类型',
    dataIndex: 'invoiceType',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_Customer_InvoiceType')
  }, // 提交所需字段同名
  { title: '收款金额', dataIndex: 'receiveAmt', width: 200, fixed: 'right' },
  { title: '备注', dataIndex: 'remark', width: 200, fixed: 'right' }
]

// 所属客户选择后的回调
const customerChange = (id, value) => {
  if (id) {
    ruleForm.actualPayor = value.name
    return
  }
  ruleForm.actualPayor = ''
}

// 本次累计核销
const curTotalWriteOff = computed(() => {
  let totalCount = 0
  ruleForm.payExplainBookEntryList.forEach((item) => {
    totalCount += item.receiveAmt
  })
  return totalCount
})
const resultCount = computed(() => {
  return ruleForm.actualReceiveAmt - curTotalWriteOff.value
})
const showText = computed(() => {
  if (resultCount.value > 0) {
    return `剩余待核销：${resultCount.value}`
  }
  if (resultCount.value === 0) {
    return '已全部核销'
  }
  return `累计核销超出实收金额，请下调：${resultCount.value}`
})

// 添加明细
const f7ModalRef = ref()
const addDetail = () => {
  if (!ruleForm.customer) {
    return message.warning('请先选择所属客户！')
  }
  f7ModalRef.value.open(ruleForm.payExplainBookEntryList.map((item) => item.receiveBillEntry))
}
const selectChange = (ids, list) => {
  const numberArr = ruleForm.payExplainBookEntryList.map((item) => item.receiveBillEntry)
  list.forEach((item) => {
    // 没添加过的才要加
    if (!numberArr.includes(item.receiveBillEntry)) {
      ruleForm.payExplainBookEntryList.push({
        id: '',
        // number: item.number, // 展示
        paymentType: item.paymentType,
        paymentType_dictText: item.paymentTypeName, // 展示
        leaseUnit: item.leaseUnit,
        leaseUnit_dictText: item.leaseUnitName, // 展示
        incomeBelongYm: item.incomeBelongYm,
        receiveBeginDate: item.receiveBeginDate,
        receiveEndDate: item.receiveEndDate,
        consumedAmt: item.consumedAmt,
        notConsumedAmt: item.notConsumedAmt,
        contractNum: item.contractNum,
        operatorDepart: item.operatorDepart,
        operatorDepart_dictText: item.operatorDepartName, // 展示
        operator: item.operator,
        operator_dictText: item.operatorName, // 展示
        waterEleTableNum: item.waterEleTableNum,
        subWaterEleTableNum: item.subWaterEleTableNum,

        // 需要用户填写或必传的参数
        receiveAmt: item.transferBalance,
        remark: '',
        receiveBill: item.parent,
        receiveBillEntry: item.id,
        sourceBillId: item.parent,
        sourceBillEntryId: item.id,
        collectionCompany: item.collectionCompany,
        invoiceType: item.invoiceType,
        isDeposit: item.isDeposit
      })
    }
  })
}
// 行删除
const rowDel = (index) => {
  Modal.confirm({
    title: '确认删除当前核销明细吗？',
    content: '',
    centered: true,
    onOk: () => {
      ruleForm.payExplainBookEntryList.splice(index, 1)
    }
  })
}
// 行 款项金额变化的回调
const rowChange = (row) => {
  row.notConsumedAmt = row.receiveAmt
}
// 提交
const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  const { message } = await submit(ruleForm)
  message.success(message)
  emits('loadData')
  handleCancel()
}

// 暂存
const handleStash = async () => {
  await formRef.value.validate()
  const { message: msg, result } = await (ruleForm.id ? edit(ruleForm) : add(ruleForm))
  message.success(msg)
  Object.assign(ruleForm, result)
  emits('loadData')
}
// 取消
const handleCancel = () => {
  Object.assign(ruleForm, {
    id: '',
    manageCompany: '',
    number: '',
    customer: '',
    billSource: '',
    actualPayor: '',
    receiveDate: '',
    actualReceiveAmt: undefined,
    serviceCharge: undefined,
    sumAmt: undefined,
    operator: '',
    operatorDepart: '',
    status: '',
    remark: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    auditBy: '',
    auditTime: '',
    attachmentIds: '',
    sourceBillId: '',
    sourceBillEntryId: '',
    ctrlUnit: '',
    payExplainBookEntryList: []
  })
  formRef.value.clearValidate()
  visible.value = false
}
</script>
