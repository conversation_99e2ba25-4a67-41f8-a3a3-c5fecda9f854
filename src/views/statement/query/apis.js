import request from '@/apis/http'
// import { advanceUpload } from '@/apis/common'

// 分页数据
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/receiveBill/bill',
    params
  })
}
// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/exportBillXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
