<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    title="账单详情"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span v-if="detailData.status === 'AUDITOK'" class="primary-btn" @click="handleUnAudit">反审核</span>
        <a-dropdown v-if="refundApplicationFormList.length">
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down text-[12px]"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="viewRefundApplicationForm">下查退款申请单</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">账单详情</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detailData.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>账单编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>

      <h2 class="text-[16px] font-bold mb-[12px] text-secondary">基础信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">客户：{{ detailData.customer_dictText || '-' }}</span>
        <span class="w-[50%]">合同：{{ detailData.contractNumber || '-' }}</span>
        <span class="w-[50%]">物业管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
        <span class="w-[50%]">业务日期：{{ detailData.bizDate || '-' }}</span>
        <span class="w-[50%]">
          账单类型：{{ renderDict(detailData.billType, 'CT_BASE_ENUM_DetailBill_BillType') || '-' }}
        </span>
        <span class="w-[50%]">业务员：{{ detailData.operator_dictText || '-' }}</span>
        <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
        <span class="w-[50%]">
          业务状态：{{ renderDict(detailData.bizStatus, 'CT_BASE_ENUM_DetailBill_BizStatus') || '-' }}
        </span>
        <span class="w-[50%]">单据状态：{{ renderDict(detailData.status, 'CT_BASE_ENUM_AuditStatus') || '-' }}</span>
        <span class="w-[50%]">关闭原因：{{ detailData.closeReason || '-' }}</span>
        <span class="w-[50%]">说明：{{ detailData.remark || '-' }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px] mt-[40px] text-secondary">账单明细</h2>
      <a-table
        :data-source="detailList"
        :columns="columns"
        :scroll="{ y: 300, x: 3000 }"
        :pagination="false"
        row-key="id"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      ></a-table>
      <a-button class="mt-[12px]" type="primary" @click="handleRefund" :disabled="!selectedRows.length">退款</a-button>
    </a-spin>
    <!-- 关联了退款单才显示 -->
    <template #footer v-if="refundApplicationFormList.length">
      <div class="flex justify-between items-center">
        <div class="text-[20px] font-bold">
          <span>申请退款：</span>
          <span>{{ renderMoney(allRefundReqAmount, 2) }}元</span>
        </div>
        <div class="primary-btn" @click="viewRefundApplicationForm">
          <template v-if="refundApplicationFormList.length > 1">
            <span>查看退款单（{{ refundApplicationFormList.length }}）</span>
            <span class="a-icon-arrow-right"></span>
          </template>
          <!-- 显示退款单状态 -->
          <template v-if="refundApplicationFormList.length === 1">
            <div>
              <span>
                {{ renderDictTag(refundApplicationFormList[0].status, 'CT_BASE_ENUM_RefundReqBill_Status', 'dot') }}
              </span>
              <span class="a-icon-arrow-right"></span>
            </div>
          </template>
        </div>
      </div>
    </template>
  </a-drawer>
  <!-- 退款申请 -->
  <edit-refund ref="editDrawerRef" @refresh="getQueryRefundReqBillByMainId(detailData.id)" />
  <!-- 退款单详情 -->
  <refund-detail ref="detailDrawerRef" @refresh="getQueryRefundReqBillByMainId(detailData.id)" />
</template>
<script setup>
import EditRefund from '@/views/finance/refund/components/EditRefund.vue'
import RefundDetail from '@/views/finance/refund/components/RefundDetail.vue'
import useTableSelection from '@/hooks/useTableSelection'
import { renderDict, renderBoolean, renderDictTag, renderMoney } from '@/utils/render'
import { Modal, message } from 'ant-design-vue'
import { detailById, queryDetailBillEntryByMainId, queryRefundReqBillByMainId, unAudit } from '../apis'
const emits = defineEmits(['loadData'])
const { ids } = defineProps({
  ids: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    const activeIndex = ids.indexOf(data.id)
    curIndex.value = activeIndex === -1 ? 0 : activeIndex
    getDetailById(data.id)
  }
}
defineExpose({ open })

const loading = ref(false)
const detailData = reactive({})
// 账单明细
const detailList = ref([])
const getQueryDetailBillEntryByMainId = async (id) => {
  const { result } = await queryDetailBillEntryByMainId(id)
  detailList.value = result
}
// 退款申请单
const refundApplicationFormList = ref([])
const getQueryRefundReqBillByMainId = async (id) => {
  const { result } = await queryRefundReqBillByMainId(id)
  refundApplicationFormList.value = result
}
const allRefundReqAmount = computed(() => {
  let num = 0
  refundApplicationFormList.value.forEach((item) => {
    num += item.refundReqAmount
  })
  return num
})
const allRefundReqAmountIds = computed(() => {
  return refundApplicationFormList.value.map((item) => item.id).join(',')
})
const { selectedRowKeys, selectedRows, onSelectChange } = useTableSelection(detailList, 'id')
// 通过id获取详情
const getDetailById = async (id) => {
  loading.value = true
  try {
    const { result } = await detailById(id)
    Object.assign(detailData, result)
    getQueryDetailBillEntryByMainId(id)
    getQueryRefundReqBillByMainId(id)
  } finally {
    loading.value = false
  }
}

const columns = [
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', width: 180, fixed: true, ellipsis: true },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '缴交周期序号', dataIndex: 'periodSeq' },
  { title: '是否押金', dataIndex: 'isDeposit', customRender: ({ text }) => renderBoolean(text) },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '到期日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '减免金额', dataIndex: 'remission', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '实际应收金额', dataIndex: 'actualReceiveAmount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已转应收金额', dataIndex: 'transfered', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未转应收金额', dataIndex: 'transferdBalance', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已抵扣金额', dataIndex: 'transferDeduction', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已退款金额', dataIndex: 'refunded', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '尾差已处理金额', dataIndex: 'offDifference', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '可抵退转金额', dataIndex: 'balance', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已提房产税', dataIndex: 'houseTax', customRender: ({ text }) => renderMoney(text, 2) }
]

// 反审核
const handleUnAudit = () => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      await unAudit({ id: detailData.id })
      message.success('反审核成功')
      emits('loadData')
      handleClose()
    }
  })
}
const router = useRouter()
// 查看退款单
const detailDrawerRef = ref()
const viewRefundApplicationForm = () => {
  if (refundApplicationFormList.value.length > 1) {
    return router.push({ path: '/finance/refund', query: { ids: allRefundReqAmountIds.value } })
  }
  // 退款单详情页
  detailDrawerRef.value.open(refundApplicationFormList.value[0])
}
// 退款操作
const editDrawerRef = ref()
const handleRefund = () => {
  editDrawerRef.value.open({}, selectedRows.value)
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>
