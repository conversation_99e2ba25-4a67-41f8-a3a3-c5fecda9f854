<template>
  <a-drawer v-model:open="visible" title="应收单详情" class="common-detail-drawer" placement="right" width="1072px">
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          v-auth="'biz.tripartsettle:ct_biz_receive_bill:edit'"
          v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)"
          class="primary-btn"
          @click="handleEdit"
        >
          编辑
        </span>
        <a-dropdown>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down text-[12px]"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item v-if="['AUDITOK'].includes(detailData.status)">
                <div v-auth="'biz.tripartsettle:ct_biz_receive_bill:unAudit'" class="primary-btn" @click="reverse">
                  反审核
                </div>
              </a-menu-item>
              <a-menu-item v-if="['TEMP', 'BACK'].includes(detailData.status)">
                <div v-auth="'biz.tripartsettle:ct_biz_receive_bill:delete'" class="primary-btn" @click="handleDel">
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">应收单详情</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detailData.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>

      <h2 class="text-[16px] font-bold mb-[12px] text-secondary">基础信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">客户：{{ detailData.customer_dictText || '-' }}</span>
        <span class="w-[50%]">
          单据来源：{{ renderDict(detailData.billSource, 'CT_BASE_ENUM_ReceiveBill_BillSource') || '-' }}
        </span>
        <span class="w-[50%]">物业管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
        <span class="w-[50%]">租金归集公司：{{ detailData.collectionCompany_dictText || '-' }}</span>
        <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
        <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
        <span class="w-[50%]">应收日期：{{ detailData.receiveDate || '-' }}</span>
        <span class="w-[50%]">业务日期：{{ detailData.bizDate || '-' }}</span>
        <span class="w-[50%]">含税：{{ renderBoolean(detailData.containTax) || '-' }}</span>
        <span class="w-[50%]">固定单据：{{ renderBoolean(detailData.fixBill) || '-' }}</span>
        <span class="w-[50%]">金额：{{ renderMoney(detailData.amount, 2) || '-' }}</span>
        <span class="w-[100%]">含税金额：{{ renderMoney(detailData.containTaxAmount, 2) || '-' }}</span>
        <span class="w-[100%]">备注：{{ detailData.remark || '-' }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px] mt-[40px] text-secondary">应收明细</h2>
      <a-table
        :data-source="detailData.list"
        :columns="columns"
        :scroll="{ y: 300, x: 3500 }"
        :pagination="false"
      ></a-table>
    </a-spin>
  </a-drawer>
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
</template>
<script setup>
import { Modal, message } from 'ant-design-vue'
import AddEdit from './AddEdit.vue'
import { renderDict, renderBoolean, renderMoney } from '@/utils/render'
import { detailById, queryReceiveBillEntryByMainId, unAudit, delById } from '../apis'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (id) => {
  visible.value = true
  if (id) {
    getDetailById(id)
  }
}
defineExpose({ open })
const loading = ref(false)
const detailData = ref({})
// 通过id获取详情
const getDetailById = async (id) => {
  loading.value = true
  try {
    const { result } = await detailById(id)
    detailData.value = result
    getQueryDetailBillEntryByMainId(id)
  } finally {
    loading.value = false
  }
}
//
const getQueryDetailBillEntryByMainId = async (id) => {
  const { result } = await queryReceiveBillEntryByMainId(id)
  detailData.value.list = result
}

const columns = [
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 200, ellipsis: true, fixed: true },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', ellipsis: true },
  { title: '合同编号', dataIndex: 'contractNum_dictText' },
  { title: '服务处', dataIndex: 'serviceCenter_dictText' },
  { title: '总表表号', dataIndex: 'waterEleTableNum_dictText' },
  { title: '分表表号', dataIndex: 'subWaterEleTableNum_dictText' },
  { title: '业务员', dataIndex: 'operator_dictText' },
  { title: '经办部门', dataIndex: 'operatorDepart_dictText', ellipsis: true },
  { title: '应收开始日期', dataIndex: 'receiveBeginDate' },
  { title: '应收结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  { title: '开票类型', dataIndex: 'invoiceType_dictText' },
  { title: '数量', dataIndex: 'quantity' },
  { title: '单价', dataIndex: 'price', customRender: ({ text }) => renderMoney(text, 6) },
  { title: '税率', dataIndex: 'taxRate', customRender: ({ text }) => renderMoney(text, 4) },
  { title: '含税单价', dataIndex: 'taxPrice', customRender: ({ text }) => renderMoney(text, 6) },
  { title: '金额', dataIndex: 'amount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '税额', dataIndex: 'taxAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '含税金额', dataIndex: 'containTaxAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已核销金额', dataIndex: 'consumedAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未核销金额', dataIndex: 'noConsumedAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '发票号码', dataIndex: 'invoiceNum' },
  { title: '备注', dataIndex: 'remark' },
  { title: '押金应收', dataIndex: 'pressPayReceive', customRender: ({ text }) => renderBoolean(text) },
  { title: '已转缴款金额', dataIndex: 'transfered', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未转缴款金额', dataIndex: 'transferBalance', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已开票', dataIndex: 'isInvoice', customRender: ({ text }) => renderBoolean(text) }
]

// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
  handleClose()
}
// 反审核
const reverse = () => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      await unAudit({ id: detailData.value.id })
      message.success('反审核成功')
      emits('loadData')
      handleClose()
    }
  })
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '确认删除当前资产处置？',
    content: '',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      handleClose()
    }
  })
}
// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>
