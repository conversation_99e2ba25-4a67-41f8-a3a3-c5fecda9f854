<template>
  <a-drawer
    class="common-drawer"
    v-model:open="visible"
    :title="ruleForm.id ? '编辑应收单' : '新建应收单'"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '100px' } }"
      autocomplete="off"
    >
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="客户" name="customer">
            <a-form-item-rest>
              <f7-select v-model="ruleForm.customer" f7-type="customer" placeholder="请选择客户"></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="应收日期" name="receiveDate">
            <a-date-picker
              v-model:value="ruleForm.receiveDate"
              picker="date"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              placeholder="请选择应收日期"
              class="w-[100%]"
              allow-clear
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="经办人" name="operator">
            <a-form-item-rest>
              <f7-select
                v-model="ruleForm.operator"
                f7-type="user"
                :depart-id="ruleForm.operatorDepart"
                relation-depart
                placeholder="请选择经办人"
              ></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="物业管理公司" name="manageCompany">
            <company-select v-model="ruleForm.manageCompany" placeholder="请选择物业管理公司" disabled></company-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="租金归集公司" name="collectionCompany">
            <company-select v-model="ruleForm.collectionCompany" placeholder="请选择租金归集公司"></company-select>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="业务日期" name="bizDate">
            <a-date-picker
              v-model:value="ruleForm.bizDate"
              picker="date"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              placeholder="请选择业务日期"
              class="w-[100%]"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="业务部门" name="operatorDepart">
            <depart-select
              v-model="ruleForm.operatorDepart"
              placeholder="请选择业务部门"
              @change="ruleForm.operator = ''"
            ></depart-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="含税" name="containTax">
            <a-checkbox v-model:checked="ruleForm.containTax"></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="固定单据" name="fixBill">
            <a-checkbox v-model:checked="ruleForm.fixBill"></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="ruleForm.remark"
              placeholder="请输入备注"
              :maxlength="255"
              show-count
              :rows="4"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <div class="mb-[12px] mt-[12px] flex justify-between">
        <h2 class="text-[16px] font-bold">应收明细</h2>
        <a-button type="primary" ghost @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          添加
        </a-button>
      </div>

      <a-table
        :data-source="ruleForm.receiveBillEntryList"
        :columns="columns"
        :scroll="{ y: 300, x: 4600 }"
        :pagination="false"
      >
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'paymentType'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>

          <template v-if="column.dataIndex === 'operator'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'operatorDepart'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'invoiceType'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'price'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'taxPrice'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
        </template>
        <template #bodyCell="{ column, record, index }">
          <!-- 款项类型 -->
          <template v-if="column.dataIndex === 'paymentType'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'paymentType']"
              :rules="[{ required: true, message: '请选择款项类型', trigger: ['change', 'blur'] }]"
            >
              <div class="flex items-center">
                <span
                  class="a-icon-remove remove-btn cursor-pointer font-18 mr-[5px]"
                  @click="rowDel(record, index)"
                ></span>
                <a-form-item-rest>
                  <f7-select
                    v-model="record.paymentType"
                    f7-type="paymentType"
                    placeholder="请选择款项类型"
                    @change="(id, value) => rowPaymentTypeChange(id, value, record)"
                  ></f7-select>
                </a-form-item-rest>
              </div>
            </a-form-item>
          </template>
          <!-- 租赁单元 -->
          <template v-if="column.dataIndex === 'leaseUnit'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'leaseUnit']"
              :rules="[
                {
                  required: getPaymentTypeCheckObj(record.paymentType).check.leaseUnit,
                  message: '请选择租赁单元',
                  trigger: ['change', 'blur']
                }
              ]"
            >
              <a-form-item-rest>
                <f7-select
                  v-model="record.leaseUnit"
                  f7-type="leaseUnit"
                  placeholder="请选择租赁单元"
                  @change="rowLeaseUnitChange($event, record)"
                ></f7-select>
              </a-form-item-rest>
            </a-form-item>
          </template>
          <!-- 合同编号 -->
          <template v-if="column.dataIndex === 'contractNum'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'contractNum']"
              :rules="[
                {
                  required: getPaymentTypeCheckObj(record.paymentType).check.contractNum,
                  message: '请选择合同',
                  trigger: ['change', 'blur']
                }
              ]"
            >
              <a-form-item-rest>
                <f7-select v-model="record.contractNum" f7-type="contract" placeholder="请选择合同"></f7-select>
              </a-form-item-rest>
            </a-form-item>
          </template>
          <!-- 车位号 -->
          <template v-if="column.dataIndex === 'carportNum'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'carportNum']"
              :rules="[
                {
                  required: getPaymentTypeCheckObj(record.paymentType).check.carportNum,
                  message: '请选择车位号',
                  trigger: ['change', 'blur']
                }
              ]"
            >
              <dict-select
                v-model="record.carportNum"
                placeholder="请选择车位号"
                code="CT_BAS_CarportNum"
              ></dict-select>
            </a-form-item>
          </template>
          <!-- 服务处 -->
          <template v-if="column.dataIndex === 'serviceCenter'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'serviceCenter']"
              :rules="[
                {
                  required: getPaymentTypeCheckObj(record.paymentType).check.serviceCenter,
                  message: '请选择服务处',
                  trigger: ['change', 'blur']
                }
              ]"
            >
              <dict-select
                v-model="record.serviceCenter"
                placeholder="请选择服务处"
                code="CT_BAS_ServiceCenter"
              ></dict-select>
            </a-form-item>
          </template>
          <!-- 总表表号 -->
          <template v-if="column.dataIndex === 'waterEleTableNum'">
            <a-form-item label="" :name="['receiveBillEntryList', index, 'waterEleTableNum']">
              <water-elect-select
                v-model="record.waterEleTableNum"
                :options="record.waterEleTableList"
                placeholder="请选择总表表号"
              ></water-elect-select>
            </a-form-item>
          </template>
          <!-- 分表表号 -->
          <template v-if="column.dataIndex === 'subWaterEleTableNum'">
            <a-form-item label="" :name="['receiveBillEntryList', index, 'subWaterEleTableNum']">
              <water-elect-select
                v-model="record.subWaterEleTableNum"
                :options="record.subWaterEleTableList"
                placeholder="请选择分表表号"
              ></water-elect-select>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'operator'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'operator']"
              :rules="[{ required: true, message: '请选择经办人', trigger: ['change', 'blur'] }]"
            >
              <a-form-item-rest>
                <f7-select
                  v-model="record.operator"
                  f7-type="user"
                  :depart-id="record.operatorDepart"
                  relation-depart
                  placeholder="请选择经办人"
                  @change="(key, value) => rowOperatorChange(key, value, record)"
                ></f7-select>
              </a-form-item-rest>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'operatorDepart'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'operatorDepart']"
              :rules="[{ required: true, message: '请选择业务部门', trigger: ['change'] }]"
            >
              <depart-select
                v-model="record.operatorDepart"
                placeholder="请选择业务部门"
                @change="record.operator = ''"
              ></depart-select>
            </a-form-item>
          </template>
          <!-- 应收开始日期 -->
          <template v-if="column.dataIndex === 'receiveBeginDate'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'receiveBeginDate']"
              :rules="[
                {
                  required: getPaymentTypeCheckObj(record.paymentType).check.serviceCenter,
                  message: '请选择应收开始日期',
                  trigger: ['change']
                }
              ]"
            >
              <a-date-picker
                v-model:value="record.receiveBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择应收开始日期"
                class="w-[100%]"
                allow-clear
                @change="rowReceiveBeginDateChange(record)"
              />
            </a-form-item>
          </template>
          <!-- 应收结束日期 -->
          <template v-if="column.dataIndex === 'receiveEndDate'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'receiveEndDate']"
              :rules="[
                {
                  required: getPaymentTypeCheckObj(record.paymentType).check.serviceCenter,
                  message: '请选择应收结束日期',
                  trigger: ['change']
                }
              ]"
            >
              <a-date-picker
                v-model:value="record.receiveEndDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择应收结束日期"
                class="w-[100%]"
                allow-clear
              />
            </a-form-item>
          </template>
          <!-- 收入归属年月 -->
          <template v-if="column.dataIndex === 'incomeBelongYm'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'incomeBelongYm']"
              :rules="[{ required: true, message: '请选择收入归属年月', trigger: ['change'] }]"
            >
              <a-date-picker
                v-model:value="record.incomeBelongYm"
                picker="month"
                value-format="YYYY-MM"
                format="YYYY-MM"
                placeholder="请选择收入归属年月"
                class="w-[100%]"
                allow-clear
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'invoiceType'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'invoiceType']"
              :rules="[{ required: true, message: '请选择开票类型', trigger: ['change'] }]"
            >
              <dict-select
                v-model="record.invoiceType"
                placeholder="请选择开票类型"
                code="CT_BASE_ENUM_Customer_InvoiceType"
              ></dict-select>
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'quantity'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'quantity']"
              :rules="{
                required: false,
                message: '请输入数量',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.quantity"
                class="!w-[100%]"
                :min="0"
                :precision="0"
                placeholder="请输入数量"
                @change="rowQuantityChange(record)"
              ></a-input-number>
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'price'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'price']"
              :rules="{
                required: true,
                message: '请输入单价',
                trigger: 'change'
              }"
            >
              <a-input-number
                v-model:value="record.price"
                class="!w-[100%]"
                :min="0"
                :precision="2"
                placeholder="请输入单价"
                disabled
              ></a-input-number>
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'taxRate'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'taxRate']"
              :rules="{
                required: false,
                message: '请输入税率',
                trigger: 'change'
              }"
            >
              <a-input-number
                v-model:value="record.taxRate"
                class="!w-[100%]"
                :min="0"
                :precision="2"
                placeholder="请输入税率"
                disabled
              ></a-input-number>
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'taxPrice'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'taxPrice']"
              :rules="{
                required: true,
                message: '请输入含税单价',
                trigger: 'change'
              }"
            >
              <a-input-number
                v-model:value="record.taxPrice"
                class="!w-[100%]"
                :min="0"
                :precision="2"
                placeholder="请输入含税单价"
                @change="rowTaxPriceChange(record)"
              ></a-input-number>
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'amount'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'amount']"
              :rules="{
                required: false,
                message: '请输入金额',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.amount"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入金额"
                disabled
              />
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'taxAmt'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'taxAmt']"
              :rules="{
                required: false,
                message: '请输入税额',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.taxAmt"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入税额"
                disabled
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'containTaxAmt'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'containTaxAmt']"
              :rules="{
                required: false,
                message: '请输入含税金额',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.containTaxAmt"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入含税金额"
                @change="rowContainTaxAmtChange(record)"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'consumedAmt'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'consumedAmt']"
              :rules="{
                required: false,
                message: '请输入已核销金额',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.consumedAmt"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入已核销金额"
                disabled
              />
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'noConsumedAmt'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'noConsumedAmt']"
              :rules="{
                required: false,
                message: '请输入未核销金额',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.noConsumedAmt"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入未核销金额"
                disabled
              />
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'invoiceNum'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'invoiceNum']"
              :rules="{
                required: false,
                message: '请输入发票号码',
                trigger: 'blur'
              }"
            >
              <a-input v-model:value="record.invoiceNum" class="!w-[100%]" placeholder="请输入发票号码"></a-input>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'remark'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'remark']"
              :rules="{
                required: false,
                message: '请输入备注',
                trigger: 'blur'
              }"
            >
              <a-input v-model:value="record.remark" class="!w-[100%]" placeholder="请输入备注"></a-input>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'pressPayReceive'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'pressPayReceive']"
              :rules="{
                required: false,
                message: '请勾选押金应收',
                trigger: 'change'
              }"
            >
              <a-checkbox v-model:checked="record.pressPayReceive" disabled></a-checkbox>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'transfered'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'transfered']"
              :rules="{
                required: false,
                message: '请输入已转缴款金额',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.transfered"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入已转缴款金额"
                disabled
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'transferBalance'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'transferBalance']"
              :rules="{
                required: false,
                message: '请输入未转缴款金额',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.transferBalance"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入未转缴款金额"
                disabled
              />
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'isInvoice'">
            <a-form-item
              label=""
              :name="['receiveBillEntryList', index, 'isInvoice']"
              :rules="{
                required: false,
                message: '请勾选已开票',
                trigger: 'blur'
              }"
            >
              <a-checkbox v-model:checked="record.isInvoice" disabled></a-checkbox>
            </a-form-item>
          </template>
        </template>
      </a-table>
    </a-form>

    <template #footer>
      <a-button type="primary" @click="handleConfirm" :loading="submitLoading">提交</a-button>
      <a-button type="primary" ghost @click="handleStash" :loading="stashLoading">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>
<script setup>
import { Modal, message } from 'ant-design-vue'
import { submit, stash, edit, queryReceiveBillEntryByMainId } from '../apis'
import { queryPaymentTypeReceiveBill } from '@/views/paymentType/apis.js'
import { getAddRate } from '@/views/tax/project/paymentTypeRate/apis.js'
import { useUserStore } from '@/store/modules/user'
import { getDefaultBelongingMonth } from '@/utils'
import dayjs from 'dayjs'
const emits = defineEmits(['loadData'])
const companyOrgCode = ref('')
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data) => {
  companyOrgCode.value = userInfo.value.orgCode
  if (data.id) {
    getTableDetailById(data)
  } else {
    ruleForm.manageCompany = userInfo.value.currentCompany || ''
    ruleForm.operator = userInfo.value.id || ''
    ruleForm.operatorDepart = userInfo.value.currentDepart
    ruleForm.bizDate = dayjs(Date.now()).format('YYYY-MM-DD') // 业务日期默认取当天
  }
  visible.value = true
}
defineExpose({ open })
const store = useUserStore()
const userInfo = computed(() => store.userInfo)

const getTableDetailById = async (data) => {
  const { result } = await queryReceiveBillEntryByMainId(data.id)
  result.forEach((item) => {
    item.paymentTypeList = item.paymentType ? [{ label: item.paymentType_dictText, value: item.paymentType }] : []
    item.leaseUnitList = item.leaseUnit ? [{ label: item.leaseUnit_dictText, value: item.leaseUnit }] : []
    item.waterEleTableList = item.waterEleTableNum
      ? [{ label: item.waterEleTableNum_dictText, value: item.waterEleTableNum }]
      : []
    item.subWaterEleTableList = item.subWaterEleTableNum
      ? [{ label: item.subWaterEleTableNum_dictText, value: item.subWaterEleTableNum }]
      : []
  })
  Object.assign(ruleForm, data, { receiveBillEntryList: result })
}

const formRef = ref()
const ruleForm = reactive({
  id: '',
  number: '',
  bizDate: '',
  manageCompany: '',
  billSource: '',
  status: '',
  bizStatus: '',
  collectionCompany: '',
  operator: '',
  operatorDepart: '',
  customer: '',
  receiveDate: '',
  voucherNumber: '',
  voucherType: '',
  isFromMobile: false,
  isRecConsumed: false,
  containTax: true,
  fixBill: false,
  amount: 0,
  taxAmount: 0,
  containTaxAmount: 0,
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  auditBy: '',
  auditTime: '',
  attachmentIds: '',
  sourceBillId: '',
  sourceBillEntryId: '',
  ctrlUnit: '',
  receiveBillEntryList: []
})

const rules = {
  customer: [{ required: true, message: '请选择客户', trigger: ['change'] }],
  receiveDate: [{ required: true, message: '请选择应收日期', trigger: ['change'] }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: ['change'] }],
  collectionCompany: [{ required: true, message: '请选择租金归集公司', trigger: ['change'] }],
  operator: [{ required: true, message: '请选择经办人', trigger: ['change', 'blur'] }],
  operatorDepart: [{ required: true, message: '请选择物业部门', trigger: ['change'] }]
}
const columns = [
  { title: '款项类型', dataIndex: 'paymentType', width: 200, ellipsis: true, fixed: true },
  { title: '租赁单元', dataIndex: 'leaseUnit' },
  { title: '合同编号', dataIndex: 'contractNum' },
  { title: '车位号', dataIndex: 'carportNum' },
  { title: '服务处', dataIndex: 'serviceCenter' },
  { title: '总表表号', dataIndex: 'waterEleTableNum' },
  { title: '分表表号', dataIndex: 'subWaterEleTableNum' },
  { title: '经办人', dataIndex: 'operator' },
  { title: '业务部门', dataIndex: 'operatorDepart', width: 250 },
  { title: '应收开始日期', dataIndex: 'receiveBeginDate' },
  { title: '应收结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  { title: '开票类型', dataIndex: 'invoiceType' },
  { title: '数量', dataIndex: 'quantity' },
  { title: '单价', dataIndex: 'price' },
  { title: '税率%', dataIndex: 'taxRate' },
  { title: '含税单价', dataIndex: 'taxPrice' },
  { title: '金额', dataIndex: 'amount' },
  { title: '税额', dataIndex: 'taxAmt' },
  { title: '含税金额', dataIndex: 'containTaxAmt' },
  { title: '已核销金额', dataIndex: 'consumedAmt' },
  { title: '未核销金额', dataIndex: 'noConsumedAmt' },
  { title: '发票号码', dataIndex: 'invoiceNum' },
  { title: '备注', dataIndex: 'remark' },
  { title: '押金应收', dataIndex: 'pressPayReceive' },
  { title: '已转缴款金额', dataIndex: 'transfered' },
  { title: '未转缴款金额', dataIndex: 'transferBalance' },
  { title: '已开票', dataIndex: 'isInvoice' }
]

const handleAdd = () => {
  ruleForm.receiveBillEntryList.push({
    id: '',
    paymentType: '',
    contractNum: '',
    serviceCenter: '',
    carportNum: '',
    park: '',
    waterEleTableNum: '',
    subWaterEleTableNum: '',
    leaseUnit: '',
    paymentNature: '',
    measureUnit: '',
    receiveBeginDate: '',
    receiveEndDate: '',
    operator: ruleForm.operator,
    operatorDepart: ruleForm.operatorDepart,
    periodTotalPeriod: '',
    price: 0,
    quantity: 1,
    taxRate: 0,
    taxPrice: 0,
    amount: 0,
    taxAmt: 0,
    containTaxAmt: 0,
    consumedAmt: 0,
    noConsumedAmt: 0,
    pressPayReceive: false,
    incomeBelongYm: '',
    transfered: 0,
    transferBalance: 0,
    invoiceNum: '',
    invoiceType: '',
    isInvoice: false,
    issuedInvoiceAmt: 0,
    notIssueInvoiceAmt: 0,
    remark: '',
    sourceBillId: '',
    sourceBillEntryId: ''
  })
}
//  删除
const rowDel = (row, index) => {
  if (row.id) {
    Modal.confirm({
      title: '确定删除当前应收明细？',
      content: '',
      centered: true,
      onOk: () => {
        ruleForm.receiveBillEntryList.splice(index, 1)
      }
    })
    return
  }
  ruleForm.receiveBillEntryList.splice(index, 1)
}

// 行 应收开始时间 变化
const rowReceiveBeginDateChange = (row) => {
  row.incomeBelongYm = getDefaultBelongingMonth(row.receiveBeginDate, companyOrgCode.value)
}

// 通过公司款项类型租赁单元获取税率
const getAddRateByCompanyPaymentTypeLeaseUnit = async (row) => {
  if (ruleForm.manageCompany && row.paymentType && row.leaseUnit) {
    const { result } = await getAddRate({
      company: ruleForm.manageCompany,
      paymentType: row.paymentType,
      leaseUnit: row.leaseUnit
    })
    row.taxRate = result
  }
}

// 行 款项类型 变化
const detailCheckList = ref([]) // 款项类型关联的单据必填字段校验
const getPaymentTypeCheckObj = (id) => {
  return detailCheckList.value.find((item) => item.id === id) || { check: {} }
}
const rowPaymentTypeChange = async (id, value, row) => {
  row.invoiceType = value.invoiceType
  getAddRateByCompanyPaymentTypeLeaseUnit(row)
  // const data = await Promise.all([queryPaymentTypeReceiveBill({ id })])
  const data = await queryPaymentTypeReceiveBill({ id })
  const ids = detailCheckList.value.map((item) => item.id === id)
  if (!ids.includes(id)) {
    const billObj = data.result.length ? data.result[0] : {}
    // const recordObj = data[1].result.length ? data[1].result[0] : {}
    const obj = {}
    obj.id = id
    obj.check = billObj
    detailCheckList.value.push(obj)
  }
}
// 租赁单元变化
const rowLeaseUnitChange = (event, row) => {
  getAddRateByCompanyPaymentTypeLeaseUnit(row, event)
}

// 行 业务员 变化
const rowOperatorChange = (key, value, row) => {
  // 选择业务员后，经办部门需要自动带出
  row.operatorDepart = value.currentDepart
}

// 行 数量 变化
const rowQuantityChange = (row) => {
  // 含税金额=数量*含税单价
  if (row.quantity && row.taxPrice) {
    row.containTaxAmt = row.quantity * row.taxPrice
  } else {
    row.containTaxAmt = 0
  }
  // 金额 = 数量*单价
  if (row.quantity && row.price) {
    row.amount = row.quantity * row.price
  } else {
    row.amount = 0
  }
}
// 行 含税单价 变化
const rowTaxPriceChange = (row) => {
  // 含税金额=数量*含税单价
  row.containTaxAmt = row.quantity * row.taxPrice
  // 未转缴款金额 = 含税金额
  row.transferBalance = row.containTaxAmt
  // 单价 = 含税单价/(1+税率)
  row.price = row.taxPrice / (1 + row.taxRate / 100)
  // 金额 = 数量*单价
  row.amount = row.quantity * row.price
  // 税额 = 含税金额 - 金额
  row.taxAmt = row.containTaxAmt - row.amount
}

// 行 含税金额 变化
const rowContainTaxAmtChange = (row) => {
  // 含税单价 = 含税金额/数量
  row.taxPrice = row.containTaxAmt / row.quantity
  // 单价 = 含税单价/(1+税率)
  row.price = row.taxPrice / (1 + row.taxRate / 100)
  // 金额 = 数量*单价
  row.amount = row.quantity * row.price
  // 税额 = 含税金额 - 金额
  row.taxAmt = row.containTaxAmt - row.amount
  // 未转缴款金额 = 含税金额
  row.transferBalance = row.containTaxAmt
}

const submitLoading = ref(false)
// 提交
const handleConfirm = async () => {
  await formRef.value.validate()
  submitLoading.value = true
  try {
    await submit(ruleForm)
    message.success('提交成功')
    submitLoading.value = false
    emits('loadData')
    handleCancel()
  } finally {
    submitLoading.value = false
  }
}
// 暂存
const stashLoading = ref(false)
const handleStash = async () => {
  stashLoading.value = true
  try {
    const { result } = await (ruleForm.id ? edit(ruleForm) : stash(ruleForm))
    message.success('暂存成功')
    getTableDetailById(result)
    stashLoading.value = false
    emits('loadData')
  } finally {
    stashLoading.value = false
  }
}
// 取消
const handleCancel = () => {
  ruleForm.id = ''
  ruleForm.number = ''
  ruleForm.bizDate = ''
  ruleForm.manageCompany = ''
  ruleForm.billSource = ''
  ruleForm.status = ''
  ruleForm.bizStatus = ''
  ruleForm.collectionCompany = ''
  ruleForm.operator = ''
  ruleForm.operatorDepart = ''
  ruleForm.customer = ''
  ruleForm.receiveDate = ''
  ruleForm.voucherNumber = ''
  ruleForm.voucherType = ''
  ruleForm.isFromMobile = false
  ruleForm.isRecConsumed = false
  ruleForm.containTax = true
  ruleForm.fixBill = false
  ruleForm.amount = 0
  ruleForm.taxAmount = 0
  ruleForm.containTaxAmount = 0
  ruleForm.remark = ''
  ruleForm.createBy = ''
  ruleForm.createTime = ''
  ruleForm.updateBy = ''
  ruleForm.updateTime = ''
  ruleForm.auditBy = ''
  ruleForm.auditTime = ''
  ruleForm.attachmentIds = ''
  ruleForm.sourceBillId = ''
  ruleForm.sourceBillEntryId = ''
  ruleForm.ctrlUnit = ''
  ruleForm.receiveBillEntryList = []
  formRef.value.clearValidate()
  visible.value = false
}
</script>
