<!-- 应收单 页面 -->
<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button
          v-auth="'biz.tripartsettle:ct_biz_receive_bill:add'"
          class="mb-[10px]"
          type="primary"
          @click="handleAdd"
        >
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button v-auth="'biz.tripartsettle:ct_biz_receive_bill:importExcel'" class="mb-[10px]" @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'biz.tripartsettle:ct_biz_receive_bill:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div
                  v-auth="'biz.tripartsettle:ct_biz_receive_bill:deleteBatch'"
                  class="primary-btn"
                  @click="handleRemove(false)"
                >
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]" label="">
          <a-input
            class="!w-[280px]"
            v-model:value="search.number"
            placeholder="搜索单据编号"
            allow-clear
            @input="handleInput"
          ></a-input>
        </a-form-item>
        <a-form-item class="!mb-[10px]">
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2500 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowView(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <!-- 审核通过才能 反审核 -->
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_receive_bill:unAudit'"
                    class="primary-btn"
                    @click="reverse(record)"
                  >
                    反审核
                  </div>
                </a-menu-item>
                <!-- 审核中才有撤回操作 -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_receive_bill:edit'"
                    class="primary-btn"
                    @click="handleBack(record)"
                  >
                    撤回
                  </div>
                </a-menu-item>
                <!--  暂存 已撤回 审核不通过 才有 -->
                <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_receive_bill:edit'"
                    class="primary-btn"
                    @click="handleAdd(record)"
                  >
                    编辑
                  </div>
                </a-menu-item>
                <!-- 暂存 已撤回 才有删除 -->
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_receive_bill:delete'"
                    class="primary-btn"
                    @click="handleRemove(record)"
                  >
                    删除
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <!-- 详情 -->
    <detail ref="detailRef" @load-data="onTableChange"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入应收明细"
      :download-fn="() => exportExcel('应收明细数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import { renderDict, renderBoolean, renderMoney, renderDictTag } from '@/utils/render'
import AddEdit from './components/AddEdit.vue'
import Detail from './components/Detail.vue'
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import { getPage, exportExcel, importExcel, deleteBatch, unAudit, back } from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  // 从路由查询参数中读取客户信息
  if (route.query.customer) {
    searchFilter.value.customer = route.query.customer
  }
  // 从明细账单列表 查看应收单 跳转过来
  if (route.query.ids) {
    search.value.id = route.query.ids
  }
  const sourceBillId = sessionStorage.getItem('sourceBillIdFromWaterShareBill')
  if (sourceBillId) {
    search.value.sourceBillId = sourceBillId
    searchFilter.value.sourceBillId = sourceBillId
    sessionStorage.removeItem('sourceBillIdFromWaterShareBill')
  }
  onTableChange()
})
const route = useRoute()
const search = ref({
  column: 'number',
  order: 'desc',
  number: '',
  id: ''
})
const searchFilter = ref({
  status: '',
  manageCompany: '',
  collectionCompany: '',
  customer: '',
  containTax: '',
  fixBill: '',
  isRecConsumed: '',
  receiveDate: '',
  amount: '',
  containTaxAmount: '',
  billSource: '',
  operator: '',
  operatorDepart: '',
  sourceBillId: ''
})
const searchList = reactive([
  // { label: '单据编号', name: 'number', type: 'input', placeholder: '请输入单据编号' },
  {
    label: '单据状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择单据状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  { label: '物业管理公司', name: 'manageCompany', type: 'companySelect', placeholder: '请选择物业管理公司' },
  { label: '租金归集公司', name: 'collectionCompany', type: 'companySelect', placeholder: '请选择租金归集公司' },
  {
    label: '客户',
    name: 'customer',
    type: 'customerSelect',
    placeholder: '请选择客户'
  },
  {
    label: '含税',
    name: 'containTax',
    type: 'radio',
    placeholder: '请选择含税',
    list: [
      { label: '否', value: false },
      { label: '是', value: true }
    ]
  },
  {
    label: '固定单据',
    name: 'fixBill',
    type: 'radio',
    placeholder: '请选择固定单据',
    list: [
      { label: '否', value: false },
      { label: '是', value: true }
    ]
  },
  {
    label: '应收冲销',
    name: 'isRecConsumed',
    type: 'radio',
    placeholder: '请选择应收冲销',
    list: [
      { label: '否', value: false },
      { label: '是', value: true }
    ]
  },
  { label: '应收日期', name: 'receiveDate', type: 'date', placeholder: '请选择应收日期' },
  { label: '金额', name: 'amount', type: 'number', placeholder: '请输入金额' },
  { label: '含税金额', name: 'containTaxAmount', type: 'number', placeholder: '请输入含税金额' },
  {
    label: '单据来源',
    name: 'billSource',
    type: 'dic',
    placeholder: '请选择单据状态',
    code: 'CT_BASE_ENUM_ReceiveBill_BillSource'
  },
  {
    label: '经办人',
    name: 'operator',
    type: 'userSelect',
    placeholder: '请选择经办人'
  },
  {
    label: '原始单据',
    name: 'sourceBillId',
    type: 'input'
  },
  { label: '业务部门', name: 'operatorDepart', type: 'departSelect', placeholder: '请选择经办部门' }
])
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value })
}
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: true },
  {
    title: '单据状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
  { title: '客户', dataIndex: 'customer_dictText', ellipsis: true },
  { title: '含税', dataIndex: 'containTax', customRender: ({ text }) => renderBoolean(text) },
  { title: '固定单据', dataIndex: 'fixBill', customRender: ({ text }) => renderBoolean(text) },
  { title: '应收冲销', dataIndex: 'isRecConsumed', customRender: ({ text }) => renderBoolean(text) },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '金额', dataIndex: 'amount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '含税金额', dataIndex: 'containTaxAmount', customRender: ({ text }) => renderMoney(text, 2) },
  {
    title: '单据来源',
    dataIndex: 'billSource',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_ReceiveBill_BillSource')
  },
  { title: '经办人', dataIndex: 'operator_dictText' },
  { title: '经办部门', dataIndex: 'operatorDepart_dictText', ellipsis: true },
  { title: '备注', dataIndex: 'remark' },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

// 新增
const addEditRef = ref()
const handleAdd = (row = {}) => {
  addEditRef?.value.open({ ...row })
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row.id)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('应收单数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
// 撤回
const handleBack = (row) => {
  // if (!hasPermission('biz.funds:ct_fun_refund_req_bill:back')) return
  Modal.confirm({
    title: '确认撤回该资产？',
    content: '',
    async onOk() {
      const data = await back({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
// 批量删除
const handleRemove = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前应收单？' : '确认批量删除选中应收单？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

// 反审核
const reverse = (row) => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await unAudit({ id: row.id })
      message.success(data.message)
      onTableChange({ pageNo: pagination.value.current, pageSize: pagination.value.pageSize })
    }
  })
}
</script>
