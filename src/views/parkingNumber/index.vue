<template>
  <div>
    <div class="flex items-center mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus"></i>
        新建
      </a-button>
      <a-button @click="handleImport">
        <i class="a-icon-import-right"></i>
        导入
      </a-button>
      <a-button :loading="exportLoading" @click="handleExport">
        <i class="a-icon-export-right"></i>
        导出
      </a-button>
      <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <s-input v-model="params.name" placeholder="搜索名称" class="ml-[40px] !w-[280px]" @input="handleInput"></s-input>
      <filter-more
        :params="params"
        :search-list="searchList"
        @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></filter-more>
      <div class="flex items-center ml-[10px]">
        <a-checkbox v-model:checked="viewEnabled">仅看启用</a-checkbox>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleRemove(record)">删除</span>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('车位号导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, updateStatus } from './apis.js'
import Edit from './components/Edit.vue'
import { Modal, message } from 'ant-design-vue'

const viewEnabled = ref(false) // 是否仅看启用
watch(viewEnabled, (val) => {
  params.status = val ? 'ENABLE' : undefined
  onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
})

const params = reactive({
  column: 'createTime',
  order: 'desc',
  status: undefined,
  number: undefined,
  name: undefined,
  park: undefined,
  manageCompany: undefined,
  createTime: undefined
})

const searchList = [
  { label: '单据编号', name: 'number', type: 's-input' },
  { label: '停车场', name: 'park', type: 'dict-select', code: 'CT_BAS_Park' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_BaseStatus' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
  { label: '创建时间', name: 'createTime', type: 'date' },
  { label: '备注', name: 'remark', type: 's-input' }
]

const columns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '名称', dataIndex: 'name' },
  { title: '停车场', dataIndex: 'park_dictText' },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText' },
  { title: '单据状态', dataIndex: 'status', width: 100 },
  { title: '备注', dataIndex: 'remark', ellipsis: true },
  { title: '创建人', dataIndex: 'createBy_dictText' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open({
    id: data.id,
    name: data.name,
    number: data.number,
    manageCompany: data.manageCompany,
    park: data.park,
    status: data.status,
    remark: data.remark,
    easId: data.easId,
    isRefer: data.isRefer
  })
}

const handleStatusChange = async (data, val) => {
  if (data.loading) return
  try {
    data.loading = true
    await updateStatus({ ids: data.id, status: val ? 'ENABLE' : 'DISABLE' })
    data.loading = false
    message.success('保存成功')
    data.status = val ? 'ENABLE' : 'DISABLE'
  } catch {
    data.loading = false
    data.checked = !val
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除车位号？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('车位号数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
