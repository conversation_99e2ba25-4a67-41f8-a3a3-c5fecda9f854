import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/flowable/apithird/flowBizBill/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/flowable/apithird/flowBizBill/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/flowable/apithird/flowBizBill/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/flowable/apithird/flowBizBill/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/flowable/apithird/flowBizBill/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/flowable/apithird/flowBizBill/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/flowable/apithird/flowBizBill/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/flowable/apithird/flowBizBill/deleteBatch',
    params
  })
}

export const bizBillTreeSelectList = (params) => {
  return request({
    method: 'get',
    url: '/flowable/apithird/flowBizBill/bizBillTreeSelectList',
    params
  })
}
