<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑流程单据类型信息' : '新建流程单据类型信息'"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '150px' } }" autocomplete="off">
        <a-form-item label="名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item label="类名key" name="classNameKey">
          <a-input v-model:value="form.classNameKey" placeholder="请输入类名key" />
        </a-form-item>
        <a-form-item label="单据状态" name="status">
          <dict-select v-model="form.status" code="CT_BASE_ENUM_BaseStatus"></dict-select>
        </a-form-item>
        <a-form-item label="完整类名" name="className">
          <a-input v-model:value="form.className" placeholder="请输入完整类名" />
        </a-form-item>
        <a-form-item label="审核详情页面显示路径" name="vueUi">
          <a-input v-model:value="form.vueUi" placeholder="请输入审核详情页面显示路径" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { add, edit, detail } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.name = result.name
  form.classNameKey = result.classNameKey
  form.status = result.status
  form.className = result.className
  form.vueUi = result.vueUi
  loading.value = false
}

const form = reactive({
  id: '',
  name: '',
  classNameKey: '',
  status: '',
  vueUi: '',
  className: ''
})

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  classNameKey: [{ required: true, message: '请输入类名key', trigger: 'blur' }],
  status: [{ required: true, message: '请选择单据状态', trigger: 'blur' }],
  className: [{ required: true, message: '请输入完整类名', trigger: 'blur' }],
  vueUi: [{ required: true, message: '请输入审核详情页面显示路径', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    form.id ? await edit(params) : await add(params)
    confirmLoading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.name = ''
  form.classNameKey = ''
  form.status = ''
  form.className = ''
  form.vueUi = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
