<template>
  <a-drawer
    class="common-drawer"
    v-model:open="visible"
    title="收付款核销"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleCancel"
    :mask-closable="false"
  >
    <div class="flex">
      <div class="w-[71%] pr-[40px] !h-[calc(100vh-185px)] overflow-auto no-scrollbar">
        <a-form
          :model="ruleForm"
          ref="formRef"
          :rules="rules"
          :label-col="{ style: { width: '120px' } }"
          autocomplete="off"
        >
          <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="核销方案" name="consumeScheme">
                <api-select
                  v-model="ruleForm.consumeScheme"
                  :async-fn="() => getPage({ pageNo: 1, pageSize: 10000 })"
                  :field-names="{ label: 'schemeName', value: 'id' }"
                  placeholder="请选择核销方案"
                  @change="callBackFunc"
                ></api-select>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="正负记录优先对冲" name="hedging">
                <a-switch v-model:checked="ruleForm.hedging" @change="callBackFunc" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="核销日期" name="consumeDate">
                <a-date-picker
                  class="w-[100%]"
                  v-model:value="ruleForm.consumeDate"
                  picker="date"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择核销日期"
                  @change="callBackFunc"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <div class="flex justify-between items-center mb-[12px] mt-[16px]">
            <h2 class="text-[16px] font-bold">选择收付款记录</h2>
            <a-button type="primary" ghost @click="addRecord">
              <span class="a-icon-plus mr-[8px]"></span>
              添加
            </a-button>
          </div>
          <div class="mb-[20px]" v-for="(item, index) in ruleForm.payDataEntryList" :key="index">
            <div class="flex items-center mb-[24px]">
              <span
                class="a-icon-remove text-[20px] text-[#8992A3] mr-[10px] cursor-pointer"
                @click="recDataEntryListRemove(index)"
              ></span>
              <span>{{ item.number }}</span>
              <span class="a-icon-arrow-right text-[#8992A3]"></span>
            </div>
            <div
              class="flex flex-wrap gap-y-[12px] text-secondary p-[10px] rounded-[8px] border border-[#e6e9f0] bg-[#E6E9F0]"
            >
              <span class="w-[50%]">收付款时间：{{ item.receiveDate || '-' }}</span>
              <span class="w-[50%]">客户：{{ item.customerName || '-' }}</span>
              <span class="w-[50%]">实际来款人：{{ item.actualPayor || '-' }}</span>
              <span class="w-[50%]">收付款公司：{{ item.collectionCompanyName || '-' }}</span>
              <span class="w-[50%]">合计收付金额：{{ renderMoney(item.receiveAmt, 2) || '-' }}</span>
              <span class="w-[50%]">
                <span>已核销金额：{{ renderMoney(item.consumedAmt, 2) || '-' }}</span>
                <span>（剩余{{ renderMoney(item.notConsumedAmt, 2) }}）</span>
              </span>
            </div>
            <div></div>
          </div>

          <div class="flex justify-between items-center mb-[12px] mt-[40px]">
            <h2 class="text-[16px] font-bold">拟核销应收账单</h2>
            <a-button type="primary" ghost @click="addPay">
              <span class="a-icon-plus mr-[8px]"></span>
              添加
            </a-button>
          </div>

          <div class="mb-[20px]" v-for="(item, index) in ruleForm.recDataEntryList" :key="index">
            <div class="flex justify-between items-center">
              <div class="flex items-center mb-[24px]">
                <span
                  class="a-icon-remove text-[20px] text-[#8992A3] mr-[10px] cursor-pointer"
                  @click="payDataEntryListRemove(index)"
                ></span>
                <span>{{ item.number }}</span>
                <span class="a-icon-arrow-right text-[#8992A3]"></span>
              </div>
              <a-form-item
                class="w-[47%] !pl-[40px]"
                label="本次核销(元)"
                :name="['recDataEntryList', index, 'thisConsumedAmt']"
                :rules="{
                  required: true,
                  message: '请输入本次核销',
                  trigger: 'blur'
                }"
              >
                <a-input-number
                  class="!w-[100%]"
                  v-model:value="item.thisConsumedAmt"
                  placeholder="请输入本次核销"
                  :min="0"
                  :max="item.notConsumedAmt"
                  @change="callBackFunc"
                ></a-input-number>
              </a-form-item>
            </div>
            <div
              class="flex flex-wrap gap-y-[12px] text-secondary p-[10px] rounded-[8px] border-[#e6e9f0] bg-[#E6E9F0]"
            >
              <span class="w-[50%]">客户：{{ item.customerName || '-' }}</span>
              <span class="w-[50%]">应收日期：{{ item.receiveDate || '-' }}</span>
              <span class="w-[50%]">收入归属年月：{{ item.incomeBelongYm || '-' }}</span>
              <span class="w-[50%]">实际应收金额：{{ renderMoney(item.receiveAmt, 2) }}</span>
              <span class="w-[50%]">
                <span>已核销金额：{{ renderMoney(item.consumedAmt, 2) }}</span>
                <span>（剩余{{ renderMoney(item.notConsumedAmt, 2) }}）</span>
              </span>
            </div>
            <div></div>
          </div>
        </a-form>
      </div>
      <div class="w-[29%]">
        <div class="inline-block w-[100%] bg-[#E6E9F0] p-[16px] rounded-[8px]">
          <h1 class="text-[18px] text-[#1D335C] font-bold mb-[16px]">核销结果预览</h1>
          <div class="rounded-[8px] bg-[#fff] mb-[16px] p-[12px]">
            <h2 class="text-[16px] text-[#1D335C] font-bold mb-[12px]">收付款</h2>
            <div class="text-secondary">
              <div class="mb-[12px]">收付款总金额：{{ renderMoney(previewInfo.receiveAmt, 2) || '-' }}</div>
              <div class="mb-[12px]">已核销金额：{{ renderMoney(previewInfo.consumedAmt, 2) || '-' }}</div>
              <div class="mb-[12px]">剩余可核销金额：{{ renderMoney(previewInfo.notConsumedAmt, 2) || '-' }}</div>
              <div class="mb-[12px]">本次拟核销金额：{{ renderMoney(previewInfo.thisConsumedAmt, 2) || '-' }}</div>
              <div>核销后剩余金额：{{ renderMoney(previewInfo.balance, 2) || '-' }}</div>
            </div>
          </div>
          <div class="rounded-[8px] bg-[#fff] p-[12px]">
            <h2 class="text-[16px] text-[#1D335C] font-bold mb-[12px]">应收账单</h2>
            <div class="text-secondary">
              <div class="flex justify-between" v-for="item in previewInfo.detailList" :key="item.id">
                <span>
                  {{ item.leaseUnitName }} - {{ item.paymentTypeName }} - {{ renderMoney(item.consumedAmt, 2) }}
                </span>
                <span :class="item.notConsumedAmt ? 'text-[#FE8D52]' : 'text-[#27B723]'">
                  {{ renderMoney(item.notConsumedAmt, 2) ? `余${renderMoney(item.notConsumedAmt, 2)}` : '结清' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button type="primary" @click="handleConfirm">核销</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <!-- 选择收付款记录 -->
  <f7-modal
    :key="f7Type"
    :f7-type="f7Type"
    :multiple="true"
    ref="f7ModalRef"
    @updateValue="recordSelectChange"
  ></f7-modal>
</template>
<script setup>
import F7Modal from '@/components/F7Select/F7Modal.vue'
import { getPage } from '@/views/writeOff/solution/apis.js'
import { message } from 'ant-design-vue'
import { consumed, preview } from '../apis.js'
import { renderMoney } from '@/utils/render'
const emits = defineEmits('loadData')
const visible = ref(false)
const f7Type = ref('receiptPaymentDetail')
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (list = []) => {
  // list :需要核销的收付款记录明细
  if (list.length) {
    list.forEach((item) => {
      ruleForm.payDataEntryList.push({
        id: item.id,
        number: item.number,
        receiveDate: item.receiveDate,
        customer: item.customer,
        customerName: item.customerName,
        actualPayor: item.actualPayor, // 只展示  提交接口没有
        collectionCompanyName: item.collectionCompanyName,
        collectionCompany: item.manageCompany,
        receiveAmt: item.receiveAmt,
        consumedAmt: item.consumedAmt,
        notConsumedAmt: item.notConsumedAmt,

        thisConsumedAmt: item.thisConsumedAmt,
        detailBill: item.parent,
        detailBillEntry: item.id,
        receiveBill: item.parent,
        receiveBillEntry: item.id
      })
    })
  }
  visible.value = true
}
defineExpose({ open })

const previewInfo = ref({})
const ruleForm = reactive({
  consumeScheme: '',
  hedging: false,
  consumeDate: '',
  payDataEntryList: [],
  recDataEntryList: []
})
const rules = computed(() => {
  return {
    consumeScheme: [{ required: true, message: '请选择核销方案', trigger: ['change'] }],
    consumeDate: [{ required: true, message: '请选择核销日期', trigger: ['change'] }]
  }
})

// 表单项变化的回调
const callBackFunc = async () => {
  if (
    ruleForm.consumeScheme &&
    ruleForm.consumeDate &&
    ruleForm.payDataEntryList.length &&
    ruleForm.recDataEntryList.length
  ) {
    let allHasThisConsumedAmt = true
    ruleForm.recDataEntryList.forEach((item) => {
      if (!item.thisConsumedAmt) {
        allHasThisConsumedAmt = false
      }
    })
    if (allHasThisConsumedAmt) {
      // 获取预览数据
      const { result } = await preview(ruleForm)
      previewInfo.value = result
    }
  }
}

const f7ModalRef = ref()
// 添加收付款记录
const addRecord = async () => {
  f7Type.value = 'receiptPaymentDetail'
  await nextTick()
  f7ModalRef?.value.open()
}
// 选择收付款记录回调
const recordSelectChange = (ids, list) => {
  list.forEach((item) => {
    if (f7Type.value === 'receiptPaymentDetail') {
      const ids = ruleForm.payDataEntryList.map((item) => item.id)
      if (!ids.includes(item.id)) {
        ruleForm.payDataEntryList.push({
          id: item.id,
          number: item.number,
          receiveDate: item.receiveDate,
          customer: item.customer,
          customerName: item.customerName,
          actualPayor: item.actualPayor, // 只展示  提交接口没有
          collectionCompanyName: item.collectionCompanyName,
          collectionCompany: item.manageCompany,
          receiveAmt: item.receiveAmt,
          consumedAmt: item.consumedAmt,
          notConsumedAmt: item.notConsumedAmt,

          thisConsumedAmt: item.thisConsumedAmt,
          detailBill: item.parent,
          detailBillEntry: item.id,
          receiveBill: item.parent,
          receiveBillEntry: item.id
        })
      }
      return
    }
    list.forEach((item) => {
      const ids = ruleForm.recDataEntryList.map((item) => item.id)
      if (!ids.includes(item.id)) {
        ruleForm.recDataEntryList.push({
          id: item.id,
          number: item.number,
          customer: item.customer,
          customerName: item.customerName,
          receiveDate: item.receiveDate,
          incomeBelongYm: item.incomeBelongYm,
          paymentTypeName: item.paymentTypeName,
          paymentType: item.paymentType,
          leaseUnit: item.leaseUnit,
          leaseUnitName: item.leaseUnitName,
          contractNum: item.contractNum,
          receiveAmt: item.receiveAmt,
          consumedAmt: item.consumedAmt,
          notConsumedAmt: item.notConsumedAmt,
          // 需要用户填写的
          thisConsumedAmt: undefined,
          detailBill: item.parent,
          detailBillEntry: item.id,
          receiveBill: item.parent,
          receiveBillEntry: item.id
        })
      }
    })
  })
  callBackFunc()
}
// 添加应收单
const addPay = async () => {
  f7Type.value = 'receiveBillDetail'
  await nextTick()
  f7ModalRef?.value.open()
}
// 收付款明细删除
const recDataEntryListRemove = (index) => {
  ruleForm.payDataEntryList.splice(index, 1)
}
// 应收单明细删除
const payDataEntryListRemove = (index) => {
  ruleForm.recDataEntryList.splice(index, 1)
}

// 核销
const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  const data = await consumed(ruleForm)
  message.success(data.message)
  emits('loadData')
  handleCancel()
}

// 取消
const handleCancel = () => {
  Object.assign(ruleForm, {
    consumeScheme: '',
    hedging: false,
    consumeDate: '',
    payDataEntryList: [],
    recDataEntryList: []
  })
  previewInfo.value = {}
  formRef.value.clearValidate()
  visible.value = false
}
</script>
