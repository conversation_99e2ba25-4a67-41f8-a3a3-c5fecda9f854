<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑系统编码规则' : '新增系统编码规则'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '90px' } }"
      autocomplete="off"
    >
      <a-form-item label="规则名称" name="ruleName">
        <a-input v-model:value="form.ruleName" placeholder="请输入规则名称" :maxlength="60" />
      </a-form-item>
      <a-form-item label="规则编码" name="ruleCode">
        <a-input
          v-model:value="form.ruleCode"
          placeholder="请输入规则编码"
          :disabled="Boolean(form.id)"
          :maxlength="255"
        ></a-input>
      </a-form-item>
      <a-form-item label="规则实现类" name="ruleClass">
        <a-input v-model:value="form.ruleClass" placeholder="请输入规则实现类" :maxlength="255"></a-input>
      </a-form-item>
      <a-form-item label="规则参数">
        <a-form-item-rest>
          <div>
            <div class="flex items-center mb-[10px]" v-for="(item, index) in keyValues" :key="item.id">
              <span class="shrink-0 mr-[8px]">key:</span>
              <a-input size="medium" v-model:value="item.key" placeholder="请输入参数key" :maxlength="100"></a-input>
              <span class="shrink-0 mx-[8px]">value:</span>
              <a-input
                size="medium"
                v-model:value="item.value"
                placeholder="请输入参数value"
                :maxlength="100"
              ></a-input>
              <i class="a-icon-remove text-error cursor-pointer ml-[12px]" @click="handleRemoveJson(index)"></i>
            </div>
          </div>
          <a-button size="medium" type="primary" ghost @click="handleAddJson">
            <i class="a-icon-plus"></i>
            新增
          </a-button>
        </a-form-item-rest>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { add, edit, checkRuleCode } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    if (data.ruleParams) {
      const obj = JSON.parse(data.ruleParams)
      for (const key in obj) {
        keyValues.value.push({ id: Math.random().toString(), key, value: obj[key] })
      }
    }
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  id: '',
  ruleClass: '',
  ruleCode: '',
  ruleName: '',
  ruleParams: ''
})

const validateRuleCode = async (_, value) => {
  if (!value) return Promise.reject('请输入规则编码')
  try {
    await checkRuleCode({ tableName: 'sys_fill_rule', fieldName: 'rule_code', fieldVal: value, dataId: form.id })
    return Promise.resolve()
  } catch (err) {
    return Promise.reject((err && err.message) || '该值不可用')
  }
}
const rules = {
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  ruleCode: [{ required: true, validator: validateRuleCode, trigger: 'blur' }],
  ruleClass: [{ required: true, message: '请输入规则实现类', trigger: 'blur' }]
}

const keyValues = ref([])
const handleAddJson = () => {
  keyValues.value.push({
    id: Math.random().toString(),
    key: '',
    value: ''
  })
}
const handleRemoveJson = (index) => {
  keyValues.value.splice(index, 1)
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    if (keyValues.value.length) {
      const obj = {}
      keyValues.value.forEach((item) => {
        if (item.key && item.value) {
          obj[item.key] = item.value
        }
      })
      params.ruleParams = JSON.stringify(obj)
    } else {
      params.ruleParams = ''
    }
    form.id ? await edit(params) : await add(params)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh', !form.id)
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.ruleClass = ''
  form.ruleCode = ''
  form.ruleName = ''
  form.ruleParams = ''
  keyValues.value = []
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
