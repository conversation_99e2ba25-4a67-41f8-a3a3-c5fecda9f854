import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/sys/fillRule/list',
    params
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/sys/fillRule/deleteBatch',
    params
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/sys/fillRule/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/sys/fillRule/edit',
    data
  })
}

export const test = (params) => {
  return request({
    method: 'get',
    url: '/sys/fillRule/testFillRule',
    params
  })
}

export const checkRuleCode = (params) => {
  return request({
    method: 'get',
    url: '/sys/duplicate/check',
    params,
    showErrorMsg: false
  })
}
