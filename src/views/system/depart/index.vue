<template>
  <div class="flex !p-0 !bg-transparent gap-[16px]">
    <section class="flex-1 bg-white p-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd" v-auth="'system:depart:add'">
          <i class="a-icon-plus"></i>
          新增
        </a-button>
        <a-button type="primary" @click="handleAddChild" v-auth="'system:depart:add'">
          <i class="a-icon-lower-level"></i>
          添加下级
        </a-button>
        <a-popconfirm
          title="是否确认删除选中部门？"
          @confirm="handleDelete"
          v-if="checkedKeys.checked && checkedKeys.checked.length"
        >
          <a-button>批量删除</a-button>
        </a-popconfirm>
      </div>
      <div class="my-[20px]">
        <a-input-search v-model:value="keyWord" placeholder="按部门名称搜索" enter-button @search="onSearch" />
      </div>
      <a-tree
        v-model:expanded-keys="expandedKeys"
        v-model:checked-keys="checkedKeys"
        v-model:selected-keys="selectedKeys"
        checkable
        check-strictly
        :key="treeKey"
        :tree-data="deptTree"
        :load-data="onLoadData"
        :height="treeHeight"
        @select="onSelect"
      />
    </section>
    <section class="flex-1 bg-white p-[16px]">
      <a-tabs v-model:active-key="activeKey">
        <a-tab-pane key="baseInfo" tab="基本信息">
          <div v-show="deptTree.length" class="pt-[20px]">
            <base-info-form :dept-tree="deptTree" ref="baseInfoFormRef"></base-info-form>
            <div class="flex justify-center">
              <a-button @click="setBaseFormData" v-auth="'system:depart:edit'">重置</a-button>
              <a-button type="primary" :loading="saveLoading" @click="handleSaveDept" v-auth="'system:depart:edit'">
                保存
              </a-button>
            </div>
          </div>
          <a-empty v-show="!deptTree.length" description="暂未添加部门"></a-empty>
        </a-tab-pane>
        <a-tab-pane key="permission" tab="部门权限" force-render>
          <permission-config :depart-id="activeDeptKey"></permission-config>
        </a-tab-pane>
      </a-tabs>
    </section>
    <edit-dept ref="editDeptRef" :dept-tree="deptTree" @refresh="loadDeptTree"></edit-dept>
  </div>
</template>

<script setup>
import { getDepartTreeLazy, searchDepart, deleteDept } from './apis'
import EditDept from './components/EditDept.vue'
import BaseInfoForm from './components/BaseInfoForm.vue'
import PermissionConfig from './components/PermissionConfig.vue'
import { message } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'

const keyWord = ref('')
const onSearch = async () => {
  expandedKeys.value = []
  selectedKeys.value = []
  checkedKeys.value.checked = []
  activeDeptKey.value = ''
  if (!keyWord.value.length) {
    treeKey.value = Math.random().toString()
    await nextTick()
    await loadDeptTree()
    return
  }
  const { result } = await searchDepart({ keyWord: keyWord.value })
  deptTree.value = result || []
  initFormData()
}

const expandedKeys = ref([])
const selectedKeys = ref([])
const checkedKeys = ref({ checked: [] })
const activeDeptKey = ref('')
const deptTree = ref([])
const treeKey = ref('treeKey') // a-tree的key值，为了解决清空关键字后，搜索数据，点击箭头展开没反应的问题，改变key值，让组件重新渲染

const loadDeptTree = async () => {
  const { result } = await getDepartTreeLazy()
  deptTree.value = result || []
  initFormData()
}

// 初始化右侧表单数据
const initFormData = () => {
  if (deptTree.value.length && !selectedKeys.value.length) {
    selectedKeys.value = [deptTree.value[0].id]
    activeDeptKey.value = deptTree.value[0].id
  }
  setBaseFormData()
}

// 懒加载部门树
const onLoadData = async (treeNode) => {
  if (treeNode.dataRef.children && treeNode.dataRef.children.length) return
  const { result } = await getDepartTreeLazy({ pid: treeNode.id })
  treeNode.dataRef.children = result
  deptTree.value = [...deptTree.value]
}

const onSelect = (data) => {
  if (data && data.length) {
    selectedKeys.value = data
    activeDeptKey.value = data[0]
  } else {
    selectedKeys.value = [activeDeptKey.value]
  }
  setBaseFormData()
}

const handleDelete = async () => {
  if (!hasPermission('system:depart:delete')) return
  await deleteDept({ ids: checkedKeys.value.checked.join(',') })
  message.success('删除成功')
  if (checkedKeys.value.checked.includes(activeDeptKey.value)) {
    selectedKeys.value = []
    activeDeptKey.value = ''
  }
  checkedKeys.value.checked.splice(0)
  loadDeptTree()
}

// 根据部门id，遍历部门树，获取数据
const getDeptDataById = (list, id) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].id === id) return list[i]
    if (list[i].children) {
      const data = getDeptDataById(list[i].children, id)
      if (data) return data
    }
  }
}

const editDeptRef = ref()
const handleAdd = () => {
  editDeptRef.value.open()
}
const handleAddChild = () => {
  editDeptRef.value.open(getDeptDataById(deptTree.value, activeDeptKey.value))
}

const baseInfoFormRef = ref()

// 设置右侧部门基本信息表单数据
const setBaseFormData = () => {
  if (!deptTree.value.length) {
    baseInfoFormRef.value.setFormData({
      departName: '',
      parentId: '',
      orgCode: '',
      orgCategory: '',
      orgType: '',
      departOrder: '',
      mobile: '',
      fax: '',
      address: '',
      memo: '',
      id: ''
    })
    return
  }
  const data = getDeptDataById(deptTree.value, activeDeptKey.value)
  baseInfoFormRef.value.setFormData({
    departName: data.title,
    parentId: data.parentId,
    orgCode: data.orgCode,
    orgCategory: data.orgCategory,
    orgType: data.orgType,
    departOrder: String(data.departOrder),
    mobile: data.mobile,
    fax: data.fax,
    address: data.address,
    memo: data.memo,
    id: data.id
  })
}

const saveLoading = ref(false)
const handleSaveDept = async () => {
  if (saveLoading.value) return
  try {
    saveLoading.value = true
    await baseInfoFormRef.value.save()
    message.success('保存成功')
    loadDeptTree()
  } finally {
    saveLoading.value = false
  }
}

const activeKey = ref('baseInfo') // baseInfo || permission

const treeHeight = ref(document.body.offsetHeight - 300)

onMounted(() => {
  loadDeptTree()
})
</script>
