<template>
  <div>
    <div class="flex items-center mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus"></i>
        新建
      </a-button>
      <a-button @click="handleImport">
        <i class="a-icon-import-right"></i>
        导入
      </a-button>
      <a-button :loading="exportLoading" @click="handleExport">
        <i class="a-icon-export-right"></i>
        导出
      </a-button>
      <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">
        <i class="a-icon-delete"></i>
        批量删除
      </a-button>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <s-input
        v-model="params.user_dictText"
        placeholder="搜索人员名称"
        class="ml-[40px] !w-[280px]"
        @input="handleInput"
      ></s-input>
      <filter-more
        :params="params"
        :search-list="searchList"
        width="320px"
        label-width="70px"
        @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></filter-more>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'isFinish'">
          <a-switch
            v-model:checked="record.isFinish"
            checked-children="未结束"
            un-checked-children="已结束"
            @change="handleStatusChange(record, $event)"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleRemove(record)">删除</span>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('资料归档导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, updateStatus } from './apis.js'
import Edit from './components/Edit.vue'
import { page as getPositionList } from '@/views/system/position/apis'
import { Modal, message } from 'ant-design-vue'

const loadPositionList = () => getPositionList({ pageNo: 1, pageSize: 10000 })

const params = reactive({
  column: 'createTime',
  order: 'desc',
  id: undefined,
  user: undefined,
  user_dictText: undefined,
  partTimeCompany: undefined,
  partTimeDepart: undefined,
  partTimePost: undefined,
  isFinish: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined
})
const searchList = [
  { label: '兼职公司', name: 'partTimeCompany', type: 'company-select', companyType: 'all' },
  { label: '兼职部门', name: 'partTimeDepart', type: 'depart-select' },
  { label: '兼职职位', name: 'partTimePost', type: 'api-select', asyncFn: loadPositionList },
  {
    label: '是否结束',
    name: 'isFinish',
    type: 'a-select',
    fieldNames: { label: 'label', value: 'value' },
    options: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]
  },
  { label: '备注', name: 'remark', type: 's-input' },
  { label: '创建人', name: 'createBy', type: 'user-select' },
  { label: '创建时间', name: 'createTime', type: 'date' }
]

const columns = [
  { title: '人员', dataIndex: 'user_dictText', width: 100, fixed: 'left' },
  { title: '兼职公司', dataIndex: 'partTimeCompany_dictText' },
  { title: '兼职部门', dataIndex: 'partTimeDepart_dictText' },
  { title: '兼职职位', dataIndex: 'partTimePost_dictText' },
  { title: '是否结束', dataIndex: 'isFinish' },
  { title: '备注', dataIndex: 'remark', ellipsis: true },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.isFinish = !item.isFinish
  })
  return list
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open({
    id: data.id,
    user: data.user,
    partTimeCompany: data.partTimeCompany,
    partTimeDepart: data.partTimeDepart,
    partTimePost: data.partTimePost,
    isFinish: !data.isFinish,
    remark: data.remark
  })
}

const handleView = (data) => {
  editRef.value.open(
    {
      id: data.id,
      user: data.user,
      partTimeCompany: data.partTimeCompany,
      partTimeDepart: data.partTimeDepart,
      partTimePost: data.partTimePost,
      isFinish: !data.isFinish,
      remark: data.remark
    },
    'view'
  )
}

const handleStatusChange = async (data, val) => {
  try {
    await updateStatus({ ids: data.id, isFinish: !val })
    message.success('保存成功')
    data.isFinish = val
  } catch {
    data.isFinish = !val
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除该项？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('兼职管理数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
