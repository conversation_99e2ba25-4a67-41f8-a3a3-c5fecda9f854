import request from '@/apis/http'

export const getRoleList = (params) => {
  return request({
    method: 'get',
    url: '/sys/role/list',
    params
  })
}

export const addRole = (data) => {
  return request({
    method: 'post',
    url: '/sys/role/add',
    data
  })
}

export const editRole = (data) => {
  return request({
    method: 'post',
    url: '/sys/role/edit',
    data
  })
}

export const deleteRole = (params) => {
  return request({
    method: 'delete',
    url: '/sys/role/delete',
    params
  })
}

// 检查角色编码是否可用
export const checkRole = (params) => {
  return request({
    method: 'get',
    url: '/sys/role/checkRoleCode',
    params
  })
}

// 获取菜单树
export const queryTreeList = () => {
  return request({
    method: 'get',
    url: '/sys/role/queryTreeList'
  })
}

// 根据角色获取角色已有权限
export const queryRolePermission = (params) => {
  return request({
    method: 'get',
    url: '/sys/permission/queryRolePermission',
    params
  })
}

export const saveRolePermission = (data) => {
  return request({
    method: 'post',
    url: '/sys/permission/saveRolePermission',
    data
  })
}

export const queryAllNoByTenant = () => {
  return request({
    method: 'get',
    url: '/sys/role/queryallNoByTenant'
  })
}

export const userRoleList = (params) => {
  return request({
    method: 'get',
    url: '/sys/user/userRoleList',
    params
  })
}

export const deleteUserRole = (params) => {
  return request({
    method: 'delete',
    url: '/sys/user/deleteUserRole',
    params
  })
}

export const queryDataRule = (data) => {
  return request({
    method: 'get',
    url: `/sys/role/datarule/${data.functionId}/${data.roleId}`
  })
}

export const saveDataRule = (data) => {
  return request({
    method: 'post',
    url: '/sys/role/datarule',
    data
  })
}
