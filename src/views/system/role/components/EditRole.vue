<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑角色' : '新增角色'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '100px' } }" autocomplete="off">
      <a-form-item label="角色名称" name="roleName">
        <a-input v-model:value="form.roleName" placeholder="请输入角色名称" :maxlength="10" />
      </a-form-item>
      <a-form-item label="角色编码" name="roleCode">
        <a-input
          v-model:value="form.roleCode"
          placeholder="请输入角色编码"
          :maxlength="30"
          :disabled="Boolean(form.id)"
        />
      </a-form-item>
      <a-form-item label="角色描述" name="description">
        <a-textarea v-model:value="form.description" placeholder="请输入角色描述" :maxlength="200" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addRole, editRole, checkRole } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  description: '',
  roleCode: '',
  roleName: '',
  id: ''
})

const validateCode = async (rule, value) => {
  if (form.id) return Promise.resolve()
  if (!value) return Promise.reject('请输入角色编码')
  try {
    const { success } = await checkRole({ roleCode: value })
    return success ? Promise.resolve() : Promise.reject('角色编码已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}

const rules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  roleCode: [{ required: true, validator: validateCode, trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await editRole(form) : await addRole(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.description = ''
  form.roleCode = ''
  form.roleName = ''
  form.id = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
