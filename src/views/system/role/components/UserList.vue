<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="`角色用户[${params.roleName}]`"
    placement="right"
    width="680px"
  >
    <div class="flex items-center mb-[16px]">
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <a-input
        v-model:value="params.username"
        placeholder="搜索用户账号"
        @input="handleInput"
        class="ml-[40px] !w-[240px]"
      >
        <template #prefix>
          <i class="a-icon-search text-primary"></i>
        </template>
      </a-input>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: 'calc(100vh - 292px)' }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'description'">
          <div class="line-clamp-2" :title="record.description">{{ record.description }}</div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-popconfirm title="是否确认取消关联？" @confirm="handleCancelUserRole(record)">
            <span class="primary-btn">取消关联</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </a-drawer>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message } from 'ant-design-vue'
import { userRoleList, deleteUserRole } from '../apis'
import { renderDictTag } from '@/utils/render'

const visible = ref(false)

const open = (data) => {
  params.roleId = data.roleId
  params.roleName = data.roleName
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
}

const params = reactive({
  username: '',
  roleId: '',
  roleName: ''
})

const columns = [
  { title: '用户账号', dataIndex: 'username', width: 140, fixed: 'left' },
  { title: '用户姓名', dataIndex: 'realname' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'user_status', 'dot')
  },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(userRoleList)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const handleCancelUserRole = async (data) => {
  await deleteUserRole({ roleId: params.roleId, userId: data.id })
  message.success('已取消关联')
  let pageNo = pagination.value.current
  if (pageNo > 1 && list.value.length === 1) {
    pageNo--
  }
  onTableChange({ pageNo, pageSize: pagination.value.pageSize })
}

defineExpose({ open })
</script>
