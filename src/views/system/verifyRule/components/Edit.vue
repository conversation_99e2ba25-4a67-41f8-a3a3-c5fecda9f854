<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="form.id ? '编辑系统编码规则' : '新增系统编码规则'"
    placement="right"
    width="1072px"
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '74px' } }"
      autocomplete="off"
      class="!mb-[40px]"
    >
      <div class="flex gap-x-[40px]">
        <a-form-item label="规则名称" name="ruleName" class="flex-1">
          <a-input v-model:value="form.ruleName" placeholder="请输入规则名称" :maxlength="60" />
        </a-form-item>
        <a-form-item label="规则编码" name="ruleCode" class="flex-1">
          <a-input
            v-model:value="form.ruleCode"
            placeholder="请输入规则编码"
            :disabled="Boolean(form.id)"
            :maxlength="255"
          ></a-input>
        </a-form-item>
      </div>
      <a-form-item label="规则描述" name="ruleDescription">
        <a-textarea
          v-model:value="form.ruleDescription"
          placeholder="请输入规则描述"
          :maxlength="250"
          show-count
          :auto-size="{ minRows: 4, maxRows: 4 }"
        />
      </a-form-item>
    </a-form>
    <a-tabs v-model:active-key="activeKey">
      <a-tab-pane key="1" tab="局部规则">
        <div class="flex items-center justify-between py-[16px]">
          <a-button type="primary" size="medium" @click="handleAddRule('local')">
            <i class="a-icon-plus"></i>
            新增
          </a-button>
          <span class="text-secondary">
            <i class="a-icon-tips"></i>
            局部规则按照你输入的位数有序的校验
          </span>
        </div>
        <draggable v-model="localRuleList" handle=".a-icon-move" item-key="id">
          <template #item="{ element, index }">
            <div class="flex items-center justify-between mb-[16px] last-of-type:mb-[0]">
              <i
                class="a-icon-move cursor-move text-tertiary mr-[8px] text-[20px]"
                v-show="localRuleList.length > 1"
              ></i>
              <a-popconfirm title="是否确认移除？" @confirm="handleRemoveRule(index, 'local')">
                <div>
                  <i class="a-icon-remove cursor-pointer text-tertiary mr-[12px] text-[20px] hover:text-error"></i>
                </div>
              </a-popconfirm>
              <a-input v-model:value="element.digits" :maxlength="6">
                <template #prefix><span class="text-tertiary">位数</span></template>
              </a-input>
              <a-input v-model:value="element.pattern" :maxlength="200" class="mx-[12px]">
                <template #prefix><span class="text-tertiary">规则(正则表达式)</span></template>
              </a-input>
              <a-input v-model:value="element.message" :maxlength="200">
                <template #prefix><span class="text-tertiary">提示文本</span></template>
              </a-input>
            </div>
          </template>
        </draggable>
      </a-tab-pane>
      <a-tab-pane key="2" tab="全局规则">
        <div class="flex items-center justify-between py-[16px]">
          <a-button type="primary" size="medium" @click="handleAddRule('global')">
            <i class="a-icon-plus"></i>
            新增
          </a-button>
          <span class="text-secondary">
            <i class="a-icon-tips"></i>
            全局规则可校验用户输入的所有字符；全局规则的优先级比局部规则的要高。
          </span>
        </div>
        <draggable v-model="globalRuleList" handle=".a-icon-move" item-key="id">
          <template #item="{ element, index }">
            <div class="flex items-center justify-between mb-[16px] last-of-type:mb-[0]">
              <i
                class="a-icon-move cursor-move text-tertiary mr-[8px] text-[20px]"
                v-show="globalRuleList.length > 1"
              ></i>
              <a-popconfirm title="是否确认移除？" @confirm="handleRemoveRule(index, 'global')">
                <div>
                  <i class="a-icon-remove cursor-pointer text-tertiary mr-[12px] text-[20px] hover:text-error"></i>
                </div>
              </a-popconfirm>
              <a-select v-model:value="element.priority" class="w-full">
                <a-select-option value="1">优先运行</a-select-option>
                <a-select-option value="0">最后运行</a-select-option>
              </a-select>
              <a-input v-model:value="element.pattern" :maxlength="200" class="mx-[12px]">
                <template #prefix><span class="text-tertiary">规则(正则表达式)</span></template>
              </a-input>
              <a-input v-model:value="element.message" :maxlength="200">
                <template #prefix><span class="text-tertiary">提示文本</span></template>
              </a-input>
            </div>
          </template>
        </draggable>
      </a-tab-pane>
    </a-tabs>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleConfirm">确认</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { add, edit, checkRuleCode } from '../apis'
import draggable from 'vuedraggable'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    const list = JSON.parse(data.ruleJson)
    const firstIndex = list.findIndex((item) => typeof item.digits === 'number') // 第一个局部规则
    const lastIndex = list.findLastIndex((item) => typeof item.digits === 'number') // 最后一个局部规则
    localRuleList.value = list.filter((item) => item.digits !== '*')
    globalRuleList.value = list
      .slice(0, firstIndex)
      .map((i) => ({ ...i, priority: '1' }))
      .concat(list.slice(lastIndex + 1).map((i) => ({ ...i, priority: '0' })))
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  id: '',
  ruleCode: '',
  ruleName: '',
  ruleDescription: '',
  ruleJson: ''
})

const validateRuleCode = async (_, value) => {
  if (!value) return Promise.reject('请输入规则编码')
  try {
    await checkRuleCode({ tableName: 'sys_check_rule', fieldName: 'rule_code', fieldVal: value, dataId: form.id })
    return Promise.resolve()
  } catch (err) {
    return Promise.reject((err && err.message) || '该值不可用')
  }
}
const rules = {
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  ruleCode: [{ required: true, validator: validateRuleCode, trigger: 'blur' }]
}

const activeKey = ref('1') // 1=局部 2=全局
const localRuleList = ref([])
const globalRuleList = ref([])
const handleAddRule = (type) => {
  if (type === 'local') {
    localRuleList.value.push({
      id: Math.random().toString(),
      digits: '',
      pattern: '',
      message: ''
    })
  } else {
    globalRuleList.value.push({
      id: Math.random().toString(),
      digits: '*',
      pattern: '',
      message: '',
      priority: '1' // 1=优先运行 0=最后运行
    })
  }
}

const handleRemoveRule = (index, type) => {
  if (type === 'local') {
    localRuleList.value.splice(index, 1)
  } else {
    globalRuleList.value.splice(index, 1)
  }
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    // 整合两个局部规则和全局规则两个表的数据
    const firstGlobal = []
    const afterGlobal = []
    for (let i = 0; i < globalRuleList.value.length; i++) {
      const v = globalRuleList.value[i]
      v.digits = '*'
      if (v.priority === '1') {
        firstGlobal.push(v)
      } else {
        afterGlobal.push(v)
      }
    }
    const concatValues = firstGlobal
      .concat(localRuleList.value)
      .concat(afterGlobal)
      .map((item) => ({
        digits: item.digits,
        message: item.message,
        pattern: item.pattern
      }))
    params.ruleJson = JSON.stringify(concatValues)
    form.id ? await edit(params) : await add(params)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh', !form.id)
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.ruleCode = ''
  form.ruleName = ''
  form.ruleDescription = ''
  form.ruleJson = ''
  localRuleList.value = []
  globalRuleList.value = []
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
