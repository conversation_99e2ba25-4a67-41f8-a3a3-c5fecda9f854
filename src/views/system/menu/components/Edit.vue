<template>
  <a-drawer
    v-model:open="visible"
    class="edit-menu-drawer"
    :title="form.id ? '编辑菜单' : '新增菜单'"
    placement="right"
    width="600px"
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '100px' } }" autocomplete="off">
      <a-form-item label="菜单类型" name="menuType">
        <a-radio-group v-model:value="form.menuType" button-style="solid">
          <a-radio-button :value="menuType.PARENT_MENU">一级菜单</a-radio-button>
          <a-radio-button :value="menuType.CHILD_MENU">子菜单</a-radio-button>
          <a-radio-button :value="menuType.PERMISSION">按钮/权限</a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="按钮/权限" name="name" v-if="form.menuType === menuType.PERMISSION">
        <a-input v-model:value="form.name" placeholder="请输入按钮/权限名称" :maxlength="10"></a-input>
      </a-form-item>
      <a-form-item label="菜单名称" name="name" v-else>
        <a-input v-model:value="form.name" placeholder="请输入菜单名称" :maxlength="10"></a-input>
      </a-form-item>
      <a-form-item label="上级菜单" name="parentId" v-if="form.menuType !== menuType.PARENT_MENU">
        <a-tree-select
          v-model:value="form.parentId"
          show-search
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择上级菜单(可输入菜单名称进行搜索)"
          allow-clear
          :tree-data="menuTreeData"
          tree-node-filter-prop="title"
          :field-names="{
            children: 'children',
            label: 'title',
            value: 'id'
          }"
        ></a-tree-select>
      </a-form-item>
      <a-form-item label="访问路径" name="url">
        <a-input v-model:value="form.url" placeholder="请输入访问路径" :maxlength="50"></a-input>
      </a-form-item>
      <template v-if="form.menuType !== menuType.PERMISSION">
        <a-form-item label="组件名称" name="componentName">
          <a-input v-model:value="form.componentName" placeholder="请输入组件名称" :maxlength="50"></a-input>
        </a-form-item>
        <a-form-item label="菜单图标" name="icon">
          <choose-icon v-model="form.icon"></choose-icon>
        </a-form-item>
        <a-form-item label="菜单排序" name="sortNo">
          <a-input v-model:value="form.sortNo" placeholder="请输入菜单排序"></a-input>
        </a-form-item>
        <a-form-item label="是否缓存路由" name="keepAlive">
          <a-switch v-model:checked="form.keepAlive" checked-children="开" un-checked-children="关" />
        </a-form-item>
      </template>
      <a-form-item label="授权标识" name="perms" v-else>
        <a-input v-model:value="form.perms" placeholder="请输入授权标识"></a-input>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleConfirm">确认</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { validateInteger } from '@/utils/validate'
import { addMenu, editMenu, getMenuList } from '../apis'
import { message } from 'ant-design-vue'
import ChooseIcon from './ChooseIcon.vue'
import { useDictStore } from '@/store/modules/dict'

const emits = defineEmits(['loadData'])

const { menuType } = useDictStore()

const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
  loadMenuList()
}

const menuTreeData = ref([])
const loadMenuList = async () => {
  const { result } = await getMenuList({ name: '' })
  menuTreeData.value = result
}

const form = reactive({
  id: '',
  menuType: 0,
  name: '',
  url: '',
  component: '',
  componentName: null,
  icon: '',
  sortNo: '1',
  route: true,
  hidden: false,
  hideTab: false,
  keepAlive: false,
  alwaysShow: false,
  internalOrExternal: false,
  redirect: '',
  parentId: undefined,
  perms: '', // 授权标识
  status: '1'
})

const rules = computed(() => ({
  name: [{ required: true, message: `请输入${form.menuType === 3 ? '按钮/权限' : '菜单'}名称`, trigger: ['blur'] }],
  url: [{ required: form.menuType !== menuType.PERMISSION, message: '请输入访问路径', trigger: ['blur'] }],
  parentId: [{ required: true, message: '请选择上级菜单', trigger: ['blur'] }],
  sortNo: [{ required: true, validator: validateInteger(true, '菜单排序'), trigger: ['blur'] }]
}))

const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  try {
    if (form.url) {
      form.component = form.url.replace('/', '')
    }
    form.id ? await editMenu(form) : await addMenu(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    emits('loadData')
  } finally {
    handleCancel()
  }
}

const handleCancel = () => {
  form.id = ''
  form.menuType = 0
  form.name = ''
  form.url = ''
  form.component = ''
  form.componentName = null
  form.icon = ''
  form.sortNo = '1'
  form.route = true
  form.hidden = false
  form.hideTab = false
  form.keepAlive = false
  form.alwaysShow = false
  form.internalOrExternal = false
  form.redirect = ''
  form.parentId = undefined
  form.perms = ''
  form.status = '1'
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
