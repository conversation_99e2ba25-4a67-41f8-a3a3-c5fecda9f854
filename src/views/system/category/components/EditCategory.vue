<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑分类' : '新增分类'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '84px' } }" autocomplete="off">
      <a-form-item label="父级节点" name="pid" v-if="form.pid !== '0'">
        <a-tree-select
          v-model:value="form.pid"
          show-search
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择节点(可输入节点名称进行搜索)"
          allow-clear
          block-node
          :tree-data="treeData"
          tree-node-filter-prop="title"
          :field-names="{
            children: 'children',
            label: 'title',
            value: 'key'
          }"
        ></a-tree-select>
      </a-form-item>
      <a-form-item label="分类名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入分类名称" :maxlength="20" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { add, edit, tree } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
  loadTreeData()
}

const treeData = ref([])
const loadTreeData = async () => {
  const { result } = await tree({
    pcode: '',
    async: false
  })
  treeData.value = result
}

const form = reactive({
  pid: '',
  id: '',
  name: ''
})

const rules = {
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await edit(form) : await add(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.id = ''
  form.pid = ''
  form.name = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
