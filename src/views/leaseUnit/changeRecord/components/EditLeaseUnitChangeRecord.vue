<template>
  <a-drawer
    v-model:open="visible"
    class="edit-lease-unit-state-change-drawer common-drawer"
    :title="`${formData.id ? '编辑' : '新建'}租赁单元信息变更`"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">变更基础信息</h4>
      <a-form ref="basicFormRef" :model="formData" :label-col="{ style: { width: '140px' } }" :rules="rules">
        <a-form-item label="物业管理公司" name="manageCompany">
          <company-select v-model="formData.manageCompany" placeholder="请选择物业管理公司" disabled></company-select>
        </a-form-item>
        <a-form-item label="变更租赁单元" name="leaseUnit">
          <a-form-item-rest>
            <f7-select v-model="formData.leaseUnit" placeholder="请选择目标租赁单元" f7-type="leaseUnit" />
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>

        <a-form-item label="变更说明" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="变更说明" :maxlength="255" :rows="4" show-count />
        </a-form-item>
      </a-form>
      <div class="flex justify-between items-center mb-4">
        <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">变更内容</h4>
        <a-button type="primary" @click="handleImport">导入变更信息</a-button>
      </div>
      <!-- 当有数据时显示表格 -->
      <a-table
        v-if="formData.leaseUnitChangeRecordEntryList && formData.leaseUnitChangeRecordEntryList.length > 0"
        class="lease-unit-table"
        :columns="columns"
        :data-source="formData.leaseUnitChangeRecordEntryList"
        :pagination="false"
        row-key="id"
        :scroll="{ x: 800 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'beforeValue'">
            <span class="text-gray-600">{{ record.beforeDisplayValue || record.beforeValue || '-' }}</span>
          </template>
          <template v-if="column.dataIndex === 'afterValue'">
            <span class="text-blue-600 font-medium">{{ record.afterDisplayValue || record.afterValue || '-' }}</span>
          </template>
        </template>
      </a-table>
      <div v-else class="flex flex-col items-center py-[40px]">
        <img src="@/assets/imgs/no-data.png" class="w-[80px] h-[80px]" />
        <span class="text-tertiary mt-[8px]">暂无数据</span>
      </div>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <!-- 编辑租赁单元抽屉 -->
  <edit-lease-unit-for-change ref="editLeaseUnitRef" @refresh="handleLeaseUnitChange" />
</template>

<script setup>
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import EditLeaseUnitForChange from './EditLeaseUnitForChange.vue'
import {
  submitLeaseUnitChangeRecord,
  editLeaseUnitChangeRecord,
  addLeaseUnitChangeRecord,
  queryLeaseUnitChangeRecordEntryByMainId
} from '../apis'
import { getLeaseUnitList } from '../../manage/apis/leaseUnit'
import { useUserStore } from '@/store/modules/user'

const emits = defineEmits(['refresh'])

const store = useUserStore()

const visible = ref(false)
const confirmLoading = ref(false)
const basicFormRef = ref()
const editLeaseUnitRef = ref()
const originalLeaseUnitData = ref(null)

const formDataDefault = {
  id: undefined,
  manageCompany: undefined,
  leaseUnit: undefined,
  bizDate: undefined,
  remark: undefined,
  leaseUnitChangeRecordEntryList: []
}
const formData = reactive({ ...formDataDefault })

const rules = {
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  leaseUnit: [{ required: true, message: '请选择变更租赁单元', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  remark: [{ required: true, message: '请输入变更说明', trigger: 'blur' }]
}

const columns = [
  { title: '属性', dataIndex: 'property', width: 200, ellipsis: true },
  { title: '变更前值', dataIndex: 'beforeValue', width: 200, ellipsis: true },
  { title: '变更后值', dataIndex: 'afterValue', width: 200, ellipsis: true }
]

/**
 * 打开编辑抽屉
 * @param {Object|Array} data - 编辑数据或初始租赁单元列表
 */
const open = async (data) => {
  formData.manageCompany = store.userInfo.currentCompany
  formData.bizDate = dayjs().format('YYYY-MM-DD')

  if (data && data.id) {
    // 编辑模式
    Object.assign(formData, data)
    const { result } = await queryLeaseUnitChangeRecordEntryByMainId({ id: data.id })
    if (result && result.length > 0) {
      formData.leaseUnitChangeRecordEntryList = result
    }
  }
  visible.value = true
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  // 只有在正式保存时才进行表单验证
  if (!isTemporary) {
    await basicFormRef.value.validateFields()
  }

  confirmLoading.value = true
  const api = !isTemporary
    ? submitLeaseUnitChangeRecord
    : formData.id
      ? editLeaseUnitChangeRecord
      : addLeaseUnitChangeRecord
  try {
    await api(formData)
    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '新建'
    message.success(`${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 保存表单
 */
const handleSave = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 取消编辑
 */
const handleCancel = () => {
  visible.value = false
  basicFormRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  emits('refresh')
}

/**
 * 导入变更信息
 */
const handleImport = async () => {
  if (!formData.leaseUnit) {
    message.warning('请先选择变更租赁单元')
    return
  }

  try {
    // 获取原始租赁单元数据
    const { result } = await getLeaseUnitList({ id: formData.leaseUnit })
    if (result && result.records && result.records.length > 0) {
      originalLeaseUnitData.value = result.records[0]
      editLeaseUnitRef.value?.open(originalLeaseUnitData.value)
    } else {
      message.error('无法获取租赁单元信息')
    }
  } catch {
    message.error('获取租赁单元信息失败')
  }
}

/**
 * 处理租赁单元变更
 * @param {Object} newData - 变更后的租赁单元数据
 */
const handleLeaseUnitChange = (newData) => {
  if (!originalLeaseUnitData.value || !newData) return

  const changes = []
  const original = originalLeaseUnitData.value

  // 预处理新数据中的层数字段
  if (newData.currentLayer && newData.totalLayer) {
    newData.layerNum = `${newData.currentLayer}/${newData.totalLayer}`
  }

  // 获取所有原始数据的属性
  const originalKeys = Object.keys(original)

  // 直接逐项对比所有属性
  originalKeys.forEach((key) => {
    const originalValue = original[key] || ''
    const newValue = newData[key] || ''

    // 跳过一些不需要比较的系统字段
    const skipFields = ['id', 'createBy', 'createTime', 'updateBy', 'updateTime', 'sysOrgCode', 'delFlag']
    if (skipFields.includes(key) || key.endsWith('_dictText')) {
      return
    }

    if (String(originalValue) !== String(newValue)) {
      // 获取显示用的文本值
      const beforeDisplayValue = original[`${key}_dictText`] || originalValue
      const afterDisplayValue = newData[`${key}_dictText`] || newValue

      changes.push({
        property: key,
        propertyName: key,
        beforeValue: originalValue, // 传给后端的原始值
        afterValue: newValue, // 传给后端的原始值
        beforeDisplayValue, // 表格显示用的文本
        afterDisplayValue // 表格显示用的文本
      })
    }
  })

  if (changes.length > 0) {
    // 将变更记录添加到表格中
    formData.leaseUnitChangeRecordEntryList = [...formData.leaseUnitChangeRecordEntryList, ...changes]
    message.success(`检测到 ${changes.length} 项变更，已添加到变更内容中`)
  } else {
    message.info('未检测到任何变更')
  }
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.edit-lease-unit-state-change-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-date-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>
