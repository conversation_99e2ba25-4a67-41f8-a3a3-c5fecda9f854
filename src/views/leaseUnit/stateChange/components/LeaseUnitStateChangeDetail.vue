<template>
  <a-drawer
    v-model:open="visible"
    :loading="loading"
    class="common-detail-drawer"
    title="状态变更详情"
    placement="right"
    width="1072px"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          v-if="detailData.status === 'TEMP'"
          class="primary-btn"
          :class="{ 'cursor-not-allowed': loading }"
          @click="handleSubmit"
          v-auth="'biz.basicdatadeal:ct_bas_lease_unit_state_change_req_bill:submit'"
        >
          <i class="a-icon-check mr-1"></i>
          提交
        </span>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="edit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)">
                <div class="primary-btn" @click="handleEdit(detailData)">编辑</div>
              </a-menu-item>
              <a-menu-item key="back" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleBack">撤回</div>
              </a-menu-item>
              <a-menu-item key="audit" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleAudit">审核</div>
              </a-menu-item>
              <a-menu-item key="unAudit" v-if="detailData.status === 'AUDITOK'">
                <div class="primary-btn" @click="handleUnAudit">反审核</div>
              </a-menu-item>
            </a-menu>
          </template>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
        </a-dropdown>
      </div>
    </template>
    <div class="flex items-center mb-[12px]">
      <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">状态变更详情</h2>
      <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_AuditStatus"></status-tag>
    </div>
    <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
      单据编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
    </div>

    <anchor-tabs :tab-list="tabList" class="h-[calc(100vh-284px)]">
      <template #baseInfo>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">管理公司: {{ detailData.manageCompany_dictText || '-' }}</span>
          <span class="w-[50%]">变更目标状态: {{ detailData.destStatus_dictText || '-' }}</span>
          <span class="w-[50%]">业务日期: {{ detailData.bizDate }}</span>
          <span class="w-[100%] break-words whitespace-pre-wrap">变更说明: {{ detailData.remark || '-' }}</span>
        </div>
      </template>
      <template #leaseUnit>
        <a-table
          class="lease-unit-table"
          :columns="leaseUnitColumns"
          :data-source="leaseUnitList"
          :pagination="false"
          row-key="id"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'bizStatus'">
              <a-tag color="success">{{ record.bizStatus_dictText }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <span
                class="primary-btn"
                @click="handleViewLeaseUnitDetail(record)"
                v-auth="'bas:ct_bas_lease_unit:view'"
              >
                单元详情
              </span>
            </template>
          </template>
        </a-table>
      </template>
    </anchor-tabs>
  </a-drawer>

  <edit-lease-unit-state-change ref="editDrawerRef" @refresh="loadDetail(detailData.id)"></edit-lease-unit-state-change>
  <!-- 租赁单元详情抽屉 -->
  <lease-unit-detail ref="leaseUnitDetailRef" readonly />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import {
  getLeaseUnitStateChangeReqBillDetail,
  getLeaseUnitStateChangeReqBillEntryByMainId,
  submitLeaseUnitStateChangeReqBill,
  auditLeaseUnitStateChangeReqBill,
  unAuditLeaseUnitStateChangeReqBill,
  backLeaseUnitStateChangeReqBill
} from '../apis'
import EditLeaseUnitStateChange from './EditLeaseUnitStateChange.vue'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'

const emits = defineEmits(['refresh'])

const editDrawerRef = ref()
const leaseUnitDetailRef = ref(null)

const visible = ref(false)
const loading = ref(false)
const activeSection = ref('baseInfo')
const detailData = ref({})
const currentId = ref('')
const leaseUnitList = ref([])

const tabList = [
  { title: '基础信息', name: 'baseInfo' },
  { title: '变更单元', name: 'leaseUnit' }
]

const leaseUnitColumns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit_dictText', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'leaseUnitAddress', width: 160, ellipsis: true },
  { title: '租赁归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', width: 160, ellipsis: true },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 160, ellipsis: true },
  { title: '原业务状态', dataIndex: 'bizStatus', width: 120 },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

/**
 * 打开详情抽屉
 */
const open = async (record) => {
  if (!record?.id) {
    message.error('缺少必要参数')
    return
  }
  currentId.value = record.id
  visible.value = true
  await loadDetail(record.id)
}

/**
 * 获取详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  const res = await getLeaseUnitStateChangeReqBillDetail({ id })
  if (res.success) {
    detailData.value = res.result || {}
    const { result } = await getLeaseUnitStateChangeReqBillEntryByMainId({ id })
    leaseUnitList.value = result?.length ? result : []
  } else {
    message.error(res.message || '获取详情失败')
  }
  loading.value = false
}

/**
 * 编辑状态变更单
 */
const handleEdit = (record) => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_state_change_req_bill:edit')) return
  editDrawerRef.value.open(record)
}

/**
 * 提交状态变更单
 */
const handleSubmit = () => {
  if (loading.value) return
  Modal.confirm({
    title: '确认提交',
    content: '确定要提交该状态变更单吗？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        const { result } = await getLeaseUnitStateChangeReqBillEntryByMainId({ id: detailData.value.id })
        if (result?.length) {
          detailData.value.leaseUnitStateChangeReqBillEntryList = result
        }
        await submitLeaseUnitStateChangeReqBill(detailData.value)
        message.success('提交成功')
        handleClose()
      } finally {
        loading.value = false
      }
    }
  })
}

/**
 * 撤回状态变更单
 */
const handleBack = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_state_change_req_bill:edit')) return
  if (loading.value) return
  Modal.confirm({
    title: '确认撤回',
    content: '确定要撤回该状态变更单吗？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        await backLeaseUnitStateChangeReqBill({ id: detailData.value.id })
        message.success('撤回成功')
        handleClose()
      } finally {
        loading.value = false
      }
    }
  })
}

/**
 * 审核状态变更单
 */
const handleAudit = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_state_change_req_bill:audit')) return
  if (loading.value) return
  Modal.confirm({
    title: '确认审核',
    content: '确定要审核该状态变更单吗？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        await auditLeaseUnitStateChangeReqBill({ id: detailData.value.id })
        message.success('审核成功')
        handleClose()
      } finally {
        loading.value = false
      }
    }
  })
}

/**
 * 反审核状态变更单
 */
const handleUnAudit = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_state_change_req_bill:unAudit')) return
  if (loading.value) return
  Modal.confirm({
    title: '确认反审核',
    content: '确定要反审核该状态变更单吗？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        await unAuditLeaseUnitStateChangeReqBill({ id: detailData.value.id })
        message.success('反审核成功')
        handleClose()
      } finally {
        loading.value = false
      }
    }
  })
}

/**
 * 查看租赁单元详情
 */
const handleViewLeaseUnitDetail = (record) => {
  if (record?.id) {
    leaseUnitDetailRef.value.open(record)
  } else {
    message.error('租赁单元信息不完整，无法查看详情')
  }
}

/**
 * 关闭抽屉
 */
const handleClose = () => {
  emits('refresh')
  visible.value = false
  detailData.value = {}
  currentId.value = ''
  activeSection.value = 'baseInfo'
}

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.lease-unit-table {
  margin-top: 16px;

  :deep(.ant-table-thead > tr > th) {
    background-color: #f5f5f5;
    font-weight: 500;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f0f7ff;
  }

  :deep(.ant-tag) {
    border-radius: 4px;
  }
}
</style>
