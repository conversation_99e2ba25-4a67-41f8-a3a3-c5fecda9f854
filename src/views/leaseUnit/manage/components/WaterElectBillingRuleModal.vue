<!-- 批量设置水电计费规则弹窗 -->
<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="1200px"
    title="批量设置水电计费规则"
    @ok="handleOk"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <div class="mb-[24px]">
      <div class="flex justify-between items-center mb-[12px]">
        <h3 class="text-[16px] font-medium">水电信息</h3>
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          添加表号
        </a-button>
      </div>
      <a-table
        :data-source="waterElectList"
        :columns="columns"
        :pagination="false"
        :scroll="{ y: 300, x: 1800 }"
        row-key="id"
        size="small"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'name'">
            <i class="a-icon-remove cursor-pointer mr-2 text-red-500" @click="handleRemove(index)"></i>
            {{ record.name }}
          </template>
          <template v-if="column.dataIndex === 'price'">{{ renderMoney(record.price, 6, '元') }}</template>
          <template v-if="column.dataIndex === 'ullageQuantity'">
            <common-select
              v-model="record.ullageQuantity"
              :field-names="{ label: 'name', value: 'id' }"
              :options="formulaList"
            ></common-select>
          </template>
          <template v-if="column.dataIndex === 'unitShare'">
            <common-select
              v-model="record.unitShare"
              :field-names="{ label: 'name', value: 'id' }"
              :options="formulaList"
            ></common-select>
          </template>
          <template v-if="column.dataIndex === 'shareAmount'">
            <common-select
              v-model="record.shareAmount"
              :field-names="{ label: 'name', value: 'id' }"
              :options="formulaList"
            ></common-select>
          </template>
          <template v-if="column.dataIndex === 'remission'">
            <common-select
              v-model="record.remission"
              :field-names="{ label: 'name', value: 'id' }"
              :options="formulaList"
            ></common-select>
          </template>
          <template v-if="column.dataIndex === 'selfAmount'">
            <common-select
              v-model="record.selfAmount"
              :field-names="{ label: 'name', value: 'id' }"
              :options="formulaList"
            ></common-select>
          </template>
          <template v-if="column.dataIndex === 'totalAmount'">
            <common-select
              v-model="record.totalAmount"
              :field-names="{ label: 'name', value: 'id' }"
              :options="formulaList"
            ></common-select>
          </template>
          <template v-if="column.dataIndex === 'taxAmount'">
            <common-select
              v-model="record.taxAmount"
              :field-names="{ label: 'name', value: 'id' }"
              :options="formulaList"
            ></common-select>
          </template>
          <template v-if="column.dataIndex === 'containTaxTotalAmount'">
            <common-select
              v-model="record.containTaxTotalAmount"
              :field-names="{ label: 'name', value: 'id' }"
              :options="formulaList"
            ></common-select>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 水电表选择弹窗 -->
    <water-elect-select-dialog ref="waterElectSelectRef" :multiple="true" @select-change="handleWaterElectSelect" />
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { renderMoney } from '@/utils/render'
import { page as getFormulaPage } from '@/views/billingManage/formula/apis'
import {
  batchEditWaterShareFormulaRelations,
  getWaterShareFormulaRelationsByRelations
} from '../apis/waterShareFormulaRelations'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const waterElectSelectRef = ref()
const waterElectList = ref([])
const currentRelationId = ref('')
const currentRelationType = ref('Unit')

// 表格列定义
const columns = [
  { title: '名称', dataIndex: 'name', fixed: 'left', width: 200 },
  { title: '编码(表号)', dataIndex: 'number', width: 160 },
  { title: '类型', dataIndex: 'type_dictText', width: 120 },
  { title: '属性', dataIndex: 'property_dictText', width: 120 },
  { title: '倍率', dataIndex: 'doubleRate', width: 120 },
  { title: '单价', dataIndex: 'price', width: 120 },
  { title: '损耗量计算公式', dataIndex: 'ullageQuantity', width: 180 },
  { title: '单位分摊计算公式', dataIndex: 'unitShare', width: 180 },
  { title: '公摊金额计算公式', dataIndex: 'shareAmount', width: 180 },
  { title: '减免金额计算公式', dataIndex: 'remission', width: 180 },
  { title: '自用金额计算公式', dataIndex: 'selfAmount', width: 180 },
  { title: '不含税合计计算公式', dataIndex: 'totalAmount', width: 180 },
  { title: '税金计算公式', dataIndex: 'taxAmount', width: 180 },
  { title: '含税合计计算公式', dataIndex: 'containTaxTotalAmount', width: 180 }
]

/**
 * 打开弹窗
 * @param {Array} relationIds - 关联对象ID数组
 * @param {string} relationType - 关联类型
 */
const open = async (relationIds, relationType = 'Unit') => {
  if (!Array.isArray(relationIds) || relationIds.length === 0) {
    message.warning('请选择要设置的租赁单元')
    return
  }

  // 设置关联对象信息
  currentRelationId.value = relationIds.join(',')
  currentRelationType.value = relationType
  waterElectList.value = []
  visible.value = true

  // 加载已有的水电公摊关联关系数据
  await loadExistingRelations(currentRelationId.value, relationType)
}

/**
 * 加载已有的水电公摊关联关系数据
 * @param {string} relations - 关联对象ID
 * @param {string} relationType - 关联类型
 */
const loadExistingRelations = async (relations, relationType) => {
  const { result } = await getWaterShareFormulaRelationsByRelations(relations, relationType)
  if (result && Array.isArray(result)) {
    waterElectList.value = result.map((item) => ({
      id: item.waterEleTableId || item.id,
      relationId: item.id, // 保存关联关系的ID，用于更新
      name: item.waterEleTableName || item.name,
      number: item.waterEleTableNum,
      type: item.type,
      type_dictText: item.type_dictText,
      property: item.property,
      property_dictText: item.property_dictText,
      doubleRate: item.doubleRate,
      price: item.price,
      ullageQuantity: item.ullageQuantity,
      unitShare: item.unitShare,
      shareAmount: item.shareAmount,
      remission: item.remission,
      selfAmount: item.selfAmount,
      totalAmount: item.totalAmount,
      taxAmount: item.taxAmount,
      containTaxTotalAmount: item.containTaxTotalAmount
    }))
  }
}

/**
 * 添加水电表
 */
const handleAdd = () => {
  // 获取已选择的水电表ID列表，用于在弹窗中排除
  const selectedIds = waterElectList.value.map((item) => ({ value: item.id, label: item.name }))
  waterElectSelectRef.value?.open(selectedIds)
}

/**
 * 水电表选择回调
 * @param {Array} selectedRows - 选中的水电表数据
 */
const handleWaterElectSelect = (selectedRows) => {
  // 过滤掉已存在的水电表
  const existingIds = new Set(waterElectList.value.map((item) => item.id))
  const newItems = selectedRows.filter((item) => !existingIds.has(item.id))

  // 为新选择的水电表添加默认的计算公式
  const itemsWithDefaults = newItems.map((item) => ({
    ...item
  }))

  waterElectList.value.push(...itemsWithDefaults)
}

/**
 * 删除水电表
 * @param {number} index - 要删除的索引
 */
const handleRemove = (index) => {
  waterElectList.value.splice(index, 1)
}

/**
 * 确认保存
 */
const handleOk = async () => {
  if (waterElectList.value.length === 0) {
    message.warning('请至少添加一个水电表')
    return
  }

  // 检查是否所有水电表都已设置计算公式
  const incompleteItems = waterElectList.value.filter(
    (item) =>
      !item.ullageQuantity ||
      !item.unitShare ||
      !item.shareAmount ||
      !item.remission ||
      !item.selfAmount ||
      !item.totalAmount ||
      !item.taxAmount ||
      !item.containTaxTotalAmount
  )

  if (incompleteItems.length > 0) {
    message.warning('请为所有水电表设置完整的计算公式')
    return
  }

  // 构建批量编辑的数据
  const batchData = waterElectList.value.map((item) => ({
    id: item.relationId || undefined, // 如果是新增则不传id
    relationType: currentRelationType.value,
    relation: currentRelationId.value,
    waterEleTableNum: item.id,
    type: item.type,
    property: item.property,
    doubleRate: item.doubleRate,
    price: item.price,
    ullageQuantity: item.ullageQuantity,
    unitShare: item.unitShare,
    shareAmount: item.shareAmount,
    remission: item.remission,
    selfAmount: item.selfAmount,
    totalAmount: item.totalAmount,
    taxAmount: item.taxAmount,
    containTaxTotalAmount: item.containTaxTotalAmount
  }))

  await batchEditWaterShareFormulaRelations(batchData)

  message.success('设置成功')
  handleCancel()
}

/**
 * 取消操作
 */
const handleCancel = () => {
  emits('refresh')
  waterElectList.value = []
  visible.value = false
}

const formulaList = ref([])
const loadFormulaList = async () => {
  const { result } = await getFormulaPage({ pageNo: 1, pageSize: 10000, status: 'ENABLE' })
  formulaList.value = result.records
}

onMounted(() => {
  loadFormulaList()
})

defineExpose({ open })
</script>
