<template>
  <div>
    <!-- 水电表格 -->
    <div class="mt-[24px] mb-[12px] flex justify-between items-center">
      <h4 class="text-[16px] font-bold text-[#1d335c]">自用水电表</h4>
      <a-button type="primary" size="medium" @click="handleAdd">
        <i class="a-icon-plus mr-1"></i>
        添加
      </a-button>
    </div>
    <a-table
      :columns="waterElectricColumns"
      :data-source="props.formData.waterShareFormulas || []"
      :scroll="{ x: 1200 }"
      :pagination="false"
      row-key="id"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'name'">
          <i class="a-icon-remove cursor-pointer mr-2 text-red-500" @click="handleDelete(record, index)"></i>
          {{ record.name }}
        </template>
        <template v-if="column.dataIndex === 'selfAmount'">
          <common-select
            v-model="record.selfAmount"
            :field-names="{ label: 'name', value: 'id' }"
            :options="formulaList"
          ></common-select>
        </template>
      </template>
    </a-table>

    <!-- 水电表选择弹窗 -->
    <f7-modal
      ref="f7ModalRef"
      f7-type="waterElectricity"
      :multiple="true"
      :extra-params="{ manageCompany: props.formData.manageCompany }"
      @update-value="handleWaterElectSelect"
    />
  </div>
</template>

<script setup>
import { renderMoney } from '@/utils/render'
import { page as getFormulaPage } from '@/views/billingManage/formula/apis'
import { message } from 'ant-design-vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const waterElectricColumns = [
  { title: '名称', dataIndex: 'name', width: 160, fixed: 'left', ellipsis: true },
  { title: '编码(表号)', dataIndex: 'number', width: 120, ellipsis: true },
  { title: '类型', dataIndex: 'type_dictText', width: 80, ellipsis: true },
  { title: '属性', dataIndex: 'property_dictText', width: 80, ellipsis: true },
  { title: '倍率', dataIndex: 'ratio', width: 80 },
  { title: '单价', dataIndex: 'price', width: 80, customRender: ({ text }) => renderMoney(text, 6, '元') },
  {
    title: '自用金额计算公式',
    dataIndex: 'selfAmount',
    width: 160,
    fixed: 'right'
  }
]

const f7ModalRef = ref()

/**
 * 添加水电表信息
 */
const handleAdd = () => {
  // 获取当前已选择的水电表数据，用于默认选中
  // 需要恢复原始的id值以便弹窗能正确识别选中状态
  const currentSelected = (props.formData.waterShareFormulas || []).map((item) => ({
    ...item,
    id: item.waterEleTableNum // 恢复原始id用于弹窗选中识别
  }))
  f7ModalRef.value.open(currentSelected)
}

/**
 * 水电表选择回调
 */
const handleWaterElectSelect = (selectedKeys, selectedRows) => {
  // 确保 waterShareFormulas 数组存在
  if (!props.formData.waterShareFormulas) {
    props.formData.waterShareFormulas = []
  }

  // 清空现有数据
  props.formData.waterShareFormulas = []

  // 将选中的水电表数据添加到表格中
  selectedRows.forEach((item) => {
    const newWaterElectric = {
      ...item,
      shareType: item.type.toLowerCase(),
      waterEleTableNum: item.id,
      ratio: item.doubleRate,
      selfAmount: formulaList.value.length ? formulaList.value[0].id : '',
      relation: 'Unit'
    }
    newWaterElectric.id = undefined
    props.formData.waterShareFormulas.push(newWaterElectric)
  })
}

/**
 * 删除水电表信息
 */
const handleDelete = (record, index) => {
  if (props.formData.waterShareFormulas && props.formData.waterShareFormulas.length > index) {
    props.formData.waterShareFormulas.splice(index, 1)
  }
}

const validate = () => {
  if (!(props.formData.waterShareFormulas && props.formData.waterShareFormulas.length)) return true
  const index = props.formData.waterShareFormulas.findIndex((i) => !i.selfAmount)
  if (index !== -1) {
    message.warning(`第${index + 1}条水电表，自用金额计算公式不可为空`)
  }
  return index === -1
}

const formulaList = ref([])
const loadFormulaList = async () => {
  const { result } = await getFormulaPage({ pageNo: 1, pageSize: 10000, status: 'ENABLE' })
  formulaList.value = result.records.filter((item) => item.calculResult === 'selfAmount')
}
onMounted(() => {
  loadFormulaList()
})

defineExpose({ validate })
</script>
