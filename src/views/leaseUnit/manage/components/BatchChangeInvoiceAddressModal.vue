<template>
  <a-modal
    v-model:open="visible"
    title="批量更新发票地址"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    width="500px"
    class="common-modal"
    :mask-closable="false"
  >
    <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="发票地址" name="invoiceAddress" :rules="[{ required: true, message: '请输入发票地址' }]">
        <a-input v-model:value="formData.invoiceAddress" placeholder="请输入发票地址" maxlength="200" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { batchChangeInvoiceAddress } from '../apis/leaseUnit'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const loading = ref(false)
const selectedRecords = ref([])

const formData = reactive({
  invoiceAddress: ''
})

/**
 * 打开弹窗
 */
const open = (records = []) => {
  selectedRecords.value = records
  formData.invoiceAddress = ''
  visible.value = true
}

/**
 * 取消操作
 */
const handleCancel = () => {
  emits('refresh')
  visible.value = false
  formData.invoiceAddress = ''
  selectedRecords.value = []
}

/**
 * 提交批量更新发票地址
 */
const handleSubmit = async () => {
  if (!formData.invoiceAddress) {
    message.warning('请输入发票地址')
    return
  }

  if (!selectedRecords.value || selectedRecords.value.length === 0) {
    message.warning('请选择要更新的租赁单元')
    return
  }

  loading.value = true
  try {
    const params = {
      invoiceAddress: formData.invoiceAddress,
      leaseUnitList: selectedRecords.value
    }

    await batchChangeInvoiceAddress(params)
    message.success('批量更新发票地址成功')
  } finally {
    loading.value = false
  }
  handleCancel()
}

defineExpose({
  open
})
</script>

<style scoped lang="less">
.common-modal {
  :deep(.ant-modal-body) {
    padding: 20px;
  }
}
</style>
