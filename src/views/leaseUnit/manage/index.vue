<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex flex-wrap gap-y-[16px]">
        <a-button type="primary" v-auth="'bas:ct_bas_lease_unit:add'" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button v-auth="'bas:ct_bas_lease_unit:importExcel'" @click="handleImport">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button v-auth="'bas:ct_bas_lease_unit:exportXls'" :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchSetWaterElect">
                <div class="primary-btn" @click="handleBatchSetWaterElect">批量设置水电计费规则</div>
              </a-menu-item>
              <a-menu-item key="batchChangeAreaManager">
                <div class="primary-btn" @click="handleBatchChangeAreaManager">批量更新片管</div>
              </a-menu-item>
              <a-menu-item key="batchChangeInvoiceAddress">
                <div class="primary-btn" @click="handleBatchChangeInvoiceAddress">批量更新发票地址</div>
              </a-menu-item>
              <a-menu-item key="batchDelete">
                <div class="primary-btn" @click="handleBatchDelete">批量删除</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.name"
          placeholder="搜索租赁单元名称"
          class="ml-[40px] !w-[240px]"
          @input="handleSearch"
        ></s-input>
        <lease-unit-tree-filter class="!ml-[16px] !w-[220px]" @tree-node-change="handleTreeNodeChange" />
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'detailAddress'">
          {{ record.detailAddress }}
        </template>
        <template v-if="column.dataIndex === 'leaseArea' && record.leaseArea">
          {{ renderMoney(record.leaseArea, 4, 'm²') }}
        </template>
        <template v-if="column.dataIndex === 'status'">
          <status-tag
            :dict-value="record.status"
            dict-code="CT_BASE_ENUM_HouseOwner_AuditStatus"
            type="dot"
          ></status-tag>
        </template>
        <template v-if="column.dataIndex === 'property'">
          <span
            v-if="record.houseOwner"
            class="primary-btn"
            @click="handleAssetsDetail(record)"
            v-auth="'bas:ct_bas_house_owner:view'"
          >
            查看
          </span>
          <span v-else>-</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" v-auth="'bas:ct_bas_lease_unit:view'" @click="handleDetail(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" v-if="['TEMP', 'BACK', 'AUDITNO', 'DISABLE'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item key="submit" v-if="['BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleSubmit(record)">提交</div>
                </a-menu-item>
                <a-menu-item key="back" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleBack(record)">撤回</div>
                </a-menu-item>
                <a-menu-item key="audit" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleAudit(record)">审核</div>
                </a-menu-item>
                <a-menu-item key="unAudit" v-if="record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleUnAudit(record)">反审核</div>
                </a-menu-item>
                <a-menu-item key="delete" v-if="['TEMP', 'BACK', 'DISABLE'].includes(record.status)">
                  <div class="primary-btn" @click="handleDelete(record)">删除</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('租赁单元导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>

    <edit-lease-unit ref="editDrawerRef" @refresh="onTableChange"></edit-lease-unit>
    <lease-unit-detail ref="detailDrawerRef" @refresh="onTableChange"></lease-unit-detail>
    <assets-detail ref="assetsDetailDrawerRef"></assets-detail>
    <water-elect-billing-rule-modal ref="waterElectBillingRuleModalRef" @refresh="onTableChange" />
    <batch-change-area-manager-modal ref="batchChangeAreaManagerModalRef" @refresh="onTableChange" />
    <batch-change-invoice-address-modal ref="batchChangeInvoiceAddressModalRef" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import region from '@/json/region.json'
import { hasPermission } from '@/utils/permission'
import { renderMoney } from '@/utils/render'
import { getFilesById } from '@/apis/common'
import AssetsDetail from '@/views/assets/manage/components/Detail.vue'
import EditLeaseUnit from './components/EditLeaseUnit.vue'
import LeaseUnitDetail from './components/LeaseUnitDetail.vue'
import WaterElectBillingRuleModal from './components/WaterElectBillingRuleModal.vue'
import BatchChangeAreaManagerModal from './components/BatchChangeAreaManagerModal.vue'
import BatchChangeInvoiceAddressModal from './components/BatchChangeInvoiceAddressModal.vue'
import { getUserList } from '@/views/system/user/apis'
import { projectPage } from '@/views/projects/apis.js'
import { getPage } from '@/views/assets/manage/apis'
import { queryWaterElectricityById } from '@/views/waterElectricity/manage/apis/waterElectricity'
import {
  getLeaseUnitList,
  deleteLeaseUnit,
  batchDeleteLeaseUnit,
  importExcel,
  exportExcel,
  audit,
  unAudit,
  back,
  submitLeaseUnit
} from './apis/leaseUnit'
import LeaseUnitTreeFilter from './components/LeaseUnitTreeFilter.vue'

const route = useRoute()
const editDrawerRef = ref()
const detailDrawerRef = ref()
const assetsDetailDrawerRef = ref()
const waterElectBillingRuleModalRef = ref()
const batchChangeAreaManagerModalRef = ref()
const batchChangeInvoiceAddressModalRef = ref()
const commonImportRef = ref()
const columnSetRef = ref()
const exportLoading = ref(false)
let timer

const searchParams = reactive({
  column: 'number',
  order: 'desc',
  name: undefined,
  pcaCode: [],
  useType: undefined,
  leaseUse: undefined,
  areaManager: undefined,
  status: undefined,
  bizStatus: undefined,
  supportFacility: undefined,
  effectDate: undefined,
  expireDate: undefined,
  layerNum: undefined,
  propertyUse: undefined,
  wyProject: undefined,
  number: undefined,
  treeId: undefined
})

const searchList = reactive([
  {
    label: '区域',
    name: 'pcaCode',
    type: 'cascader',
    placeholder: '请选择区域',
    options: region,
    fieldNames: { label: 'label', value: 'value', children: 'children' }
  },
  { label: '使用类型', name: 'useType', type: 'dict-select', placeholder: '请选择使用类型', code: 'CT_BAS_UseType' },
  { label: '租赁用途', name: 'leaseUse', type: 'dict-select', code: 'CT_BAS_LeaseUse', placeholder: '请选择租赁用途' },
  {
    label: '片区管理员',
    name: 'areaManager',
    type: 'api-select',
    placeholder: '请选择片区管理员',
    asyncFn: getUserList,
    fieldNames: { label: 'realname', value: 'id' }
  },
  {
    label: '单据状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_HouseOwner_AuditStatus',
    placeholder: '请选择单据状态'
  },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_LeaseUnit_BizStatus',
    placeholder: '请选择业务状态'
  },
  { label: '配套设施', name: 'supportFacility', type: 's-input', placeholder: '请输入配套设施' },
  { label: '生效日期', name: 'effectDate', type: 'date', placeholder: '请选择生效日期' },
  { label: '到期日期', name: 'expireDate', type: 'date', placeholder: '请选择到期日期' },
  { label: '层数/总层数', name: 'layerNum', type: 's-input', placeholder: '请输入层数' },
  {
    label: '产权用途',
    name: 'propertyUse',
    type: 'dict-select',
    code: 'CT_BAS_PropertyUse',
    placeholder: '请选择产权用途'
  },
  { label: '所属项目', name: 'wyProject', type: 'api-select', placeholder: '请选择所属项目', asyncFn: projectPage },
  { label: '关联资产', name: 'houseOwner', type: 'api-select', placeholder: '请选择关联资产', asyncFn: getPage },
  { label: '单元编号', name: 'number', type: 's-input', placeholder: '请输入单元编号' }
])

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getLeaseUnitList)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const defaultColumns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, ellipsis: true, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 160, ellipsis: true },
  { title: '使用类型', dataIndex: 'useType_dictText', width: 160 },
  { title: '租赁面积', dataIndex: 'leaseArea', width: 160 },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText', width: 120 },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 160, ellipsis: true },
  { title: '单据状态', dataIndex: 'status', width: 120 },
  { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '配套设施', dataIndex: 'supportFacility', width: 160, ellipsis: true },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期时间', dataIndex: 'expireDate', width: 120 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '产权', dataIndex: 'property', width: 120 },
  { title: '项目', dataIndex: 'wyProject_dictText', width: 160, ellipsis: true },
  { title: '租赁单元编号', dataIndex: 'number', width: 200, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

/**
 * 打开新建租赁单元弹窗
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 打开编辑租赁单元弹窗
 */
const handleEdit = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit:edit')) return
  editDrawerRef.value.open(record)
}

/**
 * 打开租赁单元详情弹窗
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 打开资产详情弹窗
 */
const handleAssetsDetail = (record) => {
  if (record.houseOwner) {
    assetsDetailDrawerRef.value.open(record.houseOwner)
  } else {
    message.warning('该租赁单元未关联资产')
  }
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出租赁单元数据
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('租赁单元清单.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

/**
 * 删除单个租赁单元
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除租赁单元"${record.name || record.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteLeaseUnit({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除租赁单元
 */
const handleBatchDelete = () => {
  if (!hasPermission('bas:ct_bas_lease_unit:deleteBatch')) return

  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }
  if (selectedRows.value.some((item) => item.status === 'AUDITING')) {
    message.warning('所选租赁单元中存在审核中的数据，无法进行批量删除')
    return
  }
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个租赁单元吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteLeaseUnit({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 批量设置水电计费规则
 */
const handleBatchSetWaterElect = () => {
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要设置水电计费规则的租赁单元')
    return
  }
  if (selectedRows.value.some((item) => item.status === 'AUDITING')) {
    message.warning('所选租赁单元中存在审核中的数据，无法进行批量设置水电计费规则')
    return
  }
  waterElectBillingRuleModalRef.value?.open(selectedRowKeys.value)
}

/**
 * 批量更新片区管理员
 */
const handleBatchChangeAreaManager = () => {
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要更新片区管理员的租赁单元')
    return
  }
  if (selectedRows.value.some((item) => item.status === 'AUDITING')) {
    message.warning('所选租赁单元中存在审核中的数据，无法进行批量更新片区管理员')
    return
  }
  batchChangeAreaManagerModalRef.value?.open(selectedRows.value)
}

/**
 * 批量更新发票地址
 */
const handleBatchChangeInvoiceAddress = () => {
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要更新发票地址的租赁单元')
    return
  }
  if (selectedRows.value.some((item) => item.status === 'AUDITING')) {
    message.warning('所选租赁单元中存在审核中的数据，无法进行批量更新发票地址')
    return
  }
  batchChangeInvoiceAddressModalRef.value?.open(selectedRows.value)
}

/**
 * 执行审核操作
 */
const handleAudit = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit:audit')) return

  Modal.confirm({
    title: '确认审核',
    content: `确定要对租赁单元"${record.name || record.number}"执行审核操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await audit({ id: record.id })
      message.success('审核成功')
      onTableChange()
    }
  })
}

/**
 * 执行反审核操作
 */
const handleUnAudit = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: `确定要对租赁单元"${record.name || record.number}"执行反审核操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await unAudit({ id: record.id })
      message.success('反审核成功')
      onTableChange()
    }
  })
}

/**
 * 提交租赁单元
 */
const handleSubmit = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit:submit')) return
  Modal.confirm({
    title: '确认提交',
    content: `确定要提交租赁单元"${record.name || record.number}"审核吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 准备提交数据
        const submitData = { ...record }

        // 获取附件信息
        try {
          const fileList = await getFilesById(record.id)
          submitData.attachmentIds = fileList.map((file) => file.id).join(',')
        } catch {
          submitData.attachmentIds = undefined
        }

        // 获取水电表信息
        if (record.waterShareFormulas && record.waterShareFormulas.length > 0) {
          const promises = record.waterShareFormulas.map((item) =>
            queryWaterElectricityById({ id: item.waterEleTableNum })
          )
          const results = await Promise.all(promises)
          submitData.waterShareFormulas.forEach((item, idx) => {
            const res = results[idx]
            if (res && res.success && res.result) {
              Object.assign(item, res.result)
            }
          })
        }

        await submitLeaseUnit(submitData)
        message.success('提交成功')
        onTableChange()
      } catch (error) {
        message.error(error.message || '提交失败')
      }
    }
  })
}

/**
 * 撤回租赁单元
 */
const handleBack = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回租赁单元"${record.name || record.number}"审核吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await back({ id: record.id })
      message.success('撤回成功')
      onTableChange()
    }
  })
}

/**
 * 表格变化事件处理
 */
const onTableChange = ({ current = pagination.value.current, pageNo, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: pageNo ?? current, pageSize, ...searchParams })
}

/**
 * 搜索输入防抖处理
 */
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      current: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

/**
 * 处理树节点变更
 */
const handleTreeNodeChange = (nodeId) => {
  searchParams.treeId = nodeId
  onTableChange({ current: 1 })
}

onMounted(() => {
  // 资产页面跳转过来 （id:资产id）
  if (route.query.id) {
    searchParams.houseOwner = route.query.id
  }
  onTableChange()
})
</script>
