<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="70%"
    title="自定义常用功能"
    @ok="handleOk"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <div class="flex justify-between">
      <div class="left-wrapper w-[70%] pr-[24px]">
        <!-- 锚点导航 -->
        <anchor-tabs :tab-list="navList" height="calc(100vh - 590px)">
          <template #1></template>
        </anchor-tabs>
      </div>
      <div class="right-wrapper pl-[24px] w-[30%] border-l border-[#EAEAEA]">
        <div class="text-secondary mb-[22px]">
          <span class="a-icon-tips mr-[5px]"></span>
          <span>拖动可调整显示列顺序</span>
        </div>
        <draggable v-model="customList" handle=".a-icon-move" item-key="id">
          <template #item="{ element, index }">
            <div class="flex justify-between items-center p-[10px] bg-[#F5F5F5] mb-[12px] rounded-[8px]">
              <div>
                <i class="a-icon-move cursor-move text-secondary mr-[5px]"></i>
                <span>{{ element.name }}</span>
              </div>
              <span class="a-icon-remove cursor-pointer hover:text-error" @click="rowDel(index)"></span>
            </div>
          </template>
        </draggable>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
import { Modal } from 'ant-design-vue'
import draggable from 'vuedraggable'
const visible = ref(false)
const open = () => {
  visible.value = true
}
defineExpose({ open })
// 导航项
const navList = [
  { name: '1', title: '项目' },
  { name: '2', title: '资产' },
  { name: '3', title: '租赁单元' },
  { name: '4', title: '模块4' },
  { name: '5', title: '模块5' },
  { name: '6', title: '模块6' },
  { name: '7', title: '模块7' }
]
const customList = ref([
  { name: '新建合同', id: '1' },
  { name: '新建客户', id: '2' },
  { name: '导入客户', id: '3' },
  { name: '导入合同', id: '4' },
  { name: '添加客户跟进', id: '5' }
])

const rowDel = (index) => {
  Modal.confirm({
    title: '确定删除当前常用工能吗？',
    content: '',
    centered: true,
    onOk: () => {
      customList.value.splice(index, 1)
    }
  })
}
const handleOk = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}
</script>
