<template>
  <a-drawer
    v-model:open="visible"
    class="project-detail-drawer common-detail-drawer"
    title="项目详情"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span class="primary-btn" @click="handleEditProject" v-if="detail.status !== 'ENABLE'">编辑</span>
        <span class="primary-btn" @click="handleProjectStatus">
          {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
        </span>
        <span class="primary-btn" @click="handleDeleteProject" v-if="detail.status !== 'ENABLE'">删除</span>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.name }}</h2>
        <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <div>
        <h2 class="text-[16px] font-bold mb-[12px]">项目基础信息</h2>
        <a-row :gutter="20" class="text-secondary">
          <a-col :span="12">项目名称: {{ detail.name }}</a-col>
          <a-col :span="12">物业管理公司: {{ detail.company_dictText }}</a-col>
        </a-row>
        <div class="text-secondary mt-[12px] break-all">备注: {{ detail.remark }}</div>
      </div>
      <div class="flex items-center justify-between mt-[40px] mb-[12px]">
        <h2 class="text-[16px] font-bold">楼栋信息</h2>
      </div>
      <div class="flex gap-[16px]" v-if="buildingList.length">
        <ul class="w-[240px] max-h-[calc(100vh-390px)] overflow-y-auto">
          <li
            v-for="item in buildingList"
            :key="item.id"
            class="building-item"
            :class="{ active: currentBuilding.id === item.id }"
            @click="handleSwitchBuilding(item)"
          >
            <div class="flex-1 overflow-hidden">
              <h4 class="line-clamp-1" :title="item.name">{{ item.name }}</h4>
              <small>{{ item.number }}</small>
            </div>
            <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="item.status"></status-tag>
          </li>
        </ul>
        <section class="floor-section">
          <div class="flex items-center justify-between p-[16px] border-0 border-b border-solid border-[#e6e9f0]">
            <strong class="text-[16px]">{{ currentBuilding.name }}: 共{{ floorList.length }}楼层</strong>
          </div>
          <a-table
            :data-source="floorList"
            :columns="columns"
            :loading="tableLoading"
            :pagination="false"
            :scroll="{ y: 'calc(100vh - 504px)' }"
            class="floor-table"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'leaseUnitCount'">
                <span v-if="record.leaseUnitCount === 0">0</span>
                <span class="primary-btn" v-else @click="handleViewLeaseUnit(record)">
                  查看({{ record.leaseUnitCount }})
                </span>
              </template>
            </template>
          </a-table>
        </section>
      </div>
      <a-empty description="暂未添加楼栋信息" v-else></a-empty>
      <div class="flex items-center justify-between mt-[40px] mb-[12px]">
        <h2 class="text-[16px] font-bold">项目水电分摊信息</h2>
        <span v-if="detail.everyMonthAutoBillDay">自动出账时间: 每月{{ detail.everyMonthAutoBillDay }}日</span>
      </div>
      <a-table
        :data-source="waterElectricityList"
        :columns="waterElectricityColumns"
        :pagination="false"
        :scroll="{ y: '50vh', x: 2100 }"
      ></a-table>
    </a-spin>
  </a-drawer>
  <watch-lease-unit ref="watchLeaseUnitRef"></watch-lease-unit>
</template>

<script setup>
import { projectDetail, queryBuilding, deleteProject, updateStatus } from '../apis.js'
import { queryFloor, getWater } from '@/views/building/apis/building.js'
import { message, Modal } from 'ant-design-vue'
import { renderMoney } from '@/utils/render'
import WatchLeaseUnit from '@/views/building/components/WatchLeaseUnit.vue'

const emit = defineEmits(['editProject', 'refresh'])

const visible = ref(false)
const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const loading = ref(false)
const detail = reactive({
  id: '',
  name: '',
  company_dictText: '',
  number: '',
  remark: '',
  createTime: '',
  createBy_dictText: '',
  status: ''
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await projectDetail({ id })
  Object.assign(detail, result)
  await Promise.all([loadBuildingList(id), loadWaterElectricityList(id)])
  loading.value = false
}

const handleEditProject = () => {
  visible.value = false
  emit('editProject', detail)
}

const handleProjectStatus = () => {
  Modal.confirm({
    title: `确认${detail.status === 'ENABLE' ? '禁用' : '启用'}该项目？`,
    content: '',
    centered: true,
    onOk: async () => {
      await updateStatus({
        ids: detail.id,
        status: detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(detail.status === 'ENABLE' ? '已禁用' : '启用成功')
      detail.status = detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      emit('refresh')
    }
  })
}

const handleDeleteProject = () => {
  Modal.confirm({
    title: '确认删除该项目？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteProject({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const waterElectricityList = ref([])
// 获取水电分摊信息
const loadWaterElectricityList = async (id) => {
  const { result } = await getWater({
    relation: id,
    relationType: 'WyProject',
    pageNo: 1,
    pageSize: 1000
  })
  waterElectricityList.value = result.records
}
const waterElectricityColumns = [
  { title: '分摊类别', dataIndex: 'shareType_dictText', width: 200, fixed: 'left' },
  { title: '编码(表号)', dataIndex: 'waterEleTableNum_dictText', width: 240 },
  { title: '类型', dataIndex: 'type_dictText' },
  { title: '属性', dataIndex: 'property_dictText' },
  { title: '倍率', dataIndex: 'doubleRate' },
  { title: '单价', dataIndex: 'price', customRender: ({ text }) => renderMoney(text, 6) },
  { title: '损耗量计算公式', dataIndex: 'ullageQuantity_dictText' },
  { title: '单位分摊计算公式', dataIndex: 'unitShare_dictText' },
  { title: '公摊金额计算公式', dataIndex: 'shareAmount_dictText' },
  { title: '减免金额计算公式', dataIndex: 'remission_dictText' },
  { title: '不含税合计计算公式', dataIndex: 'totalAmount_dictText', width: 170 },
  { title: '税金计算公式', dataIndex: 'taxAmount_dictText' },
  { title: '含税合计计算公式', dataIndex: 'containTaxTotalAmount_dictText' }
]

const buildingList = ref([])
const loadBuildingList = async (id) => {
  const { result } = await queryBuilding({ id })
  buildingList.value = result
  if (!currentBuilding.id) {
    Object.assign(currentBuilding, buildingList.value[0])
    loadFloorList(currentBuilding.id)
  } else {
    loadFloorList(currentBuilding.id)
  }
}

const currentBuilding = reactive({
  id: '',
  name: '',
  status: ''
})

const handleSwitchBuilding = (item) => {
  Object.assign(currentBuilding, item)
  loadFloorList(currentBuilding.id)
}

const tableLoading = ref(false)
const columns = [
  { title: '楼层名称', dataIndex: 'name' },
  { title: '租赁单元', dataIndex: 'leaseUnitCount', width: 160 }
]
const floorList = ref([])
const loadFloorList = async (id) => {
  tableLoading.value = true
  const { result } = await queryFloor({ id })
  floorList.value = result
  tableLoading.value = false
}

const watchLeaseUnitRef = ref()
const handleViewLeaseUnit = (data) => {
  watchLeaseUnitRef.value.open({
    id: data.id,
    name: data.name,
    projectName: detail.name,
    buildingName: currentBuilding.name
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.project-detail-drawer {
  .building-item {
    border-radius: 8px;
    border: 1px solid #e6e9f0;
    background-color: #f7f8fa;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    transition:
      border-color 0.2s,
      background-color 0.2s;
    cursor: pointer;
    &:hover {
      border-color: var(--color-primary);
    }
    &.active {
      border-color: var(--color-primary);
      background-color: #eaf0fe;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .floor-section {
    flex: 1;
    border: 1px solid #e6e9f0;
    border-radius: 8px;
    overflow: hidden;
  }
  .floor-table {
    .ant-table {
      border: none;
      overflow: visible;
      .ant-table-cell {
        border-bottom: 1px solid #f0f0f0 !important;
      }
    }
  }
}
</style>
