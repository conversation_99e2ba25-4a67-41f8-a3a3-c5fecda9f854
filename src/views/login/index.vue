<template>
  <div class="h-[100vh] login-page flex justify-end">
    <div class="logo">
      <img src="@/assets/imgs/logo-row.png" class="w-[250px]" />
    </div>
    <section
      class="w-[40%] px-[80px] pt-[8vh] max-1690px:pt-[6vh] bg-[rgba(255,255,255,0.8)] border border-solid border-white max-1570px:px-[50px] max-1376px:pt-[20px]"
    >
      <h1 class="text-[40px] font-bold max-1690px:text-[32px] max-1570px:text-[28px] max-1376px:text-[24px]">
        欢迎回来！
      </h1>
      <div class="flex items-center my-[40px] max-1690px:my-[30px] max-1570px:my-[24px] max-1376px:my-[20px]">
        <span
          class="text-secondary text-[24px] cursor-pointer transition-colors max-1690px:text-[20px]"
          :class="{ 'font-bold !text-primary': loginMethod === 'account' }"
          @click="changeLoginMethod('account')"
        >
          账号密码登录
        </span>
        <span class="w-[2px] h-[20px] bg-[#d7dae0] rounded-[1px] mx-[24px] max-1690px:h-[18px]"></span>
        <span
          class="text-secondary text-[24px] cursor-pointer transition-colors max-1690px:text-[20px]"
          :class="{ 'font-bold !text-primary': loginMethod === 'phone' }"
          @click="changeLoginMethod('phone')"
        >
          手机验证码登录
        </span>
      </div>
      <a-form :model="form" ref="formRef" :rules="rules" layout="vertical" hide-required-mark>
        <a-form-item label="登录账号" name="username">
          <a-input v-model:value="form.username" placeholder="请输入登录账号" :maxlength="30">
            <template #prefix>
              <i class="a-icon-account"></i>
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="登录密码" name="password">
          <a-input
            v-model:value="form.password"
            placeholder="请输入登录密码"
            :maxlength="20"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <i class="a-icon-password"></i>
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="验证码" name="captcha">
          <a-input
            v-model:value="form.captcha"
            placeholder="请输入图形验证码"
            :maxlength="4"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <i class="a-icon-key"></i>
            </template>
            <template #suffix>
              <img :src="form.captchaImg" class="cursor-pointer" @click="loadCaptchaImg" />
            </template>
          </a-input>
        </a-form-item>
      </a-form>
      <p
        class="text-tertiary text-[20px] mt-[56px] mb-[30px] max-1690px:mt-[40px] max-1690px:mb-[20px] max-1570px:text-[16px]"
      >
        <span>登录即代表同意</span>
        <span class="text-primary cursor-pointer">《用户协议》</span>
        <span>、</span>
        <span class="text-primary cursor-pointer">《隐私政策》</span>
      </p>
      <a-button type="primary" class="login-btn" :loading="loading" @click="handleLogin">登录</a-button>
      <p class="text-tertiary text-[20px] mt-[40px] max-1690px:mt-[24px] max-1570px:text-[16px] max-1570px:mt-[20px]">
        <span>忘记密码？</span>
        <span class="text-primary cursor-pointer">找回密码</span>
      </p>
    </section>
  </div>
</template>

<script setup>
import { useUserStore } from '@/store/modules/user'
import { getCaptchaImg } from '@/apis/user'

const store = useUserStore()
const router = useRouter()

const loginMethod = ref('account') // 登录方式 account | phone
const changeLoginMethod = (method) => {
  loginMethod.value = method
}

const form = reactive({
  username: 'admin',
  password: 'Xmqgjt1234!',
  checkKey: '',
  captcha: '',
  captchaImg: ''
})
const rules = reactive({
  username: [{ required: true, message: '请输入登录账号', trigger: ['blur'] }],
  password: [{ required: true, message: '请输入登录密码', trigger: ['blur'] }],
  captcha: [{ required: true, message: '请输入图形验证码', trigger: ['blur'] }]
})

const formRef = ref()
const loading = ref(false)
const handleLogin = async () => {
  if (loading.value) return
  try {
    loading.value = true
    await formRef.value.validate()
    await store.loginByUsername(form)
    loading.value = false
    router.push('/home')
  } catch {
    loading.value = false
    loadCaptchaImg()
  }
}

const loadCaptchaImg = async () => {
  form.checkKey = Date.now() + Math.random().toString(36).slice(-4)
  const { result } = await getCaptchaImg(form.checkKey)
  form.captchaImg = result
}

onMounted(() => {
  loadCaptchaImg()
})
</script>

<style lang="less" scoped>
.login-page {
  background: url(@/assets/imgs/login-bg.png) center/100% 100% no-repeat;
}
:deep(.ant-form) {
  .ant-form-item-label {
    padding: 0 0 12px;
    & > label {
      font-size: 20px;
    }
  }
  .ant-input {
    line-height: 42px;
    font-size: 20px;
  }
  .ant-input-affix-wrapper {
    padding: 4px 24px;
    border-radius: 12px;
  }
  .ant-input-prefix {
    padding-right: 16px;
    margin-inline-end: 16px;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      background-color: #e6e9f0;
      width: 2px;
      height: 24px;
      border-radius: 1px;
    }
    & > i {
      font-size: 24px;
    }
  }
}
.login-btn {
  width: 100%;
  height: 52px;
  font-size: 20px;
  font-weight: bold;
}

@media screen and (max-width: 1690px) {
  :deep(.ant-form) {
    .ant-form-item-label {
      padding: 0 0 10px;
      & > label {
        font-size: 18px;
      }
    }
    .ant-input {
      line-height: 40px;
      font-size: 18px;
    }
    .ant-input-affix-wrapper {
      padding: 4px 18px;
    }
    .ant-input-prefix {
      &::after {
        height: 18px;
      }
      & > i {
        font-size: 18px;
      }
    }
  }
  .login-btn {
    height: 50px;
    font-size: 18px;
  }
}

@media screen and (max-width: 1570px) {
  :deep(.ant-form) {
    .ant-form-item-label {
      padding: 0 0 8px;
      & > label {
        font-size: 16px;
      }
    }
    .ant-input {
      line-height: 36px;
      font-size: 16px;
    }
    .ant-input-affix-wrapper {
      padding: 4px 16px;
    }
    .ant-input-prefix {
      position: relative;
      &::after {
        height: 16px;
      }
      & > i {
        font-size: 16px;
      }
    }
  }
  .login-btn {
    height: 46px;
    font-size: 16px;
  }
}

.logo {
  position: fixed;
  top: 64px;
  left: 64px;
  z-index: 99;
}
</style>
