<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div>
        <a-button type="primary" @click="handleAdd" v-auth="'biz.funds:ct_fun_refund_req_bill:add'">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'biz.funds:ct_fun_refund_req_bill:importExcel'">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport" v-auth="'biz.funds:ct_fun_refund_req_bill:exportXls'">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete">
                <div @click="handleBatchDelete">
                  <i class="a-icon-delete mr-1"></i>
                  <span class="primary-btn">批量删除</span>
                </div>
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            <i class="a-icon-more mr-1"></i>
            更多操作
            <i class="a-icon-down"></i>
          </a-button>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.number"
          placeholder="搜索编号"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
    </div>

    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      row-key="id"
      :scroll="{ x: 1200, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_RefundReqBill_Status" type="dot"></status-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)" v-auth="'biz.funds:ct_fun_refund_req_bill:view'">
            查看
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item key="submit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleSubmit(record)">提交</div>
                </a-menu-item>
                <a-menu-item key="delete" v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleDelete(record)">删除</div>
                </a-menu-item>
                <a-menu-item key="audit" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleAudit(record)">审核</div>
                </a-menu-item>
                <a-menu-item key="unAudit" v-if="record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleUnAudit(record)">反审批</div>
                </a-menu-item>
                <a-menu-item key="back" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleBack(record)">撤回</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('退款申请导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    />
    <edit-refund ref="editDrawerRef" @refresh="onTableChange" />
    <refund-detail ref="detailDrawerRef" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { hasPermission } from '@/utils/permission'
import { renderMoney } from '@/utils/render'
import {
  getRefundReqBillList,
  submitRefundReqBill,
  auditRefundReqBill,
  unAuditRefundReqBill,
  backRefundReqBill,
  deleteRefundReqBill,
  batchDeleteRefundReqBill,
  importExcel,
  exportExcel,
  queryRefundReqBillEntries
} from './apis'
import EditRefund from './components/EditRefund.vue'
import RefundDetail from './components/RefundDetail.vue'

const route = useRoute()

const commonImportRef = ref()
const editDrawerRef = ref()
const detailDrawerRef = ref()

const exportLoading = ref(false)

const searchParams = reactive({
  column: 'number',
  order: 'desc',
  number: undefined,
  customer: undefined,
  contract: undefined,
  refundReqAmount: undefined,
  bizDate: undefined,
  billSource: undefined,
  status: undefined,
  operator: undefined,
  remark: undefined,
  createTime: undefined
})

const searchList = [
  { label: '退款客户', name: 'customer', type: 'user-select' },
  { label: '退款合同', name: 'contract', type: 's-input' },
  { label: '申请退款金额', name: 'refundReqAmount', type: 's-input' },
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '来源', name: 'billSource', type: 's-input' },
  { label: '状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_RefundReqBill_Status' },
  { label: '提交人', name: 'operator', type: 'user-select' },
  { label: '备注', name: 'remark', type: 's-input' },
  { label: '创建时间', name: 'createTime', type: 'date' }
]

const columns = [
  { title: '单据编号', dataIndex: 'number', fixed: 'left', width: 200 },
  { title: '退款客户', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  { title: '退款合同', dataIndex: 'contract_dictText', width: 160, ellipsis: true },
  {
    title: '申请退款金额',
    dataIndex: 'refundReqAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  { title: '来源', dataIndex: 'billSource_dictText', width: 120 },
  { title: '状态', dataIndex: 'status', width: 100 },
  { title: '提交人', dataIndex: 'operator_dictText', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 200, ellipsis: true },
  { title: '创建时间', dataIndex: 'createTime', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', fixed: 'right', width: 140 }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getRefundReqBillList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

/**
 * 打开新建退款申请记录弹窗
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 编辑退款申请记录
 * @param {Object} record - 要编辑的记录对象
 */
const handleEdit = (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:edit')) return
  editDrawerRef.value.open(record)
}

/**
 * 查看退款申请记录详情
 * @param {Object} record - 要查看的记录对象
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 提交退款申请记录
 * @param {Object} record - 要提交的记录对象
 */
const handleSubmit = async (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:submit')) return
  const detailList = await queryRefundReqBillEntries({ id: record.id })
  record.refundReqBillEntryList = detailList.result
  Modal.confirm({
    title: '确认提交',
    content: '确认提交该退款申请记录？',
    async onOk() {
      await submitRefundReqBill({ ...record })
      message.success('提交成功')
      onTableChange()
    }
  })
}

/**
 * 审核退款申请记录
 * @param {Object} record - 要审核的记录对象
 */
const handleAudit = (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:audit')) return
  Modal.confirm({
    title: '确认审核',
    content: '确认审核该退款申请记录？',
    async onOk() {
      await auditRefundReqBill({ id: record.id })
      message.success('审核成功')
      onTableChange()
    }
  })
}

/**
 * 反审核退款申请记录
 * @param {Object} record - 要反审核的记录对象
 */
const handleUnAudit = (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: '确认反审核该退款申请记录？',
    async onOk() {
      await unAuditRefundReqBill({ id: record.id })
      message.success('反审核成功')
      onTableChange()
    }
  })
}

/**
 * 撤回退款申请记录
 * @param {Object} record - 要撤回的记录对象
 */
const handleBack = (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: '确认撤回该退款申请记录？',
    async onOk() {
      await backRefundReqBill({ id: record.id })
      message.success('撤回成功')
      onTableChange()
    }
  })
}

/**
 * 删除单条退款申请记录
 * @param {Object} record - 要删除的记录对象
 */
const handleDelete = (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: '确认删除该退款申请记录？',
    async onOk() {
      await deleteRefundReqBill({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除选中的退款申请记录
 */
const handleBatchDelete = () => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:deleteBatch')) return
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确认删除选中的 ${selectedRowKeys.value.length} 条记录？`,
    async onOk() {
      await batchDeleteRefundReqBill({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出退款申请记录清单
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('退款申请记录清单.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams, id: route.query.ids })
}

onMounted(() => {
  onTableChange()
})
</script>
