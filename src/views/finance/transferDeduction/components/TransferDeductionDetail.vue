<template>
  <a-drawer
    v-model:open="visible"
    title="转款抵扣详情"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          class="primary-btn"
          @click="handleEdit"
          v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)"
          v-auth="'biz.funds:ct_fun_transfer_deduction_req:edit'"
        >
          编辑
        </span>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="submit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)">
                <div class="primary-btn" @click="handleSubmit">提交</div>
              </a-menu-item>
              <a-menu-item key="audit" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleAudit">审核</div>
              </a-menu-item>
              <a-menu-item key="unAudit" v-if="detailData.status === 'AUDITOK'">
                <div class="primary-btn" @click="handleUnAudit">反审核</div>
              </a-menu-item>
              <a-menu-item key="back" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleBack">撤回</div>
              </a-menu-item>
              <a-menu-item key="delete" v-if="['TEMP', 'BACK'].includes(detailData.status)">
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">转款抵扣详情</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_AuditStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <div id="basic" class="mb-[40px]">
        <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">基础信息</h4>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">业务时间：{{ detailData.bizDate || '-' }}</span>
          <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
          <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
          <span class="w-[50%]">审核人：{{ detailData.auditBy_dictText || '-' }}</span>
          <span class="w-[50%]">审核时间：{{ detailData.auditTime || '-' }}</span>
          <span class="w-[50%]">
            转款抵扣金额：{{ renderMoney(detailData.transferDeductionAmount, 2, '元') || '-' }}
          </span>
          <span class="w-[100%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
      </div>
      <div id="transferItems" class="mb-[40px]">
        <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">转款明细</h4>
        <a-table
          v-if="transferList?.length"
          :columns="transferColumns"
          :data-source="transferList"
          :loading="transferLoading"
          :scroll="{ x: 1000 }"
          :pagination="false"
        />
        <div v-else class="flex flex-col items-center">
          <img src="@/assets/imgs/no-data.png" />
          <span class="text-tertiary">暂无数据</span>
        </div>
      </div>

      <div id="deductionItems" class="mb-[40px]">
        <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">抵扣欠款明细</h4>
        <a-table
          v-if="deductionList?.length"
          :columns="deductionColumns"
          :data-source="deductionList"
          :loading="deductionLoading"
          :scroll="{ x: 1000 }"
          :pagination="false"
        />
        <div v-else class="flex flex-col items-center">
          <img src="@/assets/imgs/no-data.png" />
          <span class="text-tertiary">暂无数据</span>
        </div>
      </div>
    </a-spin>
  </a-drawer>

  <edit-transfer-deduction ref="editDrawerRef" @refresh="refreshData" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import { renderMoney } from '@/utils/render'
import usePageTable from '@/hooks/usePageTable'
import {
  getTransferDeductionById,
  deleteTransferDeduction,
  submitTransferDeduction,
  auditTransferDeductionReq,
  unAuditTransferDeduction,
  backTransferDeduction,
  getTransferDeductionDetail,
  getDeductionDetail
} from '../apis'
import EditTransferDeduction from './EditTransferDeduction.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const loading = ref(false)
const detailData = ref({})
const editDrawerRef = ref()

const {
  tableLoading: transferLoading,
  list: transferList,
  onTableList: fetchTransferData
} = usePageTable(getTransferDeductionDetail)

const {
  tableLoading: deductionLoading,
  list: deductionList,
  onTableList: fetchDeductionData
} = usePageTable(getDeductionDetail)

/**
 * 转款明细表格列配置
 */
const transferColumns = [
  { title: '账单编号', dataIndex: 'detailBill_dictText', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract_dictText', width: 160, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '减免金额', dataIndex: 'remission', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '实际应收',
    dataIndex: 'actualReceiveAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已收金额', dataIndex: 'paid', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '未收金额', dataIndex: 'residual', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已转款抵扣',
    dataIndex: 'transferDeduction',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已退金额', dataIndex: 'refunded', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已处理尾差',
    dataIndex: 'offDifference',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '剩余可转',
    dataIndex: 'residueTransferAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '本次转款金额',
    dataIndex: 'thisTransferOutAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true, fixed: 'right' }
]

/**
 * 抵扣欠款明细表格列配置
 */
const deductionColumns = [
  { title: '账单编号', dataIndex: 'detailBill_dictText', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract_dictText', width: 200, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '减免金额', dataIndex: 'remission', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '实际应收',
    dataIndex: 'actualReceiveAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已收金额', dataIndex: 'paid', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '未收金额', dataIndex: 'residual', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已转款抵扣',
    dataIndex: 'transferDeduction',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已退金额', dataIndex: 'refunded', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已处理尾差',
    dataIndex: 'offDifference',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '剩余欠款',
    dataIndex: 'residueDebtAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '本次转入金额',
    dataIndex: 'thisTransferIntoAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true, fixed: 'right' }
]

/**
 * 打开详情抽屉
 * @param {Object} record - 转款抵扣记录数据
 */
const open = async (record) => {
  if (!record || !record.id) {
    message.error('缺少必要参数')
    return
  }
  visible.value = true
  await loadDetail(record.id)
}

/**
 * 关闭详情抽屉
 */
const handleCancel = () => {
  emits('refresh')
  visible.value = false
}

/**
 * 打开编辑抽屉
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 处理删除转款抵扣记录
 */
const handleDelete = () => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除单据 ${detailData.value.number} 吗？`,
    onOk: async () => {
      await deleteTransferDeduction({ id: detailData.value.id })
      message.success('删除成功')
      handleCancel()
    }
  })
}

/**
 * 处理提交转款抵扣记录
 */
const handleSubmit = async () => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:submit')) return
  const transferListData = await getTransferDeductionDetail({ id: detailData.value.id })
  const deductionListData = await getDeductionDetail({ id: detailData.value.id })
  const submitData = {
    ...detailData.value,
    transferDeductionReqTransferDetailList: transferListData.result,
    transferDeductionReqDeductionDetailList: deductionListData.result
  }
  Modal.confirm({
    title: '确认提交',
    content: `确定要提交单据 ${detailData.value.number} 吗？`,
    onOk: async () => {
      await submitTransferDeduction(submitData)
      message.success('提交成功')
      refreshData()
    }
  })
}

/**
 * 处理审核转款抵扣记录
 */
const handleAudit = () => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:audit')) return
  Modal.confirm({
    title: '确认审核',
    content: `确定要审核单据 ${detailData.value.number} 吗？`,
    onOk: async () => {
      await auditTransferDeductionReq({ id: detailData.value.id })
      message.success('审核成功')
      refreshData()
    }
  })
}

/**
 * 处理反审核转款抵扣记录
 */
const handleUnAudit = () => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: `确定要反审核单据 ${detailData.value.number} 吗？`,
    onOk: async () => {
      await unAuditTransferDeduction({ id: detailData.value.id })
      message.success('反审核成功')
      refreshData()
    }
  })
}

/**
 * 处理撤回转款抵扣记录
 */
const handleBack = () => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回单据 ${detailData.value.number} 吗？`,
    onOk: async () => {
      await backTransferDeduction({ id: detailData.value.id })
      message.success('撤回成功')
      refreshData()
    }
  })
}

/**
 * 刷新当前详情数据
 */
const refreshData = async () => {
  await loadDetail(detailData.value.id)
  emits('refresh')
}

/**
 * 加载详情数据
 * @param {string} id - 转款抵扣记录ID
 */
const loadDetail = async (id) => {
  loading.value = true

  try {
    // 加载基础信息
    const basicResult = await getTransferDeductionById({ id })
    detailData.value = basicResult.result

    // 并行加载两个表格的数据
    await Promise.all([fetchTransferData({ id }), fetchDeductionData({ id })])
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>
