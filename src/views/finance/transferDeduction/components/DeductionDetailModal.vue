<template>
  <a-modal
    v-model:open="visible"
    title="选择抵扣欠款明细"
    width="1200px"
    class="common-modal deduction-detail-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center mb-[16px]">
      <s-input
        v-model:value="params.number"
        placeholder="搜索单据编号"
        @input="handleInput"
        class="!w-[280px] mr-[16px]"
      ></s-input>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: 2000 })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <filter-more
        :params="params"
        label-width="100px"
        :search-list="searchList"
        :stat-ignore-keys="['customer']"
        :clear-ignore-keys="['customer']"
        @query="onTableChange({ pageNo: 1, pageSize: 2000 })"
      ></filter-more>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="false"
      children-column-name="detailBillEntryVOList"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        checkStrictly: false
      }"
      :scroll="{ x: 3500, y: 'calc(75vh - 120px)' }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'detailAddress'">
          {{ record.province }}{{ record.city }}{{ record.district }}{{ record.detailAddress }}
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import { f7DeductionDetailList } from '../apis'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message } from 'ant-design-vue'
import { renderDictTag, renderMoney } from '@/utils/render'

const emit = defineEmits(['updateList'])

const visible = ref(false)

/**
 * 打开抵扣欠款明细选择弹窗
 * @param {Array} list - 已选择的明细列表
 * @param {Object} paramsData - 额外的查询参数
 */
const open = (list, paramsData) => {
  selectedRowKeys.value = list.map((item) => item.id)
  selectedRows.value = list
  if (paramsData) {
    for (const key in paramsData) {
      params[key] = paramsData[key]
    }
  }
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 2000 })
}

const { list, tableLoading, onTableFetch } = usePageTable(f7DeductionDetailList, (list) => {
  list.forEach((item) => {
    // 因为要做成树形数据，所以要将外层的部分字段，设为跟子级一致的字段
    item.remission = item.sumRemission // 减免金额
    item.periodSeq = item.mergePeriodSeq // 缴交周期序号
    item.paymentType_dictText = item.mergePaymentType_dictText // 款项类型
    item.areaManager_dictText = item.mergeAreaManager_dictText // 片区管理员
    item.actualReceiveAmount = item.sumActualReceiveAmount // 实际应收金额
    item.residual = item.sumResidual // 未收金额
    item.transferdBalance = item.sumTransferdBalance // 未转应收金额
    item.paid = item.sumPaid // 已收金额
    item.transfered = item.sumTransfered // 已转应收金额
    item.paymentAmount = item.sumPaymentAmount // 应收金额
    item.receiveDate = item.mergeReceiveDate // 应收日期
    item.collectionCompany_dictText = item.mergeCollectionCompany_dictText // 租金归集公司
    item.leaseUnit = item.mergeLeaseUnit // 租赁单元
  })
  return list
})

const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id', false, true)

const params = reactive({
  number: undefined,
  customer: undefined,
  currentBillId: undefined,
  contract: undefined,
  contractNumber: undefined,
  contractType: undefined,
  billType: undefined,
  operator: undefined,
  operatorDepart: undefined,
  status: undefined,
  bizStatus: undefined,
  paymentAmount: undefined,
  receiveDate: undefined,
  bizDate: undefined
})

const searchList = [
  { label: '合同', name: 'contractNumber', type: 's-input' },
  { label: '账单类型', name: 'billType', type: 'dict-select', code: 'CT_BASE_ENUM_DetailBill_BillType' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '业务状态', name: 'bizStatus', type: 'dict-select', code: 'CT_BASE_ENUM_DetailBill_BizStatus' },
  { label: '应收金额', name: 'paymentAmount', type: 'input' },
  { label: '应收日期', name: 'receiveDate', type: 'date' },
  { label: '业务日期', name: 'bizDate', type: 'date' }
]

const columns = [
  { title: '单据编号', dataIndex: 'number', width: 240, fixed: 'left' },
  { title: '合同', dataIndex: 'contractNumber', width: 160 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus_dictText',
    width: 120
  },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 160 },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  {
    title: '账单/款项类型',
    dataIndex: 'billType',
    width: 120,
    customRender: ({ record }) => record.billType_dictText || record.paymentType_dictText
  },
  { title: '业务员', dataIndex: 'operator_dictText', width: 120 },
  { title: '业务部门', dataIndex: 'operatorDepart_dictText', width: 120 },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text) || '-', width: 120 },
  { title: '减免金额', dataIndex: 'remission', customRender: ({ text }) => renderMoney(text) || '-', width: 120 },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text) || '-', width: 120 },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text) || '-', width: 120 },
  {
    title: '实际应收金额',
    dataIndex: 'actualReceiveAmount',
    customRender: ({ text }) => renderMoney(text) || '-',
    width: 120
  },
  {
    title: '已转款抵扣',
    dataIndex: 'transferDeduction',
    customRender: ({ text }) => renderMoney(text) || '-',
    width: 120
  },
  { title: '已退款金额', dataIndex: 'refunded', customRender: ({ text }) => renderMoney(text) || '-', width: 120 },
  {
    title: '尾差已处理金额',
    dataIndex: 'offDifference',
    customRender: ({ text }) => renderMoney(text) || '-',
    width: 130
  },
  { title: '可抵退转金额', dataIndex: 'balance', customRender: ({ text }) => renderMoney(text) || '-', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '结束日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 120, customRender: ({ text }) => text || '-' },
  { title: '业务日期', dataIndex: 'bizDate', width: 120, customRender: ({ text }) => text || '-' }
]

/**
 * 表格数据变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 2000 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
/**
 * 搜索输入处理
 */
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: 2000 })
  }, 600)
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择抵扣欠款明细')
    return
  }
  emit(
    'updateList',
    selectedRows.value.filter((item) => !item.detailBillEntryVOList)
  )
  handleCancel()
}

/**
 * 取消操作
 */
const handleCancel = () => {
  list.value = []
  clearSelection()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.deduction-detail-modal {
  top: 5vh;
  .ant-modal-body {
    max-height: 75vh !important;
  }
  .ant-table-row {
    .ant-table-cell:first-child {
      border-left: 4px solid transparent;
    }
  }
  .ant-table-row.ant-table-row-level-1 {
    .ant-table-cell:first-child {
      border-left: 4px solid var(--color-primary);
    }
  }
}
</style>
