import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

// 编辑
export function editTransferDeduction(data) {
  return request({
    url: '/biz/funds/transferDeductionReq/edit',
    method: 'post',
    data
  })
}

// 审核
export function auditTransferDeductionReq(data) {
  return request({
    url: '/biz/funds/transferDeductionReq/audit',
    method: 'post',
    data
  })
}

// 反审核
export function unAuditTransferDeduction(data) {
  return request({
    url: '/biz/funds/transferDeductionReq/unAudit',
    method: 'post',
    data
  })
}

// 提交
export function submitTransferDeduction(data) {
  return request({
    url: '/biz/funds/transferDeductionReq/submit',
    method: 'post',
    data
  })
}

// 添加
export function addTransferDeduction(data) {
  return request({
    url: '/biz/funds/transferDeductionReq/add',
    method: 'post',
    data
  })
}

// 撤回
export function backTransferDeduction(data) {
  return request({
    url: '/biz/funds/transferDeductionReq/back',
    method: 'post',
    data
  })
}

// 转款明细主表ID查询
export function getTransferDeductionDetail(params) {
  return request({
    url: '/biz/funds/transferDeductionReq/queryTransferDeductionReqTransferDetailByMainId',
    method: 'get',
    params
  })
}

// 抵扣明细主表ID查询
export function getDeductionDetail(params) {
  return request({
    url: '/biz/funds/transferDeductionReq/queryTransferDeductionReqDeductionDetailByMainId',
    method: 'get',
    params
  })
}

// 通过id查询
export function getTransferDeductionById(params) {
  return request({
    url: '/biz/funds/transferDeductionReq/queryById',
    method: 'get',
    params
  })
}

// 分页列表查询
export function getTransferDeductionList(params) {
  return request({
    url: '/biz/funds/transferDeductionReq/list',
    method: 'get',
    params
  })
}

// 通过id删除
export function deleteTransferDeduction(params) {
  return request({
    url: '/biz/funds/transferDeductionReq/delete',
    method: 'delete',
    params
  })
}

// 批量删除
export function batchDeleteTransferDeduction(params) {
  return request({
    url: '/biz/funds/transferDeductionReq/deleteBatch',
    method: 'delete',
    params
  })
}

// 转款抵扣申请单-转款添加明细
export function f7TransferDetailList(params) {
  return request({
    url: '/biz/funds/transferDeductionReq/f7TransferDetailList',
    method: 'get',
    params
  })
}

// 转款抵扣申请单-抵扣欠款添加明细
export function f7DeductionDetailList(params) {
  return request({
    url: '/biz/funds/transferDeductionReq/f7DeductionDetailList',
    method: 'get',
    params
  })
}

// 导出 Excel
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/funds/transferDeductionReq/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入 Excel
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/funds/transferDeductionReq/importExcel', data, controller)
}

// 通过合同主表ID查询退租清算信息
export function getContractClearingByMainId(params) {
  return request({
    url: '/contractmanage/contract/queryContractClearingVOByMainId',
    method: 'get',
    params
  })
}
