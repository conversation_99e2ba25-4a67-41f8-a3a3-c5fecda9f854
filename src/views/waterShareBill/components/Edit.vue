<template>
  <a-drawer
    v-model:open="visible"
    class="edit-water-share-bill-drawer common-drawer"
    :title="form.id ? '编辑水电计费单' : '新建水电计费单'"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form
        :model="form"
        ref="formRef"
        :rules="rules"
        label-align="left"
        :label-col="{ style: { width: '100px' } }"
        autocomplete="off"
      >
        <h2 class="text-[16px] font-bold mb-[20px] w-full">基础信息</h2>
        <a-form-item label="项目/楼栋/楼层" name="selectProject">
          <a-cascader
            v-model:value="form.selectProject"
            :options="projectOptions"
            change-on-select
            :field-names="{ label: 'label', value: 'value', children: 'children' }"
            :load-data="loadBuildingFloorData"
            placeholder="请选择项目/楼栋/楼层"
            @change="loadBill"
          />
        </a-form-item>
        <a-form-item label="计费类型" name="feeType">
          <a-checkbox-group v-model:value="form.feeType" :options="feeTypeList"></a-checkbox-group>
        </a-form-item>
        <a-form-item label="分摊类别" name="shareType">
          <dict-select
            v-model="form.shareType"
            code="CT_BASE_ENUM_WaterShareBill_ShareType"
            @change="loadBill"
          ></dict-select>
        </a-form-item>
        <a-form-item label="应收日期" name="receiveDate">
          <a-date-picker
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="form.receiveDate"
            placeholder="请选择应收日期"
            class="w-full"
          />
        </a-form-item>
        <a-form-item label="收入归属年月" name="incomeBelongYm">
          <a-date-picker
            picker="month"
            value-format="YYYY-MM"
            format="YYYY-MM"
            v-model:value="form.incomeBelongYm"
            placeholder="请选择收入归属年月"
            class="w-full"
            @change="loadBill"
          />
        </a-form-item>
        <a-form-item label="经办人" name="operator">
          <a-form-item-rest>
            <f7-select
              v-model="form.operator"
              f7-type="user"
              :depart-id="form.operatorDepart"
              placeholder="请选择经办人"
            />
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="业务部门" name="operatorDepart">
          <depart-select v-model="form.operatorDepart" @change="onDepartChange" />
        </a-form-item>
        <a-form-item label="服务处" name="serviceCenter">
          <dict-select v-model="form.serviceCenter" code="CT_BAS_ServiceCenter"></dict-select>
        </a-form-item>
        <a-form-item label="待摊用量" name="dtUseQuantity">
          <a-input-number
            v-model:value="form.dtUseQuantity"
            :min="0"
            :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
            placeholder="请输入待摊用量"
            class="!w-full"
          />
        </a-form-item>
        <a-form-item label="单价" name="price">
          <a-input-number
            v-model:value="form.price"
            :min="0"
            addon-after="元"
            :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
            placeholder="请输入单价"
            class="!w-full"
          />
        </a-form-item>
        <a-form-item label="分摊说明" name="shareExplain" class="!w-full">
          <a-textarea
            v-model:value="form.shareExplain"
            placeholder="请输入分摊说明(选填)"
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
        <h2 class="flex items-center justify-between text-[16px] font-bold mb-[16px] w-full mt-[40px]">
          <span>分摊明细</span>
          <a-button type="primary" size="medium" :loading="updateLoading" @click="handleUpdateResult">
            更新计算结果
          </a-button>
        </h2>
        <a-table
          :data-source="form.waterShareBillEntryList"
          :columns="tableColumns"
          row-key="id"
          :scroll="{ x: 1200, y: '50vh' }"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'thisMeterReadQuantity'">
              <a-input-number
                v-model:value="record.thisMeterReadQuantity"
                :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
                class="!w-full"
                @change="() => changeComputed(record)"
              />
            </template>
            <template v-if="column.dataIndex === 'thisMeterReadDate'">
              <a-date-picker
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="record.thisMeterReadDate"
                placeholder="请选择本次抄表日期"
                class="w-full"
              />
            </template>
            <template v-if="column.dataIndex === 'selfAmount'">
              <a-input-number
                v-model:value="record.selfAmount"
                addon-after="元"
                :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
                class="!w-full"
              />
            </template>
            <template v-if="column.dataIndex === 'price'">
              <a-input-number
                v-model:value="record.price"
                addon-after="元"
                :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
                class="!w-full"
              />
            </template>
            <template v-if="column.dataIndex === 'unitShare'">
              <a-input-number
                v-model:value="record.unitShare"
                :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
                class="!w-full"
              />
            </template>
            <template v-if="column.dataIndex === 'shareAmount'">
              <a-input-number
                v-model:value="record.shareAmount"
                addon-after="元"
                :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
                class="!w-full"
              />
            </template>
            <template v-if="column.dataIndex === 'remission'">
              <a-input-number
                v-model:value="record.remission"
                addon-after="元"
                :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
                @change="computeSum(record)"
                class="!w-full"
              />
            </template>
            <template v-if="column.dataIndex === 'remark'">
              <a-input v-model:value="record.remark" placeholder="请输入备注" />
            </template>
            <template v-if="column.dataIndex === 'isCompanyExpense'">
              <a-checkbox v-model:checked="record.isCompanyExpense">是</a-checkbox>
            </template>
          </template>
        </a-table>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button type="primary" :loading="saveLoading" ghost @click="handleSave">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { projectPage, queryBuilding } from '@/views/projects/apis.js'
import { queryFloor } from '@/views/building/apis/building.js'
import { add, submit, edit, detail, waterShareBillList, waterShareBillCalc, queryWaterShareBillEntry } from '../apis.js'
import { message, Modal } from 'ant-design-vue'
import Decimal from 'decimal.js'
import { formatCurrency, formatArea } from '@/utils/number'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/modules/user'

const emit = defineEmits(['refresh'])
const store = useUserStore()

const feeTypeList = [
  { label: '自用', value: 'SelfUse' },
  { label: '公摊', value: 'ShareFee' }
]

const visible = ref(false)

const open = async (id) => {
  visible.value = true
  if (id) {
    loading.value = true
    await Promise.all([loadDetail(id), loadDetailList(id)])
    await loadProjectList()
    loading.value = false
    return
  }
  loadProjectList()
  form.receiveDate = dayjs(Date.now()).format('YYYY-MM-DD')
  form.operatorDepart = store.userInfo.currentDepart
  form.operator = store.userInfo.id
  form.feeType = ['SelfUse']
}

const loading = ref(false)
const loadDetail = async (id) => {
  const { result } = await detail({ id })
  for (const key in form) {
    if (!['waterShareBillEntryList', 'feeType'].includes(key)) {
      form[key] = result[key]
    }
  }
  form.feeType = result.feeType === 'ALL' ? ['SelfUse', 'ShareFee'] : [result.feeType]
  form.selectProject = [form.wyProject, form.wyBuilding, form.wyFloor].filter((i) => Boolean(i))
}

const loadDetailList = async (id) => {
  const { result } = await queryWaterShareBillEntry({ id })
  form.waterShareBillEntryList = result
}

/**
 * 加载项目列表数据
 */
const projectOptions = ref([])
const loadProjectList = async () => {
  const { result } = await projectPage({ status: 'ENABLE', pageSize: 1000 })
  projectOptions.value = result.records.map((item) => ({
    value: item.id,
    label: item.name,
    isLeaf: false
  }))
  if (!form.wyProject) return
  if (form.wyBuilding) {
    const project = projectOptions.value.find((item) => item.value === form.wyProject)
    if (!project) {
      form.selectProject = []
      form.wyProject = ''
      form.wyBuilding = ''
      form.wyFloor = ''
      Modal.info({
        title: '系统提示',
        content: '该水电计费单关联的项目，已经不存在',
        centered: true
      })
      return
    }
    const { result } = await queryBuilding({ id: form.wyProject })
    project.children = result.map((item) => ({
      value: item.id,
      label: item.name,
      isLeaf: false
    }))
    if (form.wyFloor) {
      const building = project.children.find((i) => i.value === form.wyBuilding)
      if (!building) {
        form.selectProject = [form.wyProject]
        form.wyBuilding = ''
        form.wyFloor = ''
        Modal.info({
          title: '系统提示',
          content: '该水电计费单关联的楼栋，已经不存在',
          centered: true
        })
        return
      }
      const { result } = await queryFloor({ id: form.wyBuilding })
      building.children = result.map((item) => ({
        value: item.id,
        label: item.name,
        isLeaf: true
      }))
    }
  }
}

/**
 * 动态加载楼栋和楼层数据
 */
const loadBuildingFloorData = async (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.loading = true

  try {
    const isLoadingBuilding = selectedOptions.length === 1
    const api = isLoadingBuilding ? queryBuilding : queryFloor
    const res = await api({ id: targetOption.value })

    if (res?.result) {
      targetOption.children = res.result.map((item) => ({
        value: item.id,
        label: item.name,
        isLeaf: !isLoadingBuilding
      }))
    }
  } finally {
    targetOption.loading = false
  }
}

/**
 * 处理项目选择变更事件
 */
const loadBill = async () => {
  const { selectProject, incomeBelongYm, shareType } = form
  const [wyProject, wyBuilding, wyFloor] = selectProject
  form.wyProject = wyProject
  form.wyBuilding = wyBuilding
  form.wyFloor = wyFloor
  if (!wyProject || !incomeBelongYm || !shareType) {
    return
  }
  // 获取分摊明细
  const { result } = await waterShareBillList(wyProject, wyBuilding, wyFloor, shareType, incomeBelongYm)
  form.waterShareBillEntryList = (result || []).map((item) => {
    item.thisMeterReadDate = dayjs().format('YYYY-MM-DD')
    return item
  })
}

const columns = [
  { title: '客户', dataIndex: 'customer_dictText', width: 160, fixed: 'left' },
  { title: '总表号', dataIndex: 'totalWaterEleTableNum_dictText', width: 200 },
  { title: '分表号', dataIndex: 'subWaterEleTableNum_dictText', width: 200 },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', width: 200 },
  { title: '地址', dataIndex: 'detailAddress', width: 160 },
  { title: '倍率', dataIndex: 'doubleRate', width: 160 },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', width: 160 },
  {
    title: '面积(㎡)',
    dataIndex: 'leaseArea',
    width: 150,
    customRender: ({ text }) => formatArea(text)
  },
  { title: '分摊系数', dataIndex: 'shareCoefficient', width: 150, customRender: ({ text }) => text && `${text}%` },
  { title: '业务员', dataIndex: 'operator', width: 150 },
  { title: '部门', dataIndex: 'operatorDepart', width: 150 },
  { title: '上次抄表数', dataIndex: 'lastMeterReadQuantity', width: 160 },
  { title: '本次抄表数', dataIndex: 'thisMeterReadQuantity', width: 160 },
  { title: '本次抄表日期', dataIndex: 'thisMeterReadDate', width: 160 },
  { title: '实际用量', dataIndex: 'actualUseQuantity', width: 150 },
  { title: '单价', dataIndex: 'price', width: 150 },
  { title: '自用金额', dataIndex: 'selfAmount', width: 180, customRender: ({ text }) => formatCurrency(text) },
  { title: '单位分摊', dataIndex: 'unitShare', width: 180, customRender: ({ text }) => formatCurrency(text) },
  { title: '公摊金额', dataIndex: 'shareAmount', width: 180, customRender: ({ text }) => formatCurrency(text) },
  { title: '减免金额', dataIndex: 'remission', width: 180, customRender: ({ text }) => formatCurrency(text) },
  { title: '合计', dataIndex: 'totalAmount', width: 180, customRender: ({ text }) => formatCurrency(text) },
  { title: '税率', dataIndex: 'taxRate', width: 130, customRender: ({ text }) => text && `${text}%` },
  { title: '税金', dataIndex: 'taxAmount', width: 160, customRender: ({ text }) => formatCurrency(text) },
  {
    title: '含税合计',
    dataIndex: 'containTaxTotalAmount',
    width: 160,
    customRender: ({ text }) => formatCurrency(text)
  },
  { title: '备注', dataIndex: 'remark', width: 160 },
  { title: '公司费用', dataIndex: 'isCompanyExpense', width: 160, customRender: ({ text }) => text && '是' }
]

const tableColumns = computed(() => {
  if (form.feeType.length === 2 || form.feeType.length === 0) return columns
  if (form.feeType[0] === 'SelfUse') {
    return columns.slice(0, 17).concat(columns.slice(19))
  }
  return columns.slice(0, 11).concat(columns.slice(17))
})

const form = reactive({
  id: '',
  number: '',
  wyProject: '',
  wyBuilding: '',
  wyFloor: '',
  selectProject: [],
  status: '',
  receiveDate: '',
  shareType: '',
  feeType: ['SelfUse'],
  operator: '',
  operatorDepart: '',
  serviceCenter: '',
  dtUseQuantity: '',
  dtUseTotalAmount: '', // 待摊总额
  price: '',
  incomeBelongYm: '',
  shareExplain: '',
  waterShareBillEntryList: []
})

const rules = {
  feeType: [{ required: true, type: 'array', message: '请选择计费类型', trigger: 'change' }],
  shareType: [{ required: true, message: '请选择分摊类型', trigger: 'change' }],
  receiveDate: [{ required: true, message: '请选择应收日期', trigger: 'change' }],
  incomeBelongYm: [{ required: true, message: '请选择收入归属年月', trigger: 'change' }],
  operatorDepart: [{ required: true, message: '请选择业务部门', trigger: 'change' }],
  operator: [{ required: true, message: '请选择经办人', trigger: 'change' }],
  serviceCenter: [{ required: true, message: '请选择服务处', trigger: 'change' }],
  dtUseQuantity: [{ required: true, message: '请输入待摊用量', trigger: 'blur' }],
  price: [{ required: true, message: '请输入单价', trigger: 'blur' }]
}

const changeComputed = (record) => {
  /**
   * lastMeterReadQuantity: 上次抄表数
   * thisMeterReadQuantity: 本次抄表数
   */
  const { lastMeterReadQuantity, thisMeterReadQuantity } = record
  record.actualUseQuantity = new Decimal(thisMeterReadQuantity || 0)
    .sub(new Decimal(lastMeterReadQuantity || 0))
    .toNumber()
}

// 减免金额改变时，触发的计算函数
const computeSum = (record) => {
  /**
   * 1. 自用金额+公摊金额-减免金额=含税合计
   * 2. 不含税合计=含税合计/(1+税率)
   * 3. 税金合计=含税合计-不含税合计
   */
  const { selfAmount, shareAmount, remission, taxRate } = record
  record.containTaxTotalAmount = new Decimal(selfAmount || 0)
    .plus(new Decimal(shareAmount || 0))
    .sub(new Decimal(remission || 0))
    .toNumber()

  record.totalAmount = new Decimal(record.containTaxTotalAmount)
    .div(new Decimal(1).plus(new Decimal(taxRate || 0)))
    .toNumber()

  record.taxAmount = new Decimal(record.containTaxTotalAmount).sub(new Decimal(record.totalAmount)).toNumber()
}

const getParams = () => {
  let dtUseTotalAmount
  if (form.dtUseQuantity && form.price) {
    dtUseTotalAmount = new Decimal(form.dtUseQuantity).times(new Decimal(form.price)).toNumber()
  }
  const feeType = form.feeType.length === 2 ? 'All' : form.feeType.length ? form.feeType[0] : ''
  return {
    ...form,
    feeType,
    wyProject: form.selectProject && form.selectProject[0],
    wyBuilding: form.selectProject && form.selectProject[1],
    wyFloor: form.selectProject && form.selectProject[2],
    dtUseTotalAmount,
    waterShareBillEntryList: form.waterShareBillEntryList.map((item) => {
      if (feeType === 'All' || feeType === '') return item
      if (feeType === 'SelfUse') {
        item.unitShare = 0
        item.shareAmount = 0
        return item
      }
      item.thisMeterReadQuantity = 0
      item.thisMeterReadDate = ''
      item.actualUseQuantity = 0
      item.price = 0
      item.selfAmount = 0
      return item
    })
  }
}

// 校验分摊明细，单价要必填
const validateList = () => {
  if (!form.waterShareBillEntryList.length) return true
  const feeType = form.feeType.length === 2 ? 'All' : form.feeType.length ? form.feeType[0] : ''
  if (feeType === 'ShareFee') return true // 计费类型为公摊时，没有单价字段
  return !form.waterShareBillEntryList.some((item, index) => {
    if (['', null, undefined].includes(item.price)) {
      message.warning(`第${index + 1}条分摊明细，单价不可为空`)
      return true
    }
    return false
  })
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  if (!validateList()) return
  try {
    confirmLoading.value = true
    await submit(getParams())
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  if (!validateList()) return
  try {
    saveLoading.value = true
    if (form.id) {
      await edit(getParams())
    } else {
      const { result } = await add(getParams())
      form.id = result.id
      form.status = result.status
      form.number = result.number
    }
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const updateLoading = ref(false)
const handleUpdateResult = async () => {
  if (updateLoading.value) return
  await formRef.value.validate()
  if (!validateList()) return
  try {
    updateLoading.value = true
    const { result } = await waterShareBillCalc(getParams())
    form.waterShareBillEntryList = result.waterShareBillEntryList
    message.success('已更新')
  } finally {
    updateLoading.value = false
  }
}

const resetForm = () => {
  for (const key in form) {
    if (key === 'feeType') {
      form[key] = ['SelfUse']
    } else if (['selectProject', 'waterShareBillEntryList'].includes(key)) {
      form[key] = []
    } else {
      form[key] = ''
    }
  }
}

const onDepartChange = () => {
  form.operator = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-water-share-bill-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 40px;
    .ant-form-item {
      width: calc(50% - 20px);
    }
  }
}
</style>
