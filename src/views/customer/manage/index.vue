<template>
  <div>
    <div class="flex justify-between items-center">
      <a-tabs v-model:active-key="activeTabKey" class="flex-1">
        <a-tab-pane key="all" tab="全部"></a-tab-pane>
        <a-tab-pane key="Intention" tab="意向"></a-tab-pane>
        <a-tab-pane key="Official" tab="正式"></a-tab-pane>
      </a-tabs>
    </div>

    <div class="flex items-center justify-between mt-[24px] mb-[16px]">
      <div class="flex">
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="addOfficial">
                <div class="primary-btn" @click="handleAddOfficial">新建正式客户</div>
              </a-menu-item>
              <a-menu-item key="addIntention">
                <div class="primary-btn" @click="handleAddIntention">新建意向客户</div>
              </a-menu-item>
            </a-menu>
          </template>
          <a-button type="primary">
            <i class="a-icon-plus mr-1"></i>
            新建
          </a-button>
        </a-dropdown>
        <a-button @click="handleImport" v-auth="'bas:ct_bas_customer:importExcel'">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport" v-auth="'bas:ct_bas_customer:exportXls'">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete">
                <div class="primary-btn" @click="handleBatchDelete">批量删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-1"></i>
          </a-button>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.name"
          placeholder="搜索名称"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
        <div class="flex items-center ml-[16px]">
          <a-checkbox v-model:checked="viewEnabled">仅看启用</a-checkbox>
        </div>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch :checked="record.status === 'ENABLE'" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'isFollow'">
          <span>{{ record.isFollow ? '是' : '否' }}</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)" v-auth="'bas:ct_bas_customer:view'">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <!-- 正式客户菜单 -->
                <template v-if="record.customerStatus === 'Official'">
                  <a-menu-item key="edit" v-if="record.status !== 'ENABLE'">
                    <div class="primary-btn" @click="handleEditOfficialCustomer(record)">编辑</div>
                  </a-menu-item>
                  <a-menu-item key="newContract">
                    <div class="primary-btn" @click="handleAddContract(record)">新建合同</div>
                  </a-menu-item>
                  <a-menu-item key="newFollowRecord">
                    <div class="primary-btn" @click="handleAddFollowRecord(record)">新建跟进记录</div>
                  </a-menu-item>
                  <a-menu-item key="viewRelatedContracts">
                    <div class="primary-btn" @click="handleViewRelatedContracts(record)">查看关联合同</div>
                  </a-menu-item>
                  <a-menu-item key="viewFollowRecords">
                    <div class="primary-btn" @click="handleViewFollowRecords(record)">查看跟进记录</div>
                  </a-menu-item>
                  <a-menu-item key="viewReceivableDetails">
                    <div class="primary-btn" @click="handleViewReceivableDetails(record)">查看应收明细</div>
                  </a-menu-item>
                  <a-menu-item key="viewPaymentRecords">
                    <div class="primary-btn" @click="handleViewPaymentRecords(record)">查看收付款记录</div>
                  </a-menu-item>
                </template>
                <!-- 意向客户菜单 -->
                <template v-if="record.customerStatus === 'Intention'">
                  <a-menu-item key="edit" v-if="record.status !== 'ENABLE'">
                    <div class="primary-btn" @click="handleEditIntentionCustomer(record)">编辑</div>
                  </a-menu-item>
                  <a-menu-item key="convertOfficial">
                    <div class="primary-btn" @click="handleConvertOfficial(record)">转正式客户</div>
                  </a-menu-item>
                  <a-menu-item key="newFollowRecord">
                    <div class="primary-btn" @click="handleAddFollowRecord(record)">新建跟进记录</div>
                  </a-menu-item>
                  <a-menu-item key="viewFollowRecords">
                    <div class="primary-btn" @click="handleViewFollowRecords(record)">查看跟进记录</div>
                  </a-menu-item>
                </template>
                <a-menu-item key="delete">
                  <div class="primary-btn" @click="handleDelete(record)">删除</div>
                </a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('客户导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
    <edit-official-customer ref="editOfficialCustomerDrawerRef" @refresh="onTableChange" />
    <edit-intention-customer ref="editIntentionCustomerDrawerRef" @refresh="onTableChange" />
    <official-customer-detail ref="officialCustomerDetailDrawerRef" @refresh="onTableChange" />
    <intention-customer-detail ref="intentionCustomerDetailDrawerRef" @refresh="onTableChange" />
    <edit-contract ref="editContractRef" @refresh="onTableChange" />
    <edit-follow-record ref="editFollowRecordRef" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getCustomerList, deleteCustomer, batchDeleteCustomer, updateStatus, exportExcel, importExcel } from './apis'
import EditIntentionCustomer from './components/EditIntentionCustomer.vue'
import EditOfficialCustomer from './components/EditOfficialCustomer.vue'
import OfficialCustomerDetail from './components/OfficialCustomerDetail.vue'
import IntentionCustomerDetail from './components/IntentionCustomerDetail.vue'
import EditContract from '@/views/contract/management/components/EditContract.vue'
import EditFollowRecord from '@/views/customer/followRecord/components/EditFollowRecord.vue'

const router = useRouter()

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getCustomerList, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const activeTabKey = ref('all')
const columnSetRef = ref()
const editOfficialCustomerDrawerRef = ref()
const editIntentionCustomerDrawerRef = ref()
const officialCustomerDetailDrawerRef = ref()
const intentionCustomerDetailDrawerRef = ref()
const editContractRef = ref()
const editFollowRecordRef = ref()
const exportLoading = ref(false)
const commonImportRef = ref()
const viewEnabled = ref(false)

const searchParams = reactive({
  column: 'createTime',
  order: 'desc',
  name: undefined,
  status: undefined,
  customerStatus: undefined,
  customerType: undefined,
  linkmanPhone: undefined,
  customerSource: undefined,
  manageCompany: undefined,
  initRequire: undefined,
  maintainPerson: undefined,
  maintainDate: undefined,
  isFollow: undefined
})

const searchList = [
  {
    label: '客户类型',
    name: 'customerType',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Customer_CustomerType',
    placeholder: '请选择客户类型'
  },
  { label: '联系电话', name: 'linkmanPhone', type: 's-input', placeholder: '请输入联系电话' },
  {
    label: '客户来源',
    name: 'customerSource',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Customer_CustomerSource',
    placeholder: '请选择客户来源'
  },
  { label: '管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择管理公司' },
  {
    label: '客户状态',
    name: 'customerStatus',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Customer_CustomerStatus',
    placeholder: '请选择客户状态'
  },
  {
    label: '启用状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_BaseStatus',
    placeholder: '请选择启用状态'
  },
  { label: '初步需求', name: 'initRequire', type: 's-input', placeholder: '请输入初步需求' },
  { label: '维护人', name: 'maintainPerson', type: 'user-select', placeholder: '请选择维护人' },
  { label: '维护时间', name: 'maintainDate', type: 'date', placeholder: '请选择维护时间' }
]

const defaultColumns = [
  { title: '客户名称', dataIndex: 'name', width: 200, ellipsis: true, fixed: 'left' },
  { title: '客户类型', dataIndex: 'customerType_dictText', width: 120 },
  { title: '联系电话', dataIndex: 'linkmanPhone', width: 120 },
  { title: '客户来源', dataIndex: 'customerSource_dictText', width: 120 },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '客户状态', dataIndex: 'customerStatus_dictText', width: 120 },
  { title: '启用状态', dataIndex: 'status', width: 120 },
  { title: '初步需求', dataIndex: 'initRequire', width: 160, ellipsis: true },
  { title: '维护人', dataIndex: 'maintainPerson_dictText', width: 120 },
  { title: '维护时间', dataIndex: 'maintainDate', width: 120 },
  { title: '是否跟进', dataIndex: 'isFollow', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

/**
 * 新增正式客户
 */
const handleAddOfficial = () => {
  if (!hasPermission('bas:ct_bas_customer:add')) return
  editOfficialCustomerDrawerRef.value.open()
}

/**
 * 新增意向客户
 */
const handleAddIntention = () => {
  if (!hasPermission('bas:ct_bas_customer:add')) return
  editIntentionCustomerDrawerRef.value.open()
}

/**
 * 新建合同
 * @param {Object} record - 客户记录
 */
const handleAddContract = (record) => {
  editContractRef.value.open(null, 'edit', { customer: record.id })
}

/**
 * 新建跟进记录
 * @param {Object} record - 客户记录
 */
const handleAddFollowRecord = (record) => {
  if (!hasPermission('bas:ct_bas_follow_record:add')) return
  editFollowRecordRef.value.open({ followCustomer: record.id })
}

/**
 * 编辑客户
 * @param {Object} record - 客户行数据
 */
const handleEditOfficialCustomer = (record) => {
  if (!hasPermission('bas:ct_bas_customer:edit')) return
  editOfficialCustomerDrawerRef.value.open(record)
}
const handleEditIntentionCustomer = (record) => {
  if (!hasPermission('bas:ct_bas_customer:edit')) return
  editIntentionCustomerDrawerRef.value.open(record)
}

/**
 * 查看客户详情
 * @param {Object} record - 客户记录
 */
const handleDetail = (record) => {
  // 根据客户状态选择对应的详情组件
  if (record.customerStatus === 'Intention') {
    // 意向客户
    intentionCustomerDetailDrawerRef.value.open(record)
  } else {
    // 正式客户
    officialCustomerDetailDrawerRef.value.open(record)
  }
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出客户数据
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('客户清单.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

/**
 * 更新客户状态
 * @param {Object} record - 客户数据
 */
const handleStatusChange = async (data, val) => {
  if (data.loading) return
  try {
    data.loading = true
    await updateStatus({ ids: data.id, status: val ? 'ENABLE' : 'DISABLE' })
    data.loading = false
    message.success('保存成功')
    data.status = val ? 'ENABLE' : 'DISABLE'
  } catch {
    data.loading = false
    data.checked = !val
  }
}

/**
 * 删除单个客户
 * @param {Object} record - 客户记录
 */
const handleDelete = (record) => {
  if (!hasPermission('bas:ct_bas_customer:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除客户"${record.name || record.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteCustomer({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除客户
 */
const handleBatchDelete = () => {
  if (!hasPermission('bas:ct_bas_customer:deleteBatch')) return
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个客户吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteCustomer({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 转为正式客户
 * @param {Object} record - 客户记录
 */
const handleConvertOfficial = (record) => {
  editOfficialCustomerDrawerRef.value.open(record)
}

/**
 * 表格变化处理
 * @param {Object} params - 分页参数
 */
const onTableChange = ({ current = pagination.value.current, pageNo, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: pageNo ?? current, pageSize, ...searchParams })
}

/**
 * 搜索输入处理（防抖）
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

watch(viewEnabled, (val) => {
  searchParams.status = val ? 'ENABLE' : undefined
  onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
})

watch(activeTabKey, (newVal) => {
  switch (newVal) {
    case 'Intention':
      searchParams.customerStatus = 'Intention'
      break
    case 'Official':
      searchParams.customerStatus = 'Official'
      break
    default:
      searchParams.customerStatus = undefined
  }
  onTableChange()
})

onMounted(() => {
  onTableChange()
})

/**
 * 查看关联合同
 * @param {Object} record - 客户记录
 */
const handleViewRelatedContracts = (record) => {
  router.push({
    path: '/contract/management',
    query: { customer: record.id }
  })
}

/**
 * 查看跟进记录
 * @param {Object} record - 客户记录
 */
const handleViewFollowRecords = (record) => {
  router.push({
    path: '/customer/followRecord',
    query: { followCustomer: record.id }
  })
}

/**
 * 查看应收明细
 * @param {Object} record - 客户记录
 */
const handleViewReceivableDetails = (record) => {
  router.push({
    path: '/statement/receiveCertificate',
    query: { customer: record.id }
  })
}

/**
 * 查看收付款记录
 * @param {Object} record - 客户记录
 */
const handleViewPaymentRecords = (record) => {
  router.push({
    path: '/receivePayRecords',
    query: { customer: record.id }
  })
}
</script>
