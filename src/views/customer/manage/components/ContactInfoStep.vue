<template>
  <div>
    <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">联系信息</h4>
    <a-form ref="contactFormRef" :model="formData" :rules="contactRules" :label-col="{ style: { width: '140px' } }">
      <a-form-item label="联系人" name="linkman">
        <a-input v-model:value="formData.linkman" placeholder="请输入联系人" />
      </a-form-item>
      <a-form-item label="联系地址" name="linkPcaCode">
        <div class="flex">
          <a-cascader
            class="!mr-[4px]"
            ref="cascaderRef"
            v-model:value="formData.linkPcaCodeArray"
            :options="areaList"
            placeholder="请选择区域"
          />

          <a-input
            v-model:value="formData.linkDetailAddress"
            placeholder="请输入详细地址"
            allow-clear
            :maxlength="64"
          ></a-input>
        </div>
      </a-form-item>
      <a-form-item label="联系电话" name="linkmanPhone">
        <a-input v-model:value="formData.linkmanPhone" placeholder="请输入联系电话" />
      </a-form-item>
      <a-form-item label="推送手机" name="pushMobile">
        <a-input v-model:value="formData.pushMobile" placeholder="请输入推送手机" />
      </a-form-item>
      <a-form-item label="邮箱" name="email">
        <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import areaList from '@/json/region.json'
const { formData } = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const contactFormRef = ref()

const contactRules = {
  linkmanPhone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  pushMobile: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }]
}

/**
 * 验证联系信息表单
 */
const validate = async () => {
  await contactFormRef.value?.validate()
}

/**
 * 重置表单
 */
const resetFields = () => {
  contactFormRef.value?.resetFields()
}

defineExpose({
  validate,
  resetFields
})
</script>
