<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="客户详情"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra v-if="!readonly">
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span class="primary-btn" @click="handleConvert">转正式客户</span>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="edit" v-if="detailData.status !== 'ENABLE'">
                <div class="primary-btn" @click="handleEdit">编辑</div>
              </a-menu-item>
              <a-menu-item key="delete">
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">客户详情: {{ detailData.name }}</h2>
        <status-tag
          :dict-value="detailData.customerStatus"
          dict-code="CT_BASE_ENUM_Customer_CustomerStatus"
        ></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #basic>
          <div class="flex flex-col gap-y-[40px]">
            <!-- 客户基础信息 -->
            <div>
              <div class="flex flex-wrap gap-y-[12px] text-secondary">
                <span class="w-[50%]">客户名称：{{ detailData.name || '-' }}</span>
                <span class="w-[50%]">管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
                <span class="w-[50%]">客户来源：{{ detailData.customerSource_dictText || '-' }}</span>
                <span class="w-[50%]">客户类型：{{ detailData.customerType_dictText || '-' }}</span>
                <span class="w-[50%]">联系电话：{{ detailData.linkmanPhone || '-' }}</span>
              </div>
            </div>
          </div>
        </template>

        <template #demand>
          <div>
            <div class="flex flex-wrap gap-y-[12px] text-secondary">
              <span class="w-[50%]">维护日期：{{ detailData.maintainDate || '-' }}</span>
              <span class="w-[50%]">维护人员：{{ detailData.maintainPerson_dictText || '-' }}</span>
              <span class="w-[100%] break-words whitespace-pre-wrap">
                初步需求：{{ detailData.initRequire || '-' }}
              </span>
            </div>
          </div>
        </template>

        <template #followRecord-title>
          <div class="flex items-center justify-between mb-[12px]">
            <h4 class="text-[16px] font-bold">跟进记录</h4>
            <a-button
              type="primary"
              @click="handleAddFollowRecord"
              v-if="!readonly"
              v-auth="'bas:ct_bas_follow_record:add'"
            >
              添加记录
            </a-button>
          </div>
        </template>
        <template #followRecord>
          <a-table
            v-if="list.length > 0"
            :data-source="list"
            :columns="followRecordColumns"
            :loading="tableLoading"
            :pagination="pagination"
            row-key="id"
            :scroll="{ x: 1200 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'followContent'">
                <span>{{ record.layoutPreference }}/{{ record.supportFacilityRequire }}/{{ record.otherContent }}</span>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <span
                  class="primary-btn"
                  @click="handleEditFollowRecord(record)"
                  v-auth="'bas:ct_bas_follow_record:edit'"
                >
                  编辑
                </span>
                <span
                  class="primary-btn"
                  @click="handleDeleteFollowRecord(record)"
                  v-auth="'bas:ct_bas_follow_record:delete'"
                >
                  删除
                </span>
              </template>
            </template>
          </a-table>
          <div v-else class="flex flex-col items-center py-[40px]">
            <img src="@/assets/imgs/no-data.png" class="w-[80px] h-[80px]" />
            <span class="text-tertiary mt-[8px]">暂无数据</span>
          </div>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>

  <edit-intention-customer ref="editDrawerRef" @refresh="refreshData" />
  <edit-official-customer ref="editOfficialDrawerRef" @refresh="refreshData" />
  <edit-follow-record ref="editFollowRecordModalRef" @refresh="onTableChange" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import { hasPermission } from '@/utils/permission'
import { getFollowRecordList, deleteFollowRecordById } from '@/views/customer/followRecord/apis'
import { queryCustomerById, deleteCustomer } from '../apis'
import EditFollowRecord from '@/views/customer/followRecord/components/EditFollowRecord.vue'
import EditIntentionCustomer from './EditIntentionCustomer.vue'
import EditOfficialCustomer from './EditOfficialCustomer.vue'
import { renderMoney } from '@/utils/render'
defineProps({
  readonly: { type: Boolean, default: false }
})

const emits = defineEmits(['refresh'])

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getFollowRecordList)

const visible = ref(false)
const loading = ref(false)
const editFollowRecordModalRef = ref()
const editDrawerRef = ref()
const editOfficialDrawerRef = ref()
const detailData = ref({})

const searchParams = reactive({
  followCustomer: undefined
})

const tabList = [
  { title: '基础信息', name: 'basic' },
  { title: '客户需求', name: 'demand' },
  { title: '跟进记录', name: 'followRecord' }
]

const followRecordColumns = [
  { title: '登记时间', dataIndex: 'followTime', width: 200, fixed: 'left' },
  { title: '跟进客户', dataIndex: 'followCustomer_dictText', width: 120, ellipsis: true },
  { title: '跟进人', dataIndex: 'followPerson_dictText', width: 120 },
  { title: '跟进方式', dataIndex: 'followMethod_dictText', width: 120 },
  { title: '参观租赁单元', dataIndex: 'visitLeaseUnit_dictText', width: 160, ellipsis: true },
  { title: '意向面积(㎡)', dataIndex: 'intendArea', width: 130, customRender: ({ text }) => renderMoney(text, 4) },
  { title: '入住意向时间', dataIndex: 'moveIntendTime', width: 120 },
  { title: '布局偏好', dataIndex: 'layoutPreference', width: 160, ellipsis: true },
  { title: '配套设施需求', dataIndex: 'supportFacilityRequire', width: 160, ellipsis: true },
  { title: '其他内容', dataIndex: 'otherContent', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

/**
 * 打开详情抽屉
 */
const open = (record) => {
  if (!record || !record.id) {
    message.error('缺少必要参数')
    return
  }
  visible.value = true
  initDetailData(record.id)
}
// 获取详情页面的所有数据
const initDetailData = (id) => {
  loadDetail(id)
  searchParams.followCustomer = id
  onTableChange()
}

/**
 * 关闭抽屉
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
}

/**
 * 编辑客户信息
 */
const handleEdit = () => {
  if (!hasPermission('bas:ct_bas_customer:edit')) return
  editDrawerRef.value.open(detailData.value)
}

/**
 * 转为正式客户
 */
const handleConvert = () => {
  editOfficialDrawerRef.value.open(detailData.value)
}

/**
 * 刷新详情数据
 */
const refreshData = () => {
  initDetailData(detailData.value.id)
  emits('refresh')
}

/**
 * 添加跟进记录
 */
const handleAddFollowRecord = () => {
  editFollowRecordModalRef.value.open({ followCustomer: detailData.value.id })
}

/**
 * 编辑跟进记录
 * @param {Object} record - 跟进记录数据
 */
const handleEditFollowRecord = (record) => {
  editFollowRecordModalRef.value.open(record)
}

/**
 * 删除跟进记录
 * @param {Object} record - 跟进记录数据
 */
const handleDeleteFollowRecord = (record) => {
  if (!hasPermission('bas:ct_bas_customer:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该跟进记录吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      await deleteFollowRecordById({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 获取客户详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const res = await queryCustomerById({ id })
    detailData.value = res.result || {}
  } finally {
    loading.value = false
  }
}

/**
 * 跟进记录表格变化处理
 */
const onTableChange = ({ pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo, pageSize, ...searchParams })
}

/**
 * 删除客户
 */
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除客户"${detailData.value.name || detailData.value.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteCustomer({ id: detailData.value.id })
      message.success('删除成功')
      visible.value = false
      emits('refresh')
    }
  })
}

defineExpose({
  open
})
</script>
