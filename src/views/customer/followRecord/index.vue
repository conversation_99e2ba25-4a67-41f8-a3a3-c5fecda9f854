<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div>
        <a-button type="primary" @click="handleAdd" v-auth="'bas:ct_bas_follow_record:add'">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'bas:ct_bas_follow_record:importExcel'">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport" v-auth="'bas:ct_bas_follow_record:exportXls'">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete">
                <div class="primary-btn" @click="handleBatchDelete">批量删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-1"></i>
          </a-button>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'intendArea' && record.intendArea">
          {{ renderMoney(record.intendArea, 4, 'm²') }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)" v-auth="'bas:ct_bas_follow_record:edit'">编辑</span>
          <span class="primary-btn" @click="handleDelete(record)" v-auth="'bas:ct_bas_follow_record:delete'">删除</span>
        </template>
      </template>
    </a-table>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('客户跟进记录导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
    <edit-follow-record ref="editModalRef" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { Modal, message } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { getFollowRecordList, deleteFollowRecordById, deleteFollowRecordBatch, importExcel, exportExcel } from './apis'
import EditFollowRecord from './components/EditFollowRecord.vue'
import { renderMoney } from '@/utils/render'

const route = useRoute()
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getFollowRecordList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editModalRef = ref()
const commonImportRef = ref()
const exportLoading = ref(false)

const searchParams = reactive({
  column: 'createTime',
  order: 'desc',
  followTime: undefined,
  followCustomer: undefined,
  followPerson: undefined,
  followMethod: undefined,
  visitLeaseUnit: undefined,
  intendArea: undefined,
  moveIntendTime: undefined,
  layoutPreference: undefined,
  supportFacilityRequire: undefined
})

const searchList = [
  { label: '登记时间', name: 'followTime', type: 'date' },
  { label: '跟进客户', name: 'followCustomer', type: 'customer-select' },
  { label: '跟进人', name: 'followPerson', type: 'user-select' },
  { label: '跟进方式', name: 'followMethod', type: 'dict-select', code: 'CT_BASE_ENUM_FollowRecord_FollowMethod' },
  { label: '参观租赁单元', name: 'visitLeaseUnit', type: 'api-select', asyncFn: getLeaseUnitList },
  { label: '意向面积', name: 'intendArea', type: 'input' },
  { label: '入住意向时间', name: 'moveIntendTime', type: 'date' },
  { label: '布局偏好', name: 'layoutPreference', type: 's-input' },
  { label: '配套设施需求', name: 'supportFacilityRequire', type: 's-input' },
  { label: '其他内容', name: 'otherContent', type: 's-input' }
]

const columns = [
  { title: '登记时间', dataIndex: 'followTime', width: 200, fixed: 'left' },
  { title: '跟进客户', dataIndex: 'followCustomer_dictText', width: 120, ellipsis: true },
  { title: '跟进人', dataIndex: 'followPerson_dictText', width: 120 },
  { title: '跟进方式', dataIndex: 'followMethod_dictText', width: 120 },
  { title: '参观租赁单元', dataIndex: 'visitLeaseUnit_dictText', width: 160, ellipsis: true },
  { title: '意向面积', dataIndex: 'intendArea', width: 120 },
  { title: '入住意向时间', dataIndex: 'moveIntendTime', width: 120 },
  { title: '布局偏好', dataIndex: 'layoutPreference', width: 160, ellipsis: true },
  { title: '配套设施需求', dataIndex: 'supportFacilityRequire', width: 160, ellipsis: true },
  { title: '其他内容', dataIndex: 'otherContent', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

/**
 * 打开新建跟进记录弹窗
 */
const handleAdd = () => {
  editModalRef.value.open()
}

/**
 * 打开编辑跟进记录弹窗
 * @param {Object} record - 当前行数据
 */
const handleEdit = (record) => {
  editModalRef.value.open(record)
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 删除单条跟进记录
 * @param {Object} record - 当前行数据
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该跟进记录吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      await deleteFollowRecordById({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除跟进记录
 */
const handleBatchDelete = () => {
  if (!hasPermission('bas:ct_bas_follow_record:deleteBatch')) return
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteFollowRecordBatch({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 导出跟进记录数据
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('客户跟进记录清单.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

/**
 * 表格变化回调（分页、排序、筛选）
 * @param {Object} params - 分页参数
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

onMounted(() => {
  // 从路由查询参数中读取客户信息
  if (route.query.followCustomer) {
    searchParams.followCustomer = route.query.followCustomer
  }
  onTableChange()
})
</script>
