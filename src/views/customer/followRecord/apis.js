import request from '@/apis/http'

// 获取客户跟进记录列表
export const getFollowRecordList = (params) => {
  return request({
    method: 'get',
    url: '/bas/followRecord/list',
    params
  })
}

// 通过 id 查询
export const getFollowRecordById = (params) => {
  return request({
    method: 'get',
    url: '/bas/followRecord/queryById',
    params
  })
}

// 添加
export const addFollowRecord = (data) => {
  return request({
    method: 'post',
    url: '/bas/followRecord/add',
    data
  })
}

// 编辑
export const editFollowRecord = (data) => {
  return request({
    method: 'post',
    url: '/bas/followRecord/edit',
    data
  })
}

// 导入 excel
export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/followRecord/importExcel',
    data
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/followRecord/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 通过 id 删除
export const deleteFollowRecordById = (params) => {
  return request({
    method: 'delete',
    url: '/bas/followRecord/delete',
    params
  })
}

// 批量删除
export const deleteFollowRecordBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/followRecord/deleteBatch',
    params
  })
}
