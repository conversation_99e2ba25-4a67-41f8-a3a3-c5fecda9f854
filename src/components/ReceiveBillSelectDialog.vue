<!-- 应收单选择弹窗 -->
<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="70%"
    title="选择应收单"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="mb-[12px]">
      <a-form autocomplete="off" layout="inline">
        <a-form-item label="名称">
          <s-input
            class="!w-[280px]"
            v-model:value="search.name"
            placeholder="请输入名称"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
      </a-form>
    </div>
    <a-table
      ref="tableRef"
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: 600, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        selectedRows,
        type: multiple ? 'checkbox' : 'radio',
        onChange: onSelectChange
      }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>
<script setup>
import { getPage } from '@/views/statement/receiveCertificate/apis.js'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message } from 'ant-design-vue'
import { renderDict, renderBoolean } from '@/utils/render'
const { multiple, asyncFunc } = defineProps({
  multiple: { default: false, type: Boolean },
  asyncFunc: { default: getPage, type: Function },
  // 已选择的ids
  ids: { default: () => [], type: Array }
})
const visible = ref(false)
const open = (selectedList = []) => {
  onTableChange()
  if (selectedList && selectedList.length > 0) {
    const selectedIds = selectedList.map((item) => item.value)
    selectedRowKeys.value = selectedIds
    // 根据 id 从 list 中获取完整的对象数据
    selectedRows.value = list.value.filter((item) => selectedIds.includes(item.id))
  }
  visible.value = true
}
defineExpose({ open })
const search = ref({
  column: 'number',
  order: 'desc',
  name: ''
})

const searchFilter = ref({})
const searchList = reactive([
  { label: '单据编号', name: 'number', type: 'input', placeholder: '请输入单据编号' },
  {
    label: '单据状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择单据状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  { label: '物业管理公司', name: 'manageCompany', type: 'companySelect', placeholder: '请选择物业管理公司' },
  { label: '租金归集公司', name: 'collectionCompany', type: 'companySelect', placeholder: '请选择租金归集公司' },
  {
    label: '客户',
    name: 'customer',
    type: 'customerSelect',
    placeholder: '请选择客户'
  },
  {
    label: '含税',
    name: 'containTax',
    type: 'radio',
    placeholder: '请选择含税',
    list: [
      { label: '否', value: false },
      { label: '是', value: true }
    ]
  },
  {
    label: '固定单据',
    name: 'fixBill',
    type: 'radio',
    placeholder: '请选择固定单据',
    list: [
      { label: '否', value: false },
      { label: '是', value: true }
    ]
  },
  {
    label: '应收冲销',
    name: 'isRecConsumed',
    type: 'radio',
    placeholder: '请选择应收冲销',
    list: [
      { label: '否', value: false },
      { label: '是', value: true }
    ]
  },
  { label: '应收日期', name: 'receiveDate', type: 'date', placeholder: '请选择应收日期' },
  { label: '金额', name: 'amount', type: 'number', placeholder: '请输入金额' },
  { label: '含税金额', name: 'containTaxAmount', type: 'number', placeholder: '请输入含税金额' },
  {
    label: '单据来源',
    name: 'billSource',
    type: 'dic',
    placeholder: '请选择单据状态',
    code: 'CT_BASE_ENUM_ReceiveBill_BillSource'
  },
  {
    label: '经办人',
    name: 'operator',
    type: 'userSelect',
    placeholder: '请选择经办人'
  },
  { label: '经办部门', name: 'operatorDepart', type: 'departSelect', placeholder: '请选择经办部门' }
])
const columns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: true },
  { title: '单据状态', dataIndex: 'status_dictText' },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
  { title: '客户', dataIndex: 'customer_dictText' },
  { title: '含税', dataIndex: 'containTax', customRender: ({ text }) => renderBoolean(text) },
  { title: '固定单据', dataIndex: 'fixBill', customRender: ({ text }) => renderBoolean(text) },
  { title: '应收冲销', dataIndex: 'isRecConsumed', customRender: ({ text }) => renderBoolean(text) },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '金额', dataIndex: 'amount' },
  { title: '含税金额', dataIndex: 'containTaxAmount' },
  {
    title: '单据来源',
    dataIndex: 'billSource',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_ReceiveBill_BillSource')
  },
  { title: '经办人', dataIndex: 'operator_dictText' },
  { title: '经办部门', dataIndex: 'operatorDepart_dictText', ellipsis: true },
  { title: '备注', dataIndex: 'remark' }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(asyncFunc)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({
    pageNo: current ?? pageNo,
    pageSize,
    ...search.value,
    ...searchFilter.value
  })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

const emits = defineEmits(['selectChange'])
const handleOk = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emits('selectChange', selectedRows.value)
  visible.value = false
}
/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}
</script>
