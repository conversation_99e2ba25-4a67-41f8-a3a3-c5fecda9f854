import { getPage } from '@/views/statement/receiveCertificate/apis'
import { renderBoolean, renderDict, renderDictTag } from '@/utils/render'

export default {
  modalTitle: '选择应收单',
  request: getPage,
  params: {
    column: 'number',
    order: 'desc',
    status: 'AUDITOK',
    manageCompany: undefined,
    collectionCompany: undefined,
    customer: undefined,
    containTax: undefined,
    fixBill: undefined,
    isRecConsumed: undefined,
    receiveDate: undefined,
    amount: undefined,
    containTaxAmount: undefined,
    billSource: undefined,
    operator: undefined,
    operatorDepart: undefined
  },
  rowKey: 'id',
  displayKey: 'number',
  keywordKey: 'number',
  keywordPlaceholder: '搜索单据编号',
  scrollX: 2500,
  clearIgnoreKeys: ['status'],
  statIgnoreKeys: ['status'],
  columns: [
    { title: '单据编号', dataIndex: 'number', width: 180, fixed: true },
    {
      title: '单据状态',
      dataIndex: 'status',
      customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
    },
    { title: '物业管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
    { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
    { title: '客户', dataIndex: 'customer_dictText' },
    { title: '含税', dataIndex: 'containTax', customRender: ({ text }) => renderBoolean(text) },
    { title: '固定单据', dataIndex: 'fixBill', customRender: ({ text }) => renderBoolean(text) },
    { title: '应收冲销', dataIndex: 'isRecConsumed', customRender: ({ text }) => renderBoolean(text) },
    { title: '应收日期', dataIndex: 'receiveDate' },
    { title: '金额', dataIndex: 'amount' },
    { title: '含税金额', dataIndex: 'containTaxAmount' },
    {
      title: '单据来源',
      dataIndex: 'billSource',
      customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_ReceiveBill_BillSource')
    },
    { title: '经办人', dataIndex: 'operator_dictText' },
    { title: '经办部门', dataIndex: 'operatorDepart_dictText', ellipsis: true },
    { title: '备注', dataIndex: 'remark' }
  ],
  searchList: [
    { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
    { label: '租金归集公司', name: 'collectionCompany', type: 'company-select' },
    { label: '客户', name: 'customer', type: 'customer-select' },
    {
      label: '含税',
      name: 'containTax',
      type: 'a-select',
      options: [
        { label: '否', value: false },
        { label: '是', value: true }
      ]
    },
    {
      label: '固定单据',
      name: 'fixBill',
      type: 'a-select',
      options: [
        { label: '否', value: false },
        { label: '是', value: true }
      ]
    },
    {
      label: '应收冲销',
      name: 'isRecConsumed',
      type: 'a-select',
      options: [
        { label: '否', value: false },
        { label: '是', value: true }
      ]
    },
    { label: '应收日期', name: 'receiveDate', type: 'date' },
    { label: '金额', name: 'amount', type: 'input' },
    { label: '含税金额', name: 'containTaxAmount', type: 'input' },
    {
      label: '单据来源',
      name: 'billSource',
      type: 'dict-select',
      code: 'CT_BASE_ENUM_ReceiveBill_BillSource'
    },
    { label: '经办人', name: 'operator', type: 'user-select' },
    { label: '经办部门', name: 'operatorDepart', type: 'depart-select' }
  ]
}
