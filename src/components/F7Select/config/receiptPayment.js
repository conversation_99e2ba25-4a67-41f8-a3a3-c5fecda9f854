import { getPage } from '@/views/receivePayRecords/apis'
import { renderDict, renderDictTag } from '@/utils/render'

export default {
  modalTitle: '选择收付款记录',
  request: getPage,
  params: {
    column: 'number',
    order: 'desc',
    number: undefined,
    customer: undefined,
    manageCompany: undefined,
    landNature: undefined,
    actualReceiveAmt: undefined,
    serviceCharge: undefined,
    sumAmt: undefined,
    status: undefined,
    bizStatus: undefined,
    billSource: undefined,
    operator: undefined,
    receiveDate: undefined
  },
  rowKey: 'id',
  displayKey: 'number',
  keywordKey: 'number',
  keywordPlaceholder: '搜索单据编号',
  scrollX: 2000,
  columns: [
    { title: '单据编号', dataIndex: 'number', width: 200, fixed: true },
    { title: '客户名称', dataIndex: 'customer_dictText' },
    { title: '收付款公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
    {
      title: '单据类型',
      dataIndex: 'landNature',
      width: 120,
      customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
    },
    { title: '收付金额', dataIndex: 'actualReceiveAmt' },
    { title: '手续费', dataIndex: 'serviceCharge' },
    { title: '合计金额', dataIndex: 'sumAmt' },
    {
      title: '数据状态',
      dataIndex: 'status',
      customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
    },
    {
      title: '核销情况',
      dataIndex: 'bizStatus',
      customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_PayExplainBook_BizStatus')
    },
    {
      title: '来源',
      dataIndex: 'billSource',
      customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_PayExplainBook_BillSource')
    },
    {
      title: '经办人',
      dataIndex: 'operator_dictText'
    },
    { title: '收款日期', dataIndex: 'receiveDate' }
  ],
  searchList: [
    { label: '客户', name: 'customer', type: 'customer-select' },
    { label: '收付款公司', name: 'manageCompany', type: 'company-select' },
    { label: '单据类型', name: 'landNature', type: 'dict-select', code: 'CT_BAS_LandNature' },
    { label: '收付金额', name: 'actualReceiveAmt', type: 'input' },
    { label: '手续费', name: 'serviceCharge', type: 'input' },
    { label: '合计金额', name: 'sumAmt', type: 'input' },
    { label: '数据状态', name: 'status', type: 'dict', code: 'CT_BASE_ENUM_AuditStatus' },
    { label: '核销情况', name: 'bizStatus', type: 'dict', code: 'CT_BASE_ENUM_PayExplainBook_BizStatus' },
    { label: '来源', name: 'billSource', type: 'dict', code: 'CT_BASE_ENUM_PayExplainBook_BillSource' },
    { label: '经办人', name: 'operator', type: 'user-select' },
    { label: '收款日期', name: 'receiveDate', type: 'date' }
  ]
}
