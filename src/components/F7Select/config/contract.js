import { f7List } from '@/views/contract/management/apis'
import { renderDictTag } from '@/utils/render'

export default {
  modalTitle: '选择合同',
  request: f7List,
  params: {
    column: 'number',
    order: 'desc',
    number: undefined,
    contractNumber: undefined,
    status: 'AUDITOK',
    bizStatus: undefined,
    bizDate: undefined,
    signDate: undefined,
    customer: undefined,
    contractType: undefined,
    manageCompany: undefined,
    operator: undefined,
    operatorDepart: undefined,
    pricedType: undefined,
    startDate: undefined,
    expireDate: undefined,
    terminateDate: undefined,
    terminateReason: undefined,
    changeReason: undefined,
    originalContract: undefined,
    totalArea: undefined,
    totalRental: undefined,
    totalRemission: undefined,
    remark: undefined,
    createBy: undefined,
    createTime: undefined,
    auditBy: undefined
  },
  rowKey: 'id',
  displayKey: 'contractNumber',
  keywordKey: 'contractNumber',
  keywordPlaceholder: '搜索合同编号',
  clearIgnoreKeys: ['status'],
  statIgnoreKeys: ['status'],
  filterWidth: '320px',
  filterLabelWidth: '100px',
  columns: [
    { title: '合同编号', dataIndex: 'contractNumber', width: 200, fixed: 'left' },
    { title: '客户', dataIndex: 'customer_dictText', width: 120 },
    {
      title: '业务状态',
      dataIndex: 'bizStatus',
      width: 100,
      customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_Contract_BizStatus', 'dot')
    },
    { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160 },
    { title: '业务日期', dataIndex: 'remark', width: 100 },
    { title: '签约日期', dataIndex: 'signDate', width: 120 },
    { title: '业务人员', dataIndex: 'operator_dictText', width: 140 },
    { title: '合同类型', dataIndex: 'contractType_dictText', width: 130 },
    { title: '租金(元/月)', dataIndex: 'wyBuildingCount', width: 140 },
    { title: '开始日期', dataIndex: 'startDate', width: 120 },
    { title: '结束日期', dataIndex: 'expireDate', width: 120 }
  ],
  searchList: [
    { label: '单据编号', name: 'number', type: 's-input' },
    { label: '合同编号', name: 'contractNumber', type: 's-input' },
    { label: '审核人', name: 'auditBy', type: 'user-select' },
    { label: '业务状态', name: 'bizStatus', type: 'dict-select', code: 'CT_BASE_ENUM_Contract_BizStatus' },
    // { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
    { label: '合同编号', name: 'contractNumber', type: 's-input' },
    { label: '合同类型', name: 'contractType', type: 'dict-select', code: 'CT_BAS_ContractType' },
    { label: '业务日期', name: 'bizDate', type: 'date' },
    { label: '签约日期', name: 'signDate', type: 'date' },
    { label: '合同开始时间', name: 'startDate', type: 'date' },
    { label: '合同结束时间', name: 'expireDate', type: 'date' },
    { label: '业务员', name: 'operator', type: 'user-select' },
    { label: '业务部门', name: 'operatorDepart', type: 'depart-select' },
    { label: '定价类型', name: 'pricedType', type: 'dict-select', code: 'CT_BASE_ENUM_Contract_PricedType' },
    { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
    { label: '客户', name: 'customer', type: 'customer-select' }
  ]
}
