import { getF7List } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { renderDictTag } from '@/utils/render'
import region from '@/json/region.json'
import { projectPage } from '@/views/projects/apis.js'
import { getPage } from '@/views/assets/manage/apis'

const getProjectPage = () => projectPage({ pageNo: 1, pageSize: 50000 })
const getAssetsPage = () => getPage({ pageNo: 1, pageSize: 50000 })

export default {
  modalTitle: '选择租赁单元',
  request: getF7List,
  params: {
    column: 'number',
    order: 'desc',
    name: undefined,
    pcaCode: [],
    useType: undefined,
    leaseUse: undefined,
    areaManager: undefined,
    status: 'AUDITOK',
    bizStatus: undefined,
    supportFacility: undefined,
    effectDate: undefined,
    expireDate: undefined,
    layerNum: undefined,
    propertyUse: undefined,
    wyProject: undefined,
    number: undefined,
    treeId: undefined
  },
  rowKey: 'id',
  displayKey: 'name',
  keywordKey: 'name',
  keywordPlaceholder: '搜索租赁单元名称',
  clearIgnoreKeys: ['status'],
  statIgnoreKeys: ['status'],
  scrollX: 2200,
  columns: [
    { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
    { title: '地址', dataIndex: 'detailAddress', ellipsis: true },
    { title: '使用类型', dataIndex: 'useType_dictText' },
    { title: '租赁面积', dataIndex: 'leaseArea' },
    { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
    { title: '片区管理员', dataIndex: 'areaManager_dictText' },
    {
      title: '单据状态',
      dataIndex: 'status',
      customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
    },
    { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
    { title: '配套设施', dataIndex: 'supportFacility', ellipsis: true },
    { title: '生效日期', dataIndex: 'effectDate' },
    { title: '到期时间', dataIndex: 'expireDate' },
    { title: '层数/总层数', dataIndex: 'layerNum' },
    { title: '产权', dataIndex: 'property' },
    { title: '项目', dataIndex: 'wyProject_dictText', ellipsis: true },
    { title: '租赁单元编号', dataIndex: 'number', width: 200 }
  ],
  searchList: [
    { label: '区域', name: 'pcaCode', type: 'cascader', options: region },
    { label: '使用类型', name: 'useType', type: 'dict-select', code: 'CT_BAS_UseType' },
    { label: '租赁用途', name: 'leaseUse', type: 'dict-select' },
    { label: '片区管理员', name: 'areaManager', type: 'user-select' },
    { label: '业务状态', name: 'bizStatus', type: 'dict-select', code: 'CT_BASE_ENUM_LeaseUnit_BizStatus' },
    { label: '配套设施', name: 'supportFacility', type: 's-input' },
    { label: '生效日期', name: 'effectDate', type: 'date' },
    { label: '到期日期', name: 'expireDate', type: 'date' },
    { label: '层数/总层数', name: 'layerNum', type: 's-input' },
    { label: '产权用途', name: 'propertyUse', type: 'dict-select', code: 'CT_BAS_PropertyUse' },
    { label: '所属项目', name: 'wyProject', type: 'api-select', asyncFn: getProjectPage },
    { label: '关联资产', name: 'houseOwner', type: 'api-select', asyncFn: getAssetsPage },
    { label: '单元编号', name: 'number', type: 's-input' }
  ]
}
