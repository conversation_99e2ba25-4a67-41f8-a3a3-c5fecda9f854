import { f7TransferDetailList } from '@/views/finance/transferDeduction/apis'
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { formatCurrency } from '@/utils/number'

const loadLeaseUnit = () => getLeaseUnitList({ pageNo: 1, pageSize: 50000 })

export default {
  modalTitle: '选择转款明细',
  request: f7TransferDetailList,
  params: {
    column: 'number',
    order: 'desc',
    number: undefined,
    customer: undefined,
    contract: undefined,
    leaseUnit: undefined,
    paymentType: undefined,
    status: 'AUDITOK',
    receiveDate: undefined,
    currentBillId: undefined
  },
  rowKey: 'id',
  displayKey: 'number',
  keywordKey: 'number',
  keywordPlaceholder: '搜索单据编号',
  clearIgnoreKeys: ['status'],
  statIgnoreKeys: ['status'],
  scrollX: 1800,
  columns: [
    { title: '账单编号', dataIndex: 'number', width: 200, fixed: 'left' },
    { title: '客户名称', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
    { title: '合同', dataIndex: 'contract_dictText', width: 200, ellipsis: true },
    { title: '租赁单元', dataIndex: 'leaseUnit', width: 160, ellipsis: true },
    { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
    { title: '状态', dataIndex: 'status', width: 120 },
    { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
    { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
    { title: '款项金额', dataIndex: 'paymentAmount', width: 120, customRender: ({ text }) => formatCurrency(text) },
    { title: '减免金额', dataIndex: 'remission', width: 120, customRender: ({ text }) => formatCurrency(text) },
    {
      title: '实际应收',
      dataIndex: 'actualReceiveAmount',
      width: 120,
      customRender: ({ text }) => formatCurrency(text)
    },
    { title: '已收金额', dataIndex: 'paid', width: 120, customRender: ({ text }) => formatCurrency(text) },
    { title: '未收金额', dataIndex: 'residual', width: 120, customRender: ({ text }) => formatCurrency(text) },
    { title: '可抵退转金额', dataIndex: 'balance', width: 140, customRender: ({ text }) => formatCurrency(text) }
  ],
  searchList: [
    { label: '客户名称', name: 'customer', type: 'customer-select' },
    { label: '合同', name: 'contract', type: 's-input' },
    { label: '租赁单元', name: 'leaseUnit', type: 'api-select', asyncFn: loadLeaseUnit },
    { label: '款项类型', name: 'paymentType', type: 'dict-select', code: 'CT_BASE_ENUM_PaymentType_PaymentProperties' },
    { label: '应收日期', name: 'receiveDate', type: 'date' }
  ]
}
