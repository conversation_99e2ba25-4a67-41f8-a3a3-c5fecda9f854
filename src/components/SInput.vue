专用于模糊搜索的输入框
<template>
  <a-input v-bind="$attrs" :value="value" allow-clear @input="handleInput">
    <template #prefix v-if="showSearchIcon">
      <i class="a-icon-search text-primary"></i>
    </template>
  </a-input>
</template>

<script setup>
const props = defineProps({
  modelValue: { type: String, default: '' },
  showSearchIcon: { type: Boolean, default: true }
})

const emit = defineEmits(['update:modelValue', 'input'])

const value = computed(() => {
  if (props.modelValue) return props.modelValue.replaceAll('*', '')
  return ''
})

const handleInput = (e) => {
  const val = e.target.value ? `*${e.target.value}*` : undefined
  emit('update:modelValue', val)
  emit('input', val)
}
</script>
