<template>
  <a-tree-select
    v-bind="$attrs"
    :value="modelValue || undefined"
    show-search
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    placeholder="请选择"
    allow-clear
    tree-default-expand-all
    :tree-data="treeData"
    tree-node-filter-prop="title"
    @change="onchange"
  ></a-tree-select>
</template>

<script setup>
const { asyncFn } = defineProps({
  modelValue: { required: true, type: [String, Array] },
  asyncFn: {
    required: true,
    type: Function,
    validator: (val) => {
      if (typeof val === 'function') return true
      throw new Error('请确认你传入的是一个函数，而不是一个Promise对象')
    }
  }
})

const emits = defineEmits(['update:modelValue', 'change'])

const onchange = (value) => {
  emits('update:modelValue', value)
  emits('change', value)
}

const treeData = ref([])
onMounted(async () => {
  const { result } = await asyncFn()
  treeData.value = result
})
</script>
