<!-- 水电表选择弹窗 -->
<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="800px"
    title="选择水电表"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="mb-[12px]">
      <a-form autocomplete="off" layout="inline">
        <a-form-item label="名称">
          <s-input
            class="!w-[280px]"
            v-model:value="searchParams.name"
            placeholder="请输入名称"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </a-form>
    </div>
    <a-table
      ref="tableRef"
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: 600, x: 1500 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        selectedRows,
        type: multiple ? 'checkbox' : 'radio',
        onChange: onSelectChange
      }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'price'">{{ record.price }}元</template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup>
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getWaterElectricityList } from '@/views/waterElectricity/manage/apis/waterElectricity.js'

const { multiple, asyncFunc } = defineProps({
  multiple: { default: false, type: Boolean },
  asyncFunc: { default: getWaterElectricityList, type: Function },
  ids: { default: () => [], type: Array }
})
const visible = ref(false)

// 搜索参数
const searchParams = reactive({
  name: undefined,
  number: undefined,
  type: undefined,
  property: undefined,
  doubleRate: undefined,
  status: undefined,
  ownerCompany: undefined,
  collectionCompany: undefined,
  manageCompany: undefined,
  price: undefined,
  remark: undefined,
  treeId: undefined
})

// 高级搜索配置
const searchList = reactive([
  { label: '编码(表号)', name: 'number', type: 's-input', placeholder: '请输入编码(表号)' },
  {
    label: '类型',
    name: 'type',
    type: 'dict-select',
    dictCode: 'water_electricity_type',
    placeholder: '请选择类型'
  },
  {
    label: '属性',
    name: 'property',
    type: 'dict-select',
    dictCode: 'water_electricity_property',
    placeholder: '请选择属性'
  },
  { label: '倍率', name: 'doubleRate', type: 'input', placeholder: '请输入倍率' },
  {
    label: '状态',
    name: 'status',
    type: 'dict-select',
    dictCode: 'common_status',
    placeholder: '请选择状态'
  },
  { label: '资产权属公司', name: 'ownerCompany', type: 'company-select', placeholder: '请选择资产权属公司' },
  { label: '租金归集公司', name: 'collectionCompany', type: 'company-select', placeholder: '请选择租金归集公司' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择物业管理公司' },
  { label: '单价', name: 'price', type: 'input', placeholder: '请输入单价' },
  { label: '备注', name: 'remark', type: 's-input', placeholder: '请输入备注' }
])
const columns = [
  { title: '名称', dataIndex: 'name', fixed: 'left', width: 200 },
  { title: '编码(表号)', dataIndex: 'number', width: 160 },
  { title: '类型', dataIndex: 'type_dictText', width: 120 },
  { title: '属性', dataIndex: 'property_dictText', width: 120 },
  { title: '倍率', dataIndex: 'doubleRate', width: 120 },
  { title: '单价', dataIndex: 'price', width: 120, customRender: ({ text }) => `${text}元` },
  { title: '资产权属公司', dataIndex: 'ownerCompany_dictText', width: 160, ellipsis: true },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '组别', dataIndex: 'treeId_dictText', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(asyncFunc)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

/**
 * 打开弹窗
 * @param {Array} selectedList - 已选择的数据列表
 * @param {Object} filterParams - 筛选参数
 */
const open = (selectedList = [], filterParams = {}) => {
  // 设置默认筛选条件
  Object.assign(searchParams, filterParams)

  onTableChange()

  if (selectedList && selectedList.length > 0) {
    const selectedIds = selectedList.map((item) => item.value)
    selectedRowKeys.value = selectedIds
    selectedRows.value = list.value.filter((item) => selectedIds.includes(item.id))
  }
  visible.value = true
}

/**
 * 表格变化事件
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

/**
 * 搜索输入防抖处理
 */
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const emits = defineEmits(['selectChange'])

/**
 * 确认选择
 */
const handleOk = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emits('selectChange', selectedRows.value)
  visible.value = false
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

defineExpose({ open })
</script>
