<!-- 筛选功能 -->
<template>
  <a-dropdown trigger="click" :visible="dropdownVisible" @visible-change="handleVisibleChange">
    <a-button>
      <span class="a-icon-filter mr-[8px]"></span>
      <span>筛选</span>
      <span>({{ filterNum }})</span>
    </a-button>
    <template #overlay>
      <a-menu class="!p-[24px] !min-w-[350px]" force-sub-menu-render>
        <a-form
          class="search-content overflow-auto no-scrollbar"
          :model="localSearch"
          ref="formRef"
          :label-col="{ style: { width: '100px' } }"
          autocomplete="off"
        >
          <a-form-item
            class="!mb-[12px]"
            v-for="(item, index) in searchList"
            :key="index"
            :name="item.name"
            :label="item.label"
          >
            <template v-if="item.type === 'number'">
              <a-input-number
                v-model:value="localSearch[item.name]"
                :min="item.min"
                :precision="item.precision"
                :placeholder="item.placeholder || '请输入'"
                style="width: 100%"
              />
            </template>
            <template v-if="item.type === 'radio'">
              <a-radio-group v-model:value="localSearch[item.name]">
                <a-radio
                  v-for="i in item.list"
                  :key="i.value"
                  :value="i.value"
                  @click="handleRadioClick(item, i.value)"
                >
                  {{ i.label }}
                </a-radio>
              </a-radio-group>
            </template>
            <template v-if="item.type === 'checkbox'">
              <a-checkbox-group v-model:value="localSearch[item.name]" :options="item.list" />
            </template>
            <template v-if="item.type === 'input'">
              <a-input
                v-model:value="localSearch[item.name]"
                :placeholder="item.placeholder || '请输入'"
                allow-clear
              ></a-input>
            </template>
            <template v-if="item.type === 'cascader'">
              <a-cascader
                ref="cascaderRef"
                v-model:value="localSearch[item.name]"
                :options="item.list"
                :placeholder="item.placeholder || '请选择'"
                allow-clear
              />
            </template>
            <template v-if="item.type === 'select'">
              <a-select v-model:value="localSearch[item.name]" :placeholder="item.placeholder || '请选择'" allow-clear>
                <a-select-option v-for="i in item.list" :key="i.value" :value="i.value">
                  {{ i.label }}
                </a-select-option>
              </a-select>
            </template>
            <template v-if="['date', 'year', 'month', 'week'].includes(item.type)">
              <a-date-picker
                :picker="item.type"
                :value-format="item.format || 'YYYY-MM-DD'"
                :format="item.format || 'YYYY-MM-DD'"
                v-model:value="localSearch[item.name]"
                :placeholder="item.placeholder || '请选择'"
                style="width: 100%"
              />
            </template>
            <template v-if="item.type === 's-input'">
              <s-input v-model="localSearch[item.name]" :placeholder="item.placeholder || '请输入'"></s-input>
            </template>
            <template v-if="item.type === 'dic'">
              <dict-select
                v-model="localSearch[item.name]"
                :placeholder="item.placeholder || '请选择'"
                :code="item.code"
              ></dict-select>
            </template>
            <template v-if="item.type === 'api'">
              <api-select
                v-model="localSearch[item.name]"
                :async-fn="item.listFunc"
                :field-names="item.fieldNames || { label: 'name', value: 'id' }"
                :placeholder="item.placeholder || '请选择'"
                @change="valueChange(localSearch[item.name], item.name)"
              ></api-select>
            </template>
            <template v-if="item.type === 'customerSelect'">
              <api-select
                v-model="localSearch[item.name]"
                :async-fn="customerListFunc"
                :field-names="item.fieldNames || { label: 'name', value: 'id' }"
                :placeholder="item.placeholder || '请选择'"
                @change="valueChange(localSearch[item.name], item.name)"
              ></api-select>
            </template>
            <template v-if="item.type === 'userSelect'">
              <api-select
                v-model="localSearch[item.name]"
                :async-fn="userListFunc"
                :field-names="item.fieldNames || { label: 'realname', value: 'id' }"
                :placeholder="item.placeholder || '请选择'"
                @change="valueChange(localSearch[item.name], item.name)"
              ></api-select>
            </template>
            <template v-if="item.type === 'leaseUnitSelect'">
              <api-select
                v-model="localSearch[item.name]"
                :async-fn="leaseUnitListFunc"
                :field-names="item.fieldNames || { label: 'name', value: 'id' }"
                :placeholder="item.placeholder || '请选择'"
                @change="valueChange(localSearch[item.name], item.name)"
              ></api-select>
            </template>
            <template v-if="item.type === 'companySelect'">
              <company-select
                v-model="localSearch[item.name]"
                :placeholder="item.placeholder || '请选择'"
              ></company-select>
            </template>
            <template v-if="item.type === 'departSelect'">
              <depart-select
                v-model="localSearch[item.name]"
                :placeholder="item.placeholder || '请选择'"
              ></depart-select>
            </template>
          </a-form-item>
        </a-form>

        <div class="flex justify-between items-center pt-[12px]">
          <div class="cursor-pointer clear-btn active:text-primary select-none" @click="clearSearch">
            <span class="a-icon-reset mr-[8px]"></span>
            <span>清空</span>
          </div>
          <div>
            <a-button @click="dropdownVisible = false">取消</a-button>
            <a-button type="primary" @click="updateSearch">筛选</a-button>
          </div>
        </div>
      </a-menu>
    </template>
  </a-dropdown>
</template>
<script setup>
import { getUserList } from '@/views/system/user/apis'
import { getCustomerList } from '@/views/customer/manage/apis'
import { getF7List as leaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import useUserStore from '@/store/modules/user'
const { userInfo } = useUserStore()
const customerListFunc = () =>
  getCustomerList({ pageNo: 1, pageSize: 10000, manageCompany: userInfo.value.currentCompany })
const userListFunc = () => getUserList({ pageNo: 1, pageSize: 10000, currentCompany: userInfo.value.currentCompany })
const leaseUnitListFunc = () =>
  leaseUnitList({ pageNo: 1, pageSize: 10000, currentCompany: userInfo.value.currentCompany })
const emits = defineEmits(['searchChange', 'filterItemChange', 'update:modelValue'])
const { modelValue } = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  searchList: { type: Array, default: () => [] }
})
const localSearch = ref({})
const dropdownVisible = ref(false)
const filterNum = computed(() => {
  let num = 0
  for (const key in modelValue) {
    if (['number', 'string', 'boolean'].includes(typeof modelValue[key]) && modelValue[key]) {
      num++
    }
    if (Array.isArray(modelValue[key]) && modelValue[key].length) {
      num++
    }
  }
  return num
})

const handleRadioClick = (item, value) => {
  // 当前选中值等于点击的选项值时，取消勾选（设为 null）
  if (localSearch.value[item.name] === value) {
    localSearch.value[item.name] = ''
  }
}

const updateSearch = () => {
  emits('update:modelValue', localSearch.value)
  emits('searchChange')
  dropdownVisible.value = false
}
const clearSearch = () => {
  for (const key in localSearch.value) {
    if (['number', 'string'].includes(typeof localSearch.value[key])) {
      localSearch.value[key] = ''
    }
    if (['boolean'].includes(typeof localSearch.value[key])) {
      localSearch.value[key] = undefined
    }
    if (Array.isArray(localSearch.value[key])) {
      localSearch.value[key] = []
    }
  }
}
const handleVisibleChange = (val) => {
  if (val) {
    localSearch.value = JSON.parse(JSON.stringify(modelValue))
  }
  dropdownVisible.value = val
}

// api-select 值改变的回调
const valueChange = (id, name) => {
  emits('filterItemChange', id, name)
}
</script>

<style lang="less" scoped>
.search-content {
  max-height: 400px;
}
</style>
