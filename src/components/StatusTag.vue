显示两种样式的tag，一种是前面有个小圆点的样式，一种是经典标签样式
因状态大多数来自数据字典，所以该组件做成，根据字典键值，自动获取字典的文本和对应的颜色
也可自定义颜色和显示文本，自定义颜色通过color属性传入，自定义文本通过slot传入，优先级高于字典配置 用法举例:
<!-- <status-tag dict-value="xxx" dict-code="xxx"></status-tag> -->
<!-- <status-tag dict-value="xxx" dict-code="xxx" color="#f60" type="dot"></status-tag> -->
<!-- <status-tag dict-value="xxx" dict-code="xxx" :style="{ fontSize: '16px' }"></status-tag> -->
<!-- <status-tag color="#f00">自定义内容</status-tag> -->

<template>
  <span :class="{ 'status-tag': true, [`status-tag-${type}`]: true }" ref="statusTagRef" :style="styleObject">
    <slot>{{ dictText }}</slot>
  </span>
</template>

<script setup>
import { useDictStore } from '@/store/modules/dict'
import { colorRegexp } from '@/utils/validate'

const { dictCode, dictValue, color, style } = defineProps({
  dictCode: { type: String, default: '' },
  dictValue: { type: [String, Number], default: '' },
  color: {
    type: String,
    default: '',
    validator: (value) => Boolean(!value || colorRegexp.test(value))
  },
  style: { type: Object, default: null }, // 自定义样式
  type: {
    type: String,
    default: 'tag',
    validator: (value) => ['tag', 'dot'].includes(value)
  }
})

const store = useDictStore()

const dictData = computed(() => {
  if (!dictCode) return ''
  if (!Object.keys(store.dict).length) return ''
  const list = store.dict[dictCode]
  if (!(list && list.length)) return
  return list.find((i) => i.value === String(dictValue))
})

const dictText = computed(() => {
  return dictData.value ? dictData.value.label : ''
})

const styleObject = computed(() => {
  const colorValue = color || (dictData.value && dictData.value.color) || '#1d64f0'
  return Object.assign(
    {
      '--color-status-tag': colorValue,
      '--color-status-tag-rgb': getColorRgb(colorValue)
    },
    style
  )
})

// 传入十六进制颜色值，获取其rgb
const getColorRgb = (hex) => {
  if (hex.length === 4) {
    hex = `#${hex[1]}${hex[1]}${hex[2]}${hex[2]}${hex[3]}${hex[3]}`
  }
  const rgb = /^#([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/.exec(hex)
  return `${parseInt(rgb[1], 16)}, ${parseInt(rgb[2], 16)}, ${parseInt(rgb[3], 16)}`
}
</script>

<style lang="less" scoped>
.status-tag {
  --color-status-tag: #1d64f0;
  --color-status-tag-rgb: 29, 100, 240;
  white-space: nowrap;
  &-tag {
    display: inline-block;
    height: 28px;
    line-height: 28px;
    border-radius: 8px;
    padding: 0 8px;
    font-size: 14px;
    color: var(--color-status-tag);
    background-color: rgba(var(--color-status-tag-rgb), 0.1);
  }
  &-dot {
    position: relative;
    padding-left: 12px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: var(--color-status-tag);
    }
  }
}
</style>
