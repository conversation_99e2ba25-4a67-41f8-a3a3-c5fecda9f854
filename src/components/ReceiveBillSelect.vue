<!-- 应收单选择 -->
<template>
  <a-select
    multiple
    v-bind="$attrs"
    v-model:value="localValue"
    :placeholder="placeholder"
    :open="false"
    style="width: 100%"
    @click="handleClick"
    :options="localOptions"
  />
  <!-- 选择应收单弹窗 -->
  <receive-bill-select-dialog
    ref="receiveBillSelectRef"
    :multiple="multiple"
    :ids="selectedIds"
    @selectChange="selectChange"
  ></receive-bill-select-dialog>
</template>
<script setup>
onMounted(() => {
  localValue.value = modelValue || undefined
  localOptions.value = options || []
})
const selectedIds = computed(() => {
  return multiple ? localValue : localValue.value && localValue.value.split(',')
})
const emits = defineEmits(['update:modelValue', 'selectChange'])
const { modelValue, options, multiple } = defineProps({
  multiple: { default: false, type: Boolean },
  modelValue: { required: true, type: [String, Array] },
  options: { default: () => [], type: Array },
  placeholder: { default: '请选择', type: String }
})
const localValue = ref(undefined)
const localOptions = ref([])
// 打开弹窗
const receiveBillSelectRef = ref()
const handleClick = () => {
  receiveBillSelectRef.value.open(localOptions.value)
}
// 选择弹窗里数据选择后的回调
const selectChange = (list) => {
  const value = multiple ? list.map((item) => item.id) : list[0].id
  localValue.value = value
  emits('update:modelValue', value)
  emits('selectChange', list)
  localOptions.value = list.map((item) => {
    return {
      label: item.name,
      value: item.id
    }
  })
}
</script>
