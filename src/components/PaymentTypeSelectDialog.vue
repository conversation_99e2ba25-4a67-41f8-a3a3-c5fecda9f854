<!-- 资产选择弹窗 -->
<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="50%"
    title="选择款项类型"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="mb-[12px]">
      <a-form autocomplete="off" layout="inline">
        <a-form-item label="编码">
          <s-input
            class="!w-[280px]"
            v-model:value="search.number"
            placeholder="请输入编码"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
      </a-form>
    </div>
    <a-table
      ref="tableRef"
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: 600, x: 1000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        selectedRows,
        type: multiple ? 'checkbox' : 'radio',
        onChange: onSelectChange
      }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>
<script setup>
import { f7List } from '@/views/paymentType/apis.js'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message } from 'ant-design-vue'

const { multiple, asyncFunc } = defineProps({
  multiple: { default: false, type: Boolean },
  asyncFunc: { default: f7List, type: Function },
  // 已选择的ids
  ids: { default: () => [], type: Array }
})
const visible = ref(false)
const open = (selectedList = []) => {
  onTableChange()
  if (selectedList && selectedList.length > 0) {
    const selectedIds = selectedList.map((item) => item.value)
    selectedRowKeys.value = selectedIds
    // 根据 id 从 list 中获取完整的对象数据
    selectedRows.value = list.value.filter((item) => selectedIds.includes(item.id))
  }
  visible.value = true
}
defineExpose({ open })
const search = ref({
  column: 'number',
  order: 'desc',
  number: ''
})

const searchFilter = ref({})
const searchList = reactive([])
const columns = [
  { title: '编码', dataIndex: 'number', width: 180, fixed: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
  {
    title: '款项类型',
    dataIndex: 'paymentProperties_dictText'
  },
  {
    title: '款项名称',
    dataIndex: 'name'
  },
  // { title: '款项类型编码', dataIndex: 'paymentProperties' },
  {
    title: '状态',
    dataIndex: 'status_dictText'
  }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(asyncFunc)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({
    pageNo: current ?? pageNo,
    pageSize,
    ...search.value,
    ...searchFilter.value
  })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

const emits = defineEmits(['selectChange'])
const handleOk = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emits('selectChange', selectedRows.value)
  visible.value = false
}
/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}
</script>
