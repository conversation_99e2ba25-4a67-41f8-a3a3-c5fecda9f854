<!-- 租赁单元选择 -->
<template>
  <a-select
    multiple
    v-bind="$attrs"
    v-model:value="localValue"
    :placeholder="placeholder"
    :open="false"
    style="width: 100%"
    @click="handleHouseOwnerClick"
    :options="localOptions"
  />
  <!-- 选择资产弹窗 -->
  <lease-unit-select ref="leaseUnitRef" :multiple="multiple" @selectChange="selectChange"></lease-unit-select>
</template>
<script setup>
onMounted(() => {
  localValue.value = modelValue || undefined
  localOptions.value = options || []
})
const emits = defineEmits(['update:modelValue', 'selectChange'])
const { modelValue, options, multiple } = defineProps({
  multiple: { default: false, type: Boolean },
  modelValue: { required: true, type: [String, Array] },
  options: { default: () => [], type: Array },
  placeholder: { default: '请选择', type: String }
})
const localValue = ref(undefined)
const localOptions = ref([])
// 打开弹窗
const leaseUnitRef = ref()
const handleHouseOwnerClick = () => {
  leaseUnitRef.value.open(localOptions.value)
}
// 选择弹窗里数据选择后的回调
const selectChange = (list) => {
  const value = multiple ? list.map((item) => item.id) : list[0].id
  localValue.value = value
  emits('update:modelValue', value)
  emits('selectChange', list)
  localOptions.value = list.map((item) => {
    return {
      label: item.name,
      value: item.id
    }
  })
}
</script>
