import axios from 'axios'
import { useUserStore } from '@/store/modules/user'
import router from '@/router'
import { message, Modal } from 'ant-design-vue'

/**
 * 接口报错时，弹出错误消息
 * @param {String} type - false | message | modal
 */
const showErrorMsg = (type, content) => {
  if (type === false) return
  type === 'message' ? message.error(content) : Modal.error({ title: '操作失败', content })
}

const request = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 10000 * 6,
  validateStatus: (status) => status >= 0 && status <= 600,
  withCredentials: true
})

request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    config.headers['X-Access-Token'] = userStore.token || ''
    // showErrorMsg=message | modal
    // 默认，接口报错时通过ant-design-vue的message来展示错误信息
    if (![false, 'message', 'modal'].includes(config.showErrorMsg)) {
      config.showErrorMsg = 'message'
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截
request.interceptors.response.use(
  (res) => {
    const code = res.data?.code ?? res.status
    const msg = res.data?.message ?? res.statusText
    // jeecg的后端有个大坑，有时候code=0时，实际上是请求成功的情况，所以补充一个判断，判断success是否为true
    if (res.data && res.data.success) {
      return res.data
    }
    if (code !== 200) {
      if (code === 401) {
        router.replace('/login')
        return Promise.reject(new Error('unauthorized'))
      }
      // 后端返回文件流的接口报错
      if (res.config.responseType === 'blob') {
        return new Promise((resolve, reject) => {
          const blobData = new Blob([res.data])
          const reader = new FileReader()
          reader.readAsText(blobData, 'utf-8')
          let msg = '导出失败，请联系管理员'
          reader.onload = () => {
            try {
              const msgObj = JSON.parse(reader.result)
              msg = msgObj.msg || '导出失败，请联系管理员'
              showErrorMsg(res.config.showErrorMsg, msg)
              reject(new Error(msg))
            } catch (err) {
              showErrorMsg(res.config.showErrorMsg, err?.message || msg)
              reject(new Error(err.message || msg))
            }
          }
        })
      }
      showErrorMsg(res.config.showErrorMsg, msg)
      return Promise.reject(new Error(msg))
    }
    if (res.config.responseType === 'blob') {
      if (!res.config.fileName) {
        showErrorMsg(res.config.showErrorMsg, '请在请求配置中设置fileName')
        return Promise.reject(new Error('请在请求配置中设置fileName'))
      }
      const blob = new Blob([res.data], { type: res.data.type })
      const url = window.URL.createObjectURL(blob) // 创建下载链接
      const a = document.createElement('a')
      a.download = res.config.fileName
      a.href = url
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    }
    return res.data
  },
  (error) => Promise.reject(error)
)

export default request
