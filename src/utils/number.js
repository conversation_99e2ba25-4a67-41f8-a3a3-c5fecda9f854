/**
 * 数字处理工具函数
 */

/**
 * 格式化数字到指定小数位数（最多保留指定位数，没有小数时显示整数）
 * @param {number|string} num - 要处理的数字
 * @param {number} decimals - 最多保留的小数位数
 * @returns {string} 格式化后的数字字符串
 */
export function formatDecimals(num, decimals = 2) {
  if (num === null || num === undefined || num === '') {
    return '0'
  }

  const number = Number(num)
  if (isNaN(number)) {
    return '0'
  }

  if (typeof decimals !== 'number' || decimals < 0) {
    decimals = 2
  }

  // 使用 toFixed 后去除末尾的零
  const fixed = number.toFixed(decimals)
  return parseFloat(fixed).toString()
}

/**
 * 对数字进行千分位处理
 * @param {number|string} num - 要处理的数字
 * @param {number} decimals - 最多保留的小数位数，默认为原数字的小数位数
 * @returns {string} 千分位格式的字符串
 */
export function formatThousands(num, decimals) {
  if (num === null || num === undefined || num === '') {
    return '0'
  }

  const number = Number(num)
  if (isNaN(number)) {
    return '0'
  }

  // 如果指定了小数位数，先进行小数位处理
  let processedNumber = number
  if (typeof decimals === 'number' && decimals >= 0) {
    // 使用 formatDecimals 保证一致的小数处理逻辑
    processedNumber = formatDecimals(number, decimals)
  }

  // 转换为字符串并分离整数和小数部分
  const parts = processedNumber.toString().split('.')
  const integerPart = parts[0]
  const decimalPart = parts[1] || ''

  // 对整数部分添加千分位分隔符
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // 拼接结果
  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger
}

/**
 * 移除千分位分隔符，返回纯数字
 * @param {string} str - 带千分位分隔符的字符串
 * @returns {number} 数字
 */
export function parseThousands(str) {
  if (typeof str !== 'string') {
    return Number(str) || 0
  }

  const cleaned = str.replace(/,/g, '')
  return Number(cleaned) || 0
}

/**
 * 判断是否为有效数字
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为有效数字
 */
export function isValidNumber(value) {
  return value !== null && value !== undefined && value !== '' && !isNaN(Number(value))
}

/**
 * 格式化数字（千分位 + 小数位处理）
 * @param {number|string} num - 要处理的数字
 * @param {number} decimals - 最多保留的小数位数，默认为2位
 * @returns {string} 格式化后的字符串（带千分位分隔符）
 */
export function formatNumber(num, decimals = 2) {
  const formattedNum = formatDecimals(num, decimals)
  return formatThousands(formattedNum)
}

/**
 * 格式化金额（专门用于金额显示）
 * @param {number|string} amount - 金额
 * @param {number} decimals - 小数位数，默认2位
 * @param {string} currency - 货币符号，默认为空
 * @param {string} suffix - 后缀，默认为"元"
 * @returns {string} 格式化后的金额字符串
 */
export function formatCurrency(amount, decimals = 2, currency = '', suffix = '元') {
  const formatted = formatNumber(amount, decimals)
  const result = currency ? `${currency}${formatted}` : formatted
  return suffix ? `${result}${suffix}` : result
}

/**
 * 格式化单价（最多保留6位小数）
 * @param {number|string} price - 单价数值
 * @param {boolean} withThousands - 是否使用千分位分隔符，默认为true
 * @param {string} suffix - 后缀，默认为"元"
 * @returns {string} 格式化后的单价字符串
 */
export function formatUnitPrice(price, withThousands = true, suffix = '元') {
  if (price === null || price === undefined || price === '') {
    const defaultValue = '0'
    return suffix ? `${defaultValue}${suffix}` : defaultValue
  }
  const number = Number(price)
  if (isNaN(number)) {
    const defaultValue = '0'
    return suffix ? `${defaultValue}${suffix}` : defaultValue
  }
  let result
  if (withThousands) {
    result = formatNumber(number, 6)
  } else {
    result = formatDecimals(number, 6)
  }
  return suffix ? `${result}${suffix}` : result
}
/**
 * 格式化税率（最多保留4位小数）
 * @param {number|string} rate - 税率数值
 * @param {boolean} withPercent - 是否添加百分号，默认为true
 * @param {boolean} withThousands - 是否使用千分位分隔符，默认为true
 * @returns {string} 格式化后的税率字符串
 */
export function formatTaxRate(rate, withPercent = true, withThousands = true) {
  if (rate === null || rate === undefined || rate === '') {
    const defaultValue = '0'
    return withPercent ? `${defaultValue}%` : defaultValue
  }
  const number = Number(rate)
  if (isNaN(number)) {
    const defaultValue = '0'
    return withPercent ? `${defaultValue}%` : defaultValue
  }
  let formattedRate
  if (withThousands) {
    formattedRate = formatNumber(number, 4)
  } else {
    formattedRate = formatDecimals(number, 4)
  }
  return withPercent ? `${formattedRate}%` : formattedRate
}

/**
 * 格式化面积（默认使用千分位分隔符，默认后缀为m²）
 * @param {number|string} area - 面积数值
 * @param {number} decimals - 最多保留的小数位数，默认4位
 * @param {boolean} withThousands - 是否使用千分位分隔符，默认为true
 * @param {string} suffix - 后缀，默认为"m²"
 * @returns {string} 格式化后的面积字符串
 */
export function formatArea(area, decimals = 4, withThousands = true, suffix = 'm²') {
  if (area === null || area === undefined || area === '') {
    const defaultValue = '0'
    return suffix ? `${defaultValue}${suffix}` : defaultValue
  }

  const number = Number(area)
  if (isNaN(number)) {
    const defaultValue = '0'
    return suffix ? `${defaultValue}${suffix}` : defaultValue
  }

  let result
  if (withThousands) {
    result = formatNumber(number, decimals)
  } else {
    result = formatDecimals(number, decimals)
  }

  return suffix ? `${result}${suffix}` : result
}

/**
 * 格式化高度（最多保留4位小数，默认使用千分位分隔符，默认后缀为m）
 * @param {number|string} height - 高度数值
 * @param {number} decimals - 最多保留的小数位数，默认4位
 * @param {boolean} withThousands - 是否使用千分位分隔符，默认为true
 * @param {string} suffix - 后缀，默认为"m"
 * @returns {string} 格式化后的高度字符串
 */
export function formatHeight(height, decimals = 4, withThousands = true, suffix = 'm') {
  if (height === null || height === undefined || height === '') {
    const defaultValue = '0'
    return suffix ? `${defaultValue}${suffix}` : defaultValue
  }

  const number = Number(height)
  if (isNaN(number)) {
    const defaultValue = '0'
    return suffix ? `${defaultValue}${suffix}` : defaultValue
  }

  let result
  if (withThousands) {
    result = formatNumber(number, decimals)
  } else {
    result = formatDecimals(number, decimals)
  }

  return suffix ? `${result}${suffix}` : result
}
