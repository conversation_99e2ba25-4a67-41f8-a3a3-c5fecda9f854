export const intRegexp = /^[0-9]+$/ // 0和正整数
export const positionIntRegexp = /^[1-9]\d*$/ // 正整数，不包含0
export const emailRegexp =
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
export const phoneRegexp = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
export const moneyRegexp = /^(0|[1-9]\d*)(\.\d{1,2})?$/ // 金额，最多保留两位小数
export const colorRegexp = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/ // 十六进制颜色值
export const oneDecimalRegexp = /^(0|[1-9]\d*)(\.\d{1})?$/ // 最多保留1位小数的数字

// 校验正整数，包含0
export const validateInteger = (required = true, name = '排序') => {
  return function (_, value) {
    return new Promise((resolve, reject) => {
      if (typeof value === 'number') {
        value = value.toString()
      }
      if (!value) {
        required ? reject(new Error(`${name}为必填项`)) : resolve()
        return
      }
      if (!intRegexp.test(value)) reject(new Error(`${name}填写不正确`))
      else resolve()
    })
  }
}

// 校验正整数，不包含0
export const validatePositionInt = (required = true, name = '数值') => {
  return function (_, value) {
    return new Promise((resolve, reject) => {
      if (typeof value === 'number') {
        value = value.toString()
      }
      if (!value) {
        required ? reject(new Error(`${name}为必填项`)) : resolve()
        return
      }
      if (!positionIntRegexp.test(value)) reject(new Error(`${name}填写不正确`))
      else resolve()
    })
  }
}

export const validatePassword = (name = '密码', oldPassword = '') => {
  return function (_, value) {
    return new Promise((resolve, reject) => {
      if (!value) return reject(oldPassword ? '请再次输入密码进行确认' : `请输入${name}`)
      if (value.length < 6) return reject('密码长度至少为6位')
      if (oldPassword && oldPassword !== value) return reject('两次密码输入不一致，请重新输入')
      resolve()
    })
  }
}

export const validateEmail = (required = true, name = '邮箱') => {
  return function (_, value) {
    return new Promise((resolve, reject) => {
      if (!value) {
        required ? reject(new Error(`${name}为必填项`)) : resolve()
        return
      }
      if (!emailRegexp.test(value)) reject(new Error(`${name}填写不正确`))
      else resolve()
    })
  }
}

export const validatePhone = (required = true, name = '手机号码') => {
  return function (_, value) {
    return new Promise((resolve, reject) => {
      if (!value) {
        required ? reject(new Error(`${name}为必填项`)) : resolve()
        return
      }
      if (value.length !== 11) return reject(`${name}长度应为11位`)
      if (!phoneRegexp.test(value)) reject(new Error(`${name}填写不正确`))
      else resolve()
    })
  }
}

export const validateMoney = (required = true, name = '金额') => {
  return function (_, value) {
    return new Promise((resolve, reject) => {
      if (typeof value === 'number') {
        value = value.toString()
      }
      if (!value) {
        required ? reject(new Error(`${name}为必填项`)) : resolve()
        return
      }
      if (!moneyRegexp.test(value))
        reject(new Error(value.includes('.') ? `${name}填写不正确，小数点后最多保留两位` : `${name}填写不正确`))
      else resolve()
    })
  }
}

export const validateColor = (required = true, name = '颜色') => {
  return function (_, value) {
    return new Promise((resolve, reject) => {
      if (!value) {
        required ? reject(new Error(`${name}为必填项`)) : resolve()
        return
      }
      if (!colorRegexp.test(value)) reject(new Error(`${name}填写不合法`))
      else resolve()
    })
  }
}
