import { message } from 'ant-design-vue'
import useUserStore from '@/store/modules/user'

/**
 * 判断是否有权限
 * @param {string | string[]} code 为数组时，判断逻辑为“或”，即只要有其中一个权限，就算有权限
 * @returns {boolean}
 */
export const hasPermission = (code) => {
  const { permission } = useUserStore()
  const codeList = permission.value.codeList
  if (!(codeList && codeList.length)) {
    message.warning('您没有该操作权限，请联系管理员')
    return false
  }
  let hasPermission
  if (Array.isArray(code)) {
    hasPermission = code.some((item) => codeList.includes(item))
  } else {
    hasPermission = codeList.includes(code)
  }
  if (!hasPermission) {
    message.warning('您没有该操作权限，请联系管理员')
  }
  return hasPermission
}

export default {
  mounted(el, binding) {
    const permissionCode = binding.value
    if (!permissionCode) return

    const clickHandler = function (e) {
      if (!hasPermission(permissionCode)) {
        e.stopPropagation()
        e.preventDefault()
      }
    }

    el._permissionClickHandler = clickHandler
    el.addEventListener('click', clickHandler, true) // 捕获阶段拦截
  },

  unmounted(el) {
    if (el._permissionClickHandler) {
      el.removeEventListener('click', el._permissionClickHandler, true)
      delete el._permissionClickHandler
    }
  }
}
