<template>
  <a-drawer
    v-model:open="visible"
    class="lease-unit-split-merge-edit-drawer common-drawer"
    :title="`${formData.id ? '编辑' : '新建'}租赁单元拆合单`"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">基础信息</h4>
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="物业管理公司" name="manageCompany">
          <company-select
            v-model="formData.manageCompany"
            placeholder="请选择物业管理公司"
            @change="handleManageCompanyChange"
          ></company-select>
        </a-form-item>

        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="经办人" name="operator">
          <a-form-item-rest>
            <f7-select
              v-model="formData.operator"
              placeholder="请选择经办人"
              f7-type="user"
              :depart-id="searchDepartId"
              :relation-depart="true"
              @change="handleOperatorChange"
            />
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="经办部门" name="operatorDepart">
          <common-select
            v-model="formData.operatorDepart"
            :options="deptList"
            :field-names="{ label: 'title' }"
            placeholder="请选择经办部门"
          ></common-select>
        </a-form-item>

        <a-form-item label="拆合类型" name="splitMergeType">
          <dict-select
            v-model="formData.splitMergeType"
            placeholder="请选择拆合类型"
            code="CT_BASE_ENUM_LeaseUnitSplitMergeBill_SplitMergeType"
          ></dict-select>
        </a-form-item>

        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="备注" :rows="4" show-count :maxlength="255" />
        </a-form-item>
        <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">拆合单元信息</h4>
        <lease-unit-transfer
          v-model:source-units="sourceUnits"
          v-model:target-units="targetUnits"
          :split-merge-type="formData.splitMergeType"
          @view-source-unit-detail="handleViewSourceUnitDetail"
          @add-target-unit="handleAddTargetUnit"
          @edit-target-unit="handleEditTargetUnit"
        />
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>

  <edit-lease-unit ref="editLeaseUnitRef" @refresh="handleEditLeaseUnitRefresh" split-merge-flag></edit-lease-unit>

  <lease-unit-detail ref="leaseUnitDetailRef" readonly />
</template>

<script setup>
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/modules/user'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import EditLeaseUnit from '@/views/leaseUnit/manage/components/EditLeaseUnit.vue'
import {
  submitLeaseUnitSplitMergeBill,
  addLeaseUnitSplitMergeBill,
  editLeaseUnitSplitMergeBill,
  queryLeaseUnitSplitMergeBillEntryOriByMainId,
  queryLeaseUnitSplitMergeBillEntryDestByMainId
} from '../apis'
import { getDepartTreeLazy } from '@/views/system/depart/apis'
import { getUserList } from '@/views/system/user/apis'
import LeaseUnitTransfer from './LeaseUnitTransfer.vue'

const emits = defineEmits(['refresh'])

const store = useUserStore()

const visible = ref(false)
const formRef = ref()
const confirmLoading = ref(false)
const editLeaseUnitRef = ref()
const leaseUnitDetailRef = ref()

const editingTargetIndex = ref(null)

const searchDepartId = ref()

const rules = {
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  operator: [{ required: true, message: '请选择经办人', trigger: 'change' }],
  operatorDepart: [{ required: true, message: '请选择经办部门', trigger: 'change' }],
  splitMergeType: [{ required: true, message: '请选择拆合类型', trigger: 'change' }],
  remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
}

const formDataDefault = {
  id: undefined,
  manageCompany: undefined,
  bizDate: undefined,
  operator: undefined,
  operator_dictText: undefined,
  operatorDepart: undefined,
  splitMergeType: 'Split',
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined,
  delFlag: undefined
}
const formData = reactive({ ...formDataDefault })

const sourceUnits = ref([])
const targetUnits = ref([])

const deptList = ref([])

/**
 * 打开编辑抽屉
 * @param {Object} record - 编辑数据，为空时表示新增
 */
const open = async (record) => {
  // 重置数组
  sourceUnits.value = []
  targetUnits.value = []

  formData.manageCompany = store.userInfo.currentCompany
  searchDepartId.value = formData.manageCompany
  formData.operator = store.userInfo.id
  formData.operatorDepart = store.userInfo.currentDepart
  formData.bizDate = dayjs().format('YYYY-MM-DD')

  if (record) {
    Object.assign(formData, record)
    // 确保日期格式正确
    if (formData.bizDate && typeof formData.bizDate === 'string') {
      formData.bizDate = dayjs(formData.bizDate).format('YYYY-MM-DD')
    }
    try {
      // 查询源租赁单元分录
      const oriRes = await queryLeaseUnitSplitMergeBillEntryOriByMainId({ id: record.id })
      if (oriRes.success && oriRes.result && Array.isArray(oriRes.result)) {
        sourceUnits.value = oriRes.result.map((entry) => {
          // 如果分录包含leaseUnitObject，使用其详细信息；否则使用分录本身
          return entry.leaseUnitObject || entry
        })
      }

      // 查询目标租赁单元分录
      const destRes = await queryLeaseUnitSplitMergeBillEntryDestByMainId({ id: record.id })
      if (destRes.success && destRes.result && Array.isArray(destRes.result)) {
        targetUnits.value = destRes.result.map((entry) => {
          // 如果分录包含leaseUnitObject，使用其详细信息；否则使用分录本身
          return entry.leaseUnitObject || entry
        })
      }
    } catch {
      message.error('加载拆合单元信息失败')
      sourceUnits.value = []
      targetUnits.value = []
    }
  }

  loadDeptList()
  visible.value = true
}

/**
 * 取消编辑并重置表单
 */
const handleCancel = () => {
  visible.value = false
  Object.assign(formData, formDataDefault)
  sourceUnits.value = []
  targetUnits.value = []
  searchDepartId.value = undefined
  deptList.value = []
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  emits('refresh')
}

/**
 * 保存
 */
const handleSave = () => saveData(false)

/**
 * 暂存
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 处理物业管理公司变更
 * @param {string} companyId - 选中的公司ID
 */
const handleManageCompanyChange = (companyId) => {
  searchDepartId.value = companyId
  // 清空经办部门，让用户重新选择
  formData.operatorDepart = undefined
  // 清空经办人，因为经办人依赖于部门
  formData.operator = undefined
  loadDeptList()
}

/**
 * 加载部门列表
 */
const loadDeptList = async () => {
  if (!formData.manageCompany) {
    deptList.value = []
    return
  }
  const { result } = await getDepartTreeLazy({ pid: formData.manageCompany })
  deptList.value = result
}

/**
 * 处理经办人变更
 * @param {string} userId - 选中的经办人ID
 */
const handleOperatorChange = async (userId) => {
  if (!userId) {
    formData.operatorDepart = undefined
    return
  }

  try {
    const { result } = await getUserList({ id: userId })
    if (result?.records?.length > 0) {
      const userInfo = result.records[0]
      formData.operatorDepart = userInfo.currentDepart
    }
  } catch {
    formData.operatorDepart = undefined
  }
}

/**
 * 查看源单元详情
 */
const handleViewSourceUnitDetail = (record) => {
  leaseUnitDetailRef.value.open(record)
}

/**
 * 添加目标单元
 */
const handleAddTargetUnit = () => {
  editingTargetIndex.value = null
  if (sourceUnits.value.length === 0) {
    message.error('请先选择源租赁单元')
    return
  }

  const targetUnit = {
    ...sourceUnits.value[0]
  }
  editLeaseUnitRef.value.open(targetUnit, true)
}

/**
 * 编辑目标单元
 */
const handleEditTargetUnit = (data) => {
  const { record, index } = data
  editingTargetIndex.value = index

  editLeaseUnitRef.value.open(record, true)
}

/**
 * 编辑租赁单元刷新
 */
const handleEditLeaseUnitRefresh = (data) => {
  if (!data) return

  const newTargetUnits = [...targetUnits.value]
  const editingIndex = editingTargetIndex.value

  if (editingIndex !== null && editingIndex >= 0 && editingIndex < newTargetUnits.length) {
    newTargetUnits[editingIndex] = { ...data }
    message.success('目标租赁单元编辑成功')
  } else {
    newTargetUnits.push({ ...data })
    message.success('目标租赁单元添加成功')
  }

  targetUnits.value = newTargetUnits
  editingTargetIndex.value = null
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  if (!isTemporary) {
    await formRef.value.validate()
  }

  confirmLoading.value = true

  if (sourceUnits.value.length === 0) {
    message.error('请至少选择一个源租赁单元')
    confirmLoading.value = false
    return
  }

  if (targetUnits.value.length === 0) {
    message.error('请至少添加一个目标租赁单元')
    confirmLoading.value = false
    return
  }

  // 验证租赁面积
  const sourceLeaseAreaSum = sourceUnits.value.reduce((sum, unit) => {
    return sum + (parseFloat(unit.leaseArea) || 0)
  }, 0)

  const targetLeaseAreaSum = targetUnits.value.reduce((sum, unit) => {
    return sum + (parseFloat(unit.leaseArea) || 0)
  }, 0)

  if (targetLeaseAreaSum < sourceLeaseAreaSum) {
    message.error(
      `目标租赁单元的租赁面积总和(${targetLeaseAreaSum}㎡)不能小于源租赁单元的租赁面积总和(${sourceLeaseAreaSum}㎡)`
    )
    confirmLoading.value = false
    return
  }

  const submitData = {
    ...formData,
    leaseUnitSplitMergeBillEntryOriList: sourceUnits.value.map((unit) => ({
      leaseUnit: unit.id,
      leaseUnitObject: unit,
      houseOwnerObject: unit.houseOwnerObject,
      parent: formData.id
    })),
    leaseUnitSplitMergeBillEntryDestList: targetUnits.value.map((unit) => ({
      leaseUnit: unit.id,
      leaseUnitObject: unit,
      houseOwnerObject: unit.houseOwnerObject,
      parent: formData.id
    }))
  }

  try {
    const api = !isTemporary
      ? submitLeaseUnitSplitMergeBill
      : formData.id
        ? editLeaseUnitSplitMergeBill
        : addLeaseUnitSplitMergeBill

    await api(submitData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '新建'
    message.success(`${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

defineExpose({ open })
</script>

<style scoped lang="less">
.lease-unit-split-merge-edit-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}

.lease-unit-split-merge-edit-drawer :deep(.lease-unit-transfer) {
  margin-bottom: 24px;
}

.lease-unit-split-merge-edit-drawer :deep(.transfer-container) {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.lease-unit-split-merge-edit-drawer :deep(.transfer-list-item:hover) {
  background-color: #f5f5f5;
}
</style>
